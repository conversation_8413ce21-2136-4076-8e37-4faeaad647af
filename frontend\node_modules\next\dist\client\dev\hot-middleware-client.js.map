{"version": 3, "sources": ["../../../src/client/dev/hot-middleware-client.ts"], "names": ["reloading", "mode", "devClient", "connect", "subscribeToHmrEvent", "obj", "isOnErrorPage", "window", "next", "router", "pathname", "action", "sendMessage", "JSON", "stringify", "event", "clientId", "__nextDevClientId", "location", "reload", "page", "data", "components", "Error"], "mappings": ";;;;+BAKA;;;eAAA;;;;uEALoB;2BACQ;AAE5B,IAAIA,YAAY;MAEhB,WAAe,CAACC;IACd,MAAMC,YAAYC,IAAAA,qBAAO,EAACF;IAE1BC,UAAUE,mBAAmB,CAAC,CAACC;QAC7B,IAAIL,WAAW;QACf,wFAAwF;QACxF,uHAAuH;QACvH,MAAMM,gBACJC,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,KAAK,UAChCH,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,KAAK;QAElC,OAAQL,IAAIM,MAAM;YAChB,KAAK;gBAAc;oBACjBC,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPC,UAAUT,OAAOU,iBAAiB;oBACpC;oBAEFjB,YAAY;oBACZ,OAAOO,OAAOW,QAAQ,CAACC,MAAM;gBAC/B;YACA,KAAK;gBAAe;oBAClB,MAAM,CAACC,KAAK,GAAGf,IAAIgB,IAAI;oBACvB,IAAID,SAASb,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,IAAIJ,eAAe;wBACzDM,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUT,OAAOU,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOb,OAAOW,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChB,MAAM,CAACC,KAAK,GAAGf,IAAIgB,IAAI;oBACvB,IACE,AAACD,SAASb,OAAOC,IAAI,CAACC,MAAM,CAACC,QAAQ,IACnC,OAAOH,OAAOC,IAAI,CAACC,MAAM,CAACa,UAAU,CAACF,KAAK,KAAK,eACjDd,eACA;wBACAM,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUT,OAAOU,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOb,OAAOW,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAkB;oBACrB;gBACF;YACA;gBAAS;oBACP,MAAM,IAAII,MAAM,uBAAuBlB,IAAIM,MAAM;gBACnD;QACF;IACF;IAEA,OAAOT;AACT"}