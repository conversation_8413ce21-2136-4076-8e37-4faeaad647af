{"version": 3, "sources": ["../../src/lib/recursive-readdir.ts"], "names": ["recursiveReadDir", "rootDirectory", "options", "pathnameFilter", "ignoreFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sortPathnames", "relativePathnames", "pathnames", "coerce", "pathname", "replace", "directories", "length", "results", "Promise", "all", "map", "directory", "result", "links", "dir", "fs", "readdir", "withFileTypes", "file", "name", "absolutePathname", "path", "join", "isDirectory", "push", "isSymbolicLink", "err", "code", "resolved", "stat", "i", "stats", "sort"], "mappings": ";;;;+BA+CsBA;;;eAAAA;;;iEA/CP;6DACE;;;;;;AA8CV,eAAeA,iBACpBC,aAAqB,EACrBC,UAAmC,CAAC,CAAC;IAErC,oBAAoB;IACpB,MAAM,EACJC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,IAAI,EACpBC,oBAAoB,IAAI,EACzB,GAAGL;IAEJ,mCAAmC;IACnC,MAAMM,YAAsB,EAAE;IAE9B;;GAEC,GACD,MAAMC,SAASF,oBACX,CAACG,WAAqBA,SAASC,OAAO,CAACV,eAAe,MACtD,CAACS,WAAqBA;IAE1B,oCAAoC;IACpC,IAAIE,cAAwB;QAACX;KAAc;IAE3C,MAAOW,YAAYC,MAAM,GAAG,EAAG;QAC7B,yDAAyD;QACzD,MAAMC,UAAU,MAAMC,QAAQC,GAAG,CAC/BJ,YAAYK,GAAG,CAAC,OAAOC;YACrB,MAAMC,SAAiB;gBAAEP,aAAa,EAAE;gBAAEJ,WAAW,EAAE;gBAAEY,OAAO,EAAE;YAAC;YAEnE,IAAI;gBACF,MAAMC,MAAM,MAAMC,iBAAE,CAACC,OAAO,CAACL,WAAW;oBAAEM,eAAe;gBAAK;gBAC9D,KAAK,MAAMC,QAAQJ,IAAK;oBACtB,+DAA+D;oBAC/D,IAAIhB,oBAAoBA,iBAAiBoB,KAAKC,IAAI,GAAG;wBACnD;oBACF;oBAEA,oBAAoB;oBACpB,MAAMC,mBAAmBC,aAAI,CAACC,IAAI,CAACX,WAAWO,KAAKC,IAAI;oBAEvD,+DAA+D;oBAC/D,IAAItB,gBAAgBA,aAAauB,mBAAmB;wBAClD;oBACF;oBAEA,sEAAsE;oBACtE,sCAAsC;oBACtC,IAAIF,KAAKK,WAAW,IAAI;wBACtBX,OAAOP,WAAW,CAACmB,IAAI,CAACJ;oBAC1B,OAAO,IAAIF,KAAKO,cAAc,IAAI;wBAChCb,OAAOC,KAAK,CAACW,IAAI,CAACJ;oBACpB,OAAO,IAAI,CAACxB,kBAAkBA,eAAewB,mBAAmB;wBAC9DR,OAAOX,SAAS,CAACuB,IAAI,CAACtB,OAAOkB;oBAC/B;gBACF;YACF,EAAE,OAAOM,KAAU;gBACjB,qEAAqE;gBACrE,sDAAsD;gBACtD,uCAAuC;gBACvC,IAAIA,IAAIC,IAAI,KAAK,YAAYhB,cAAcjB,eAAe,MAAMgC;gBAEhE,yDAAyD;gBACzD,OAAO;YACT;YAEA,OAAOd;QACT;QAGF,sEAAsE;QACtE,eAAe;QACfP,cAAc,EAAE;QAEhB,sEAAsE;QACtE,MAAMQ,QAAQ,EAAE;QAEhB,wCAAwC;QACxC,KAAK,MAAMD,UAAUL,QAAS;YAC5B,8CAA8C;YAC9C,IAAI,CAACK,QAAQ;YAEb,0DAA0D;YAC1DP,YAAYmB,IAAI,IAAIZ,OAAOP,WAAW;YAEtC,mEAAmE;YACnEQ,MAAMW,IAAI,IAAIZ,OAAOC,KAAK;YAE1B,mDAAmD;YACnDZ,UAAUuB,IAAI,IAAIZ,OAAOX,SAAS;QACpC;QAEA,kDAAkD;QAClD,IAAIY,MAAMP,MAAM,GAAG,GAAG;YACpB,MAAMsB,WAAW,MAAMpB,QAAQC,GAAG,CAChCI,MAAMH,GAAG,CAAC,OAAOU;gBACf,IAAI;oBACF,OAAO,MAAML,iBAAE,CAACc,IAAI,CAACT;gBACvB,EAAE,OAAOM,KAAU;oBACjB,gEAAgE;oBAChE,sDAAsD;oBACtD,IAAIA,IAAIC,IAAI,KAAK,UAAU,MAAMD;oBAEjC,yDAAyD;oBACzD,OAAO;gBACT;YACF;YAGF,IAAK,IAAII,IAAI,GAAGA,IAAIjB,MAAMP,MAAM,EAAEwB,IAAK;gBACrC,MAAMC,QAAQH,QAAQ,CAACE,EAAE;gBAEzB,yCAAyC;gBACzC,IAAI,CAACC,OAAO;gBAEZ,kEAAkE;gBAClE,8CAA8C;gBAC9C,MAAMX,mBAAmBP,KAAK,CAACiB,EAAE;gBAEjC,IAAIC,MAAMR,WAAW,IAAI;oBACvBlB,YAAYmB,IAAI,CAACJ;gBACnB,OAAO,IAAI,CAACxB,kBAAkBA,eAAewB,mBAAmB;oBAC9DnB,UAAUuB,IAAI,CAACtB,OAAOkB;gBACxB;YACF;QACF;IACF;IAEA,4CAA4C;IAC5C,IAAIrB,eAAe;QACjBE,UAAU+B,IAAI;IAChB;IAEA,OAAO/B;AACT"}