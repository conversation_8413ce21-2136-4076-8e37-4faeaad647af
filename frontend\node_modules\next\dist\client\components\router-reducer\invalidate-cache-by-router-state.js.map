{"version": 3, "sources": ["../../../../src/client/components/router-reducer/invalidate-cache-by-router-state.ts"], "names": ["invalidateCacheByRouterState", "newCache", "existingCache", "routerState", "key", "segmentForParallelRoute", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "existingParallelRoutesCacheNode", "parallelRoutes", "get", "parallelRouteCacheNode", "Map", "delete", "set"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;sCALqB;AAK9B,SAASA,6BACdC,QAAmB,EACnBC,aAAwB,EACxBC,WAA8B;IAE9B,+FAA+F;IAC/F,IAAK,MAAMC,OAAOD,WAAW,CAAC,EAAE,CAAE;QAChC,MAAME,0BAA0BF,WAAW,CAAC,EAAE,CAACC,IAAI,CAAC,EAAE;QACtD,MAAME,WAAWC,IAAAA,0CAAoB,EAACF;QACtC,MAAMG,kCACJN,cAAcO,cAAc,CAACC,GAAG,CAACN;QACnC,IAAII,iCAAiC;YACnC,IAAIG,yBAAyB,IAAIC,IAAIJ;YACrCG,uBAAuBE,MAAM,CAACP;YAC9BL,SAASQ,cAAc,CAACK,GAAG,CAACV,KAAKO;QACnC;IACF;AACF"}