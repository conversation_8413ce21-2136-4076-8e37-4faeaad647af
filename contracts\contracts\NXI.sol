// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title NXI Token
 * @dev Enhanced ERC20 token designed for Nexus ecosystem with NEX integration
 * Features:
 * - Burnable tokens for deflationary mechanics
 * - Pausable for emergency stops
 * - Owner controls for advanced features
 * - NEX token interaction capabilities
 * - 18 decimals (standard)
 * - Initial supply of 1,000,000 tokens
 * - Anti-whale mechanisms
 */
contract NXI is ERC20, ERC20Burnable, Ownable, Pausable, ReentrancyGuard {
    uint256 public constant INITIAL_SUPPLY = 1_000_000 * 10**18; // 1M tokens
    uint256 public constant MAX_SUPPLY = 10_000_000 * 10**18;    // 10M tokens max
    
    // NEX integration
    uint256 public nexSwapRate = 100; // 1 NEX = 100 NXI (adjustable)
    bool public nexSwapEnabled = true;
    
    // Anti-whale mechanism
    uint256 public maxTransferAmount = 50_000 * 10**18; // 50k tokens max per transfer
    mapping(address => bool) public whitelistedAddresses;
    
    // Events
    event TokensMinted(address indexed to, uint256 amount);
    event ContractPaused(address indexed by);
    event ContractUnpaused(address indexed by);
    event NEXSwapped(address indexed user, uint256 nexAmount, uint256 nxiAmount);
    event NEXSwapRateUpdated(uint256 oldRate, uint256 newRate);
    event MaxTransferUpdated(uint256 oldMax, uint256 newMax);
    event AddressWhitelisted(address indexed account, bool whitelisted);

    /**
     * @dev Constructor that mints initial supply to the deployer
     * Sets up the token with enhanced features for Nexus DeFi operations
     */
    constructor() ERC20("Nexus Index Token", "NXI") Ownable(msg.sender) {
        _mint(msg.sender, INITIAL_SUPPLY);
        
        // Whitelist deployer and common DeFi contracts
        whitelistedAddresses[msg.sender] = true;
        whitelistedAddresses[address(0)] = true; // For burns
        
        emit TokensMinted(msg.sender, INITIAL_SUPPLY);
    }

    /**
     * @dev Swap NEX for NXI tokens
     * Users can send NEX and receive NXI at the current swap rate
     */
    function swapNEXForNXI() external payable nonReentrant whenNotPaused {
        require(nexSwapEnabled, "NEX swap is disabled");
        require(msg.value > 0, "Must send NEX to swap");
        
        uint256 nxiAmount = msg.value * nexSwapRate;
        require(totalSupply() + nxiAmount <= MAX_SUPPLY, "Would exceed max supply");
        
        _mint(msg.sender, nxiAmount);
        
        emit NEXSwapped(msg.sender, msg.value, nxiAmount);
        emit TokensMinted(msg.sender, nxiAmount);
    }

    /**
     * @dev Mint new tokens (only owner, respects max supply)
     * @param to Address to mint tokens to
     * @param amount Amount of tokens to mint
     */
    function mint(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Cannot mint to zero address");
        require(totalSupply() + amount <= MAX_SUPPLY, "Would exceed max supply");

        _mint(to, amount);
        emit TokensMinted(to, amount);
    }

    /**
     * @dev Set NEX to NXI swap rate (only owner)
     * @param newRate New swap rate (1 NEX = newRate NXI)
     */
    function setNEXSwapRate(uint256 newRate) external onlyOwner {
        require(newRate > 0, "Swap rate must be positive");
        uint256 oldRate = nexSwapRate;
        nexSwapRate = newRate;
        emit NEXSwapRateUpdated(oldRate, newRate);
    }

    /**
     * @dev Enable/disable NEX swapping (only owner)
     * @param enabled Whether NEX swapping should be enabled
     */
    function setNEXSwapEnabled(bool enabled) external onlyOwner {
        nexSwapEnabled = enabled;
    }

    /**
     * @dev Set maximum transfer amount (only owner)
     * @param newMax New maximum transfer amount
     */
    function setMaxTransferAmount(uint256 newMax) external onlyOwner {
        uint256 oldMax = maxTransferAmount;
        maxTransferAmount = newMax;
        emit MaxTransferUpdated(oldMax, newMax);
    }

    /**
     * @dev Whitelist address to bypass transfer limits (only owner)
     * @param account Address to whitelist
     * @param whitelisted Whether address should be whitelisted
     */
    function setWhitelistAddress(address account, bool whitelisted) external onlyOwner {
        whitelistedAddresses[account] = whitelisted;
        emit AddressWhitelisted(account, whitelisted);
    }

    /**
     * @dev Pause all token transfers (emergency function)
     */
    function pause() external onlyOwner {
        _pause();
        emit ContractPaused(msg.sender);
    }

    /**
     * @dev Unpause all token transfers
     */
    function unpause() external onlyOwner {
        _unpause();
        emit ContractUnpaused(msg.sender);
    }

    /**
     * @dev Withdraw NEX from contract (only owner)
     * @param to Address to send NEX to
     * @param amount Amount of NEX to withdraw
     */
    function withdrawNEX(address payable to, uint256 amount) external onlyOwner {
        require(to != address(0), "Cannot withdraw to zero address");
        require(amount <= address(this).balance, "Insufficient NEX balance");
        
        to.transfer(amount);
    }

    /**
     * @dev Override transfer to respect pause state and anti-whale limits
     */
    function _update(address from, address to, uint256 value) internal override whenNotPaused {
        // Check anti-whale limits (skip for whitelisted addresses)
        if (!whitelistedAddresses[from] && !whitelistedAddresses[to] && from != address(0)) {
            require(value <= maxTransferAmount, "Transfer amount exceeds maximum");
        }
        
        super._update(from, to, value);
    }

    /**
     * @dev Returns comprehensive token information for frontend integration
     */
    function getTokenInfo() external view returns (
        string memory tokenName,
        string memory tokenSymbol,
        uint8 tokenDecimals,
        uint256 currentSupply,
        uint256 maxSupply,
        uint256 nexRate,
        bool nexSwapActive,
        uint256 maxTransfer,
        bool isPaused,
        uint256 nexBalance
    ) {
        return (
            name(),
            symbol(),
            decimals(),
            totalSupply(),
            MAX_SUPPLY,
            nexSwapRate,
            nexSwapEnabled,
            maxTransferAmount,
            paused(),
            address(this).balance
        );
    }

    /**
     * @dev Calculate NXI amount for given NEX amount
     * @param nexAmount Amount of NEX to swap
     * @return Amount of NXI that would be received
     */
    function calculateNXIForNEX(uint256 nexAmount) external view returns (uint256) {
        return nexAmount * nexSwapRate;
    }

    /**
     * @dev Check if address is whitelisted
     * @param account Address to check
     * @return True if address is whitelisted
     */
    function isWhitelisted(address account) external view returns (bool) {
        return whitelistedAddresses[account];
    }

    /**
     * @dev Receive function to handle direct NEX transfers
     */
    receive() external payable {
        if (msg.value > 0 && nexSwapEnabled && !paused()) {
            uint256 nxiAmount = msg.value * nexSwapRate;
            if (totalSupply() + nxiAmount <= MAX_SUPPLY) {
                _mint(msg.sender, nxiAmount);
                emit NEXSwapped(msg.sender, msg.value, nxiAmount);
                emit TokensMinted(msg.sender, nxiAmount);
            }
        }
    }
}