{"name": "nexus-swap-contracts", "version": "1.0.0", "description": "Smart contracts for Nexus token swap DeFi application", "main": "index.js", "scripts": {"compile": "hardhat compile", "deploy": "hardhat run scripts/deploy.js --network nexus", "deploy:local": "hardhat run scripts/deploy.js --network localhost", "verify": "hardhat run scripts/verify.js --network nexus", "transfer": "hardhat run scripts/transfer-tokens.js --network nexus", "test": "hardhat test", "coverage": "hardhat coverage", "clean": "hardhat clean"}, "keywords": ["defi", "nexus", "token-swap", "ethereum", "hardhat", "solidity"], "author": "", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "hardhat": "^2.19.0"}, "dependencies": {"@openzeppelin/contracts": "^5.4.0", "dotenv": "^16.3.1"}}