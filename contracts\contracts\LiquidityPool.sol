// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title LiquidityPool
 * @dev Advanced liquidity pool with LP tokens, fees, and multiple interaction methods
 * Designed to generate maximum transactions for Nexus testnet
 */
contract LiquidityPool is ERC20, ReentrancyGuard, Ownable {
    IERC20 public tokenA;
    IERC20 public tokenB;
    
    uint256 public constant FEE_RATE = 30; // 0.3% fee (30/10000)
    uint256 public constant FEE_DENOMINATOR = 10000;
    
    uint256 public totalFeesA;
    uint256 public totalFeesB;
    
    // Events for maximum transaction tracking
    event LiquidityAdded(address indexed provider, uint256 amountA, uint256 amountB, uint256 liquidity);
    event LiquidityRemoved(address indexed provider, uint256 amountA, uint256 amountB, uint256 liquidity);
    event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut);
    event FeesCollected(address indexed collector, uint256 feesA, uint256 feesB);
    
    constructor(
        address _tokenA,
        address _tokenB,
        string memory _name,
        string memory _symbol
    ) ERC20(_name, _symbol) Ownable(msg.sender) {
        tokenA = IERC20(_tokenA);
        tokenB = IERC20(_tokenB);
    }
    
    /**
     * @dev Add liquidity to the pool
     * Generates multiple transactions: approve + addLiquidity
     */
    function addLiquidity(
        uint256 amountA,
        uint256 amountB,
        uint256 minAmountA,
        uint256 minAmountB
    ) external nonReentrant returns (uint256 liquidity) {
        require(amountA > 0 && amountB > 0, "Amounts must be greater than 0");
        
        uint256 reserveA = tokenA.balanceOf(address(this)) - totalFeesA;
        uint256 reserveB = tokenB.balanceOf(address(this)) - totalFeesB;
        
        if (totalSupply() == 0) {
            // First liquidity provision
            liquidity = sqrt(amountA * amountB);
            require(liquidity > 0, "Insufficient liquidity minted");
        } else {
            // Calculate optimal amounts
            uint256 amountBOptimal = (amountA * reserveB) / reserveA;
            if (amountBOptimal <= amountB) {
                require(amountBOptimal >= minAmountB, "Insufficient B amount");
                amountB = amountBOptimal;
            } else {
                uint256 amountAOptimal = (amountB * reserveA) / reserveB;
                require(amountAOptimal <= amountA && amountAOptimal >= minAmountA, "Insufficient A amount");
                amountA = amountAOptimal;
            }
            
            liquidity = min((amountA * totalSupply()) / reserveA, (amountB * totalSupply()) / reserveB);
        }
        
        require(liquidity > 0, "Insufficient liquidity minted");
        
        // Transfer tokens
        require(tokenA.transferFrom(msg.sender, address(this), amountA), "Transfer A failed");
        require(tokenB.transferFrom(msg.sender, address(this), amountB), "Transfer B failed");
        
        // Mint LP tokens
        _mint(msg.sender, liquidity);
        
        emit LiquidityAdded(msg.sender, amountA, amountB, liquidity);
    }
    
    /**
     * @dev Remove liquidity from the pool
     * Generates transaction: removeLiquidity
     */
    function removeLiquidity(
        uint256 liquidity,
        uint256 minAmountA,
        uint256 minAmountB
    ) external nonReentrant returns (uint256 amountA, uint256 amountB) {
        require(liquidity > 0, "Insufficient liquidity");
        require(balanceOf(msg.sender) >= liquidity, "Insufficient LP tokens");
        
        uint256 reserveA = tokenA.balanceOf(address(this)) - totalFeesA;
        uint256 reserveB = tokenB.balanceOf(address(this)) - totalFeesB;
        
        amountA = (liquidity * reserveA) / totalSupply();
        amountB = (liquidity * reserveB) / totalSupply();
        
        require(amountA >= minAmountA && amountB >= minAmountB, "Insufficient output amounts");
        
        // Burn LP tokens
        _burn(msg.sender, liquidity);
        
        // Transfer tokens back
        require(tokenA.transfer(msg.sender, amountA), "Transfer A failed");
        require(tokenB.transfer(msg.sender, amountB), "Transfer B failed");
        
        emit LiquidityRemoved(msg.sender, amountA, amountB, liquidity);
    }
    
    /**
     * @dev Swap tokens with fee collection
     * Generates transaction: swap
     */
    function swap(
        address tokenIn,
        uint256 amountIn,
        uint256 minAmountOut
    ) external nonReentrant returns (uint256 amountOut) {
        require(amountIn > 0, "Amount must be greater than 0");
        require(tokenIn == address(tokenA) || tokenIn == address(tokenB), "Invalid token");
        
        bool isTokenA = tokenIn == address(tokenA);
        IERC20 inputToken = isTokenA ? tokenA : tokenB;
        IERC20 outputToken = isTokenA ? tokenB : tokenA;
        
        uint256 reserveIn = inputToken.balanceOf(address(this));
        uint256 reserveOut = outputToken.balanceOf(address(this));
        
        if (isTokenA) {
            reserveIn -= totalFeesA;
            reserveOut -= totalFeesB;
        } else {
            reserveIn -= totalFeesB;
            reserveOut -= totalFeesA;
        }
        
        // Calculate output with fee
        uint256 amountInWithFee = amountIn * (FEE_DENOMINATOR - FEE_RATE);
        uint256 numerator = amountInWithFee * reserveOut;
        uint256 denominator = (reserveIn * FEE_DENOMINATOR) + amountInWithFee;
        amountOut = numerator / denominator;
        
        require(amountOut >= minAmountOut, "Insufficient output amount");
        require(amountOut < reserveOut, "Insufficient liquidity");
        
        // Calculate and store fees
        uint256 fee = (amountIn * FEE_RATE) / FEE_DENOMINATOR;
        if (isTokenA) {
            totalFeesA += fee;
        } else {
            totalFeesB += fee;
        }
        
        // Execute transfers
        require(inputToken.transferFrom(msg.sender, address(this), amountIn), "Transfer in failed");
        require(outputToken.transfer(msg.sender, amountOut), "Transfer out failed");
        
        emit Swap(msg.sender, tokenIn, address(outputToken), amountIn, amountOut);
    }
    
    /**
     * @dev Collect accumulated fees (owner only)
     * Generates transaction: collectFees
     */
    function collectFees() external onlyOwner {
        uint256 feesA = totalFeesA;
        uint256 feesB = totalFeesB;
        
        if (feesA > 0) {
            totalFeesA = 0;
            require(tokenA.transfer(owner(), feesA), "Fee transfer A failed");
        }
        
        if (feesB > 0) {
            totalFeesB = 0;
            require(tokenB.transfer(owner(), feesB), "Fee transfer B failed");
        }
        
        emit FeesCollected(owner(), feesA, feesB);
    }
    
    /**
     * @dev Get current reserves (excluding fees)
     */
    function getReserves() external view returns (uint256 reserveA, uint256 reserveB) {
        reserveA = tokenA.balanceOf(address(this)) - totalFeesA;
        reserveB = tokenB.balanceOf(address(this)) - totalFeesB;
    }
    
    /**
     * @dev Calculate swap output amount
     */
    function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256 amountOut) {
        require(tokenIn == address(tokenA) || tokenIn == address(tokenB), "Invalid token");
        
        bool isTokenA = tokenIn == address(tokenA);
        uint256 reserveIn = isTokenA ? 
            tokenA.balanceOf(address(this)) - totalFeesA : 
            tokenB.balanceOf(address(this)) - totalFeesB;
        uint256 reserveOut = isTokenA ? 
            tokenB.balanceOf(address(this)) - totalFeesB : 
            tokenA.balanceOf(address(this)) - totalFeesA;
        
        uint256 amountInWithFee = amountIn * (FEE_DENOMINATOR - FEE_RATE);
        uint256 numerator = amountInWithFee * reserveOut;
        uint256 denominator = (reserveIn * FEE_DENOMINATOR) + amountInWithFee;
        amountOut = numerator / denominator;
    }
    
    // Helper functions
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }
    
    function min(uint256 a, uint256 b) internal pure returns (uint256) {
        return a < b ? a : b;
    }
}
