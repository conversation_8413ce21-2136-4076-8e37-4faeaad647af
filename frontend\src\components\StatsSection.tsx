import React, { useState, useEffect } from 'react';

interface StatsData {
  totalValueLocked: string;
  volume24h: string;
  totalTrades: string;
  totalUsers: string;
}

const StatsSection: React.FC = () => {
  const [stats, setStats] = useState<StatsData>({
    totalValueLocked: '0',
    volume24h: '0',
    totalTrades: '0',
    totalUsers: '0'
  });

  const [loading, setLoading] = useState(true);
  const [randomValues, setRandomValues] = useState<number[]>([]);

  useEffect(() => {
    // Generate random values on client mount
    setRandomValues([
      Math.random() * 1000000,
      Math.random() * 1000000,
      Math.random() * 1000000
    ]);
    
    // Simulate loading stats data
    const timer = setTimeout(() => {
      setStats({
        totalValueLocked: '12.5M',
        volume24h: '2.8M',
        totalTrades: '45,231',
        totalUsers: '8,492'
      });
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const formatNumber = (num: string) => {
    if (loading) return '...';
    return num;
  };

  const statsItems = [
    {
      label: 'Total Value Locked',
      value: stats.totalValueLocked,
      prefix: '$',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      )
    },
    {
      label: '24h Volume',
      value: stats.volume24h,
      prefix: '$',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      )
    },
    {
      label: 'Total Trades',
      value: stats.totalTrades,
      prefix: '',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
        </svg>
      )
    },
    {
      label: 'Total Users',
      value: stats.totalUsers,
      prefix: '',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      )
    }
  ];

  return (
    <section className="py-16 bg-slate-900/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">
            Platform Statistics
          </h2>
          <p className="text-slate-400 max-w-2xl mx-auto">
            Real-time metrics showcasing the growth and activity of the Nexus DEX ecosystem
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsItems.map((item, index) => (
            <div
              key={index}
              className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6 hover:border-nexus-400/50 transition-all duration-300 group"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="p-2 bg-nexus-400/10 rounded-lg text-nexus-400 group-hover:bg-nexus-400/20 transition-colors">
                  {item.icon}
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-white">
                    {loading ? (
                      <div className="animate-pulse bg-slate-700 h-8 w-16 rounded"></div>
                    ) : (
                      <span>
                        {item.prefix}{formatNumber(item.value)}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className="text-slate-400 text-sm font-medium">
                {item.label}
              </div>
              {!loading && (
                <div className="mt-2 flex items-center text-xs text-green-400">
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  +12.5% from last week
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Additional Metrics */}
        <div className="mt-12 grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
            <h3 className="text-white font-semibold mb-4">Top Trading Pairs</h3>
            <div className="space-y-3">
              {['ETH/USDC', 'WBTC/ETH', 'USDT/USDC'].map((pair, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-slate-300">{pair}</span>
                  <span className="text-nexus-400 font-medium">
                    ${randomValues[index] ? randomValues[index].toFixed(0) : '0'}K
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
            <h3 className="text-white font-semibold mb-4">Network Status</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-slate-300">Gas Price</span>
                <span className="text-green-400 font-medium">25 gwei</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-300">Block Time</span>
                <span className="text-green-400 font-medium">12.5s</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-300">Network</span>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                  <span className="text-green-400 font-medium">Healthy</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
            <h3 className="text-white font-semibold mb-4">Recent Activity</h3>
            <div className="space-y-3">
              <div className="text-sm">
                <div className="text-slate-300">Latest Swap</div>
                <div className="text-nexus-400">1.5 ETH → 2,847 USDC</div>
              </div>
              <div className="text-sm">
                <div className="text-slate-300">Latest LP Addition</div>
                <div className="text-nexus-400">ETH/USDC Pool</div>
              </div>
              <div className="text-sm">
                <div className="text-slate-300">Latest Stake</div>
                <div className="text-nexus-400">500 NEXUS tokens</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StatsSection;