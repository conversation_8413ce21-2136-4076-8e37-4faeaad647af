"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Contract ABIs - Complete for all DeFi operations\nconst TokenA_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) external returns (bool)\",\n    \"function allowance(address owner, address spender) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) external returns (bool)\",\n    \"function transferFrom(address from, address to, uint256 amount) external returns (bool)\"\n];\nconst TokenB_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\"\n];\nconst TokenSwap_ABI = [\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getTokenBLiquidity() external view returns (uint256)\",\n    \"function getTokenABalance() external view returns (uint256)\",\n    \"function tokenA() external view returns (address)\",\n    \"function tokenB() external view returns (address)\",\n    \"event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut)\"\n];\nconst LiquidityPool_ABI = [\n    \"function addLiquidity(uint256 amountA, uint256 amountB) external returns (uint256)\",\n    \"function removeLiquidity(uint256 liquidity) external returns (uint256, uint256)\",\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getReserves() external view returns (uint256, uint256)\",\n    \"function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256)\",\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function name() external view returns (string)\"\n];\nconst Staking_ABI = [\n    \"function stake(uint256 amount) external\",\n    \"function withdraw(uint256 amount) external\",\n    \"function claimReward() external\",\n    \"function exit() external\",\n    \"function compound() external\",\n    \"function multiStake(uint256[] amounts) external\",\n    \"function multiWithdraw(uint256[] amounts) external\",\n    \"function multiClaim(uint256 times) external\",\n    \"function earned(address account) external view returns (uint256)\",\n    \"function balances(address account) external view returns (uint256)\",\n    \"function getStakingInfo(address user) external view returns (uint256, uint256, uint256, uint256)\"\n];\n// Nexus network configuration\nconst NEXUS_NETWORK = {\n    chainId: \"0xF64\",\n    chainName: \"Nexus Testnet III\",\n    nativeCurrency: {\n        name: \"NEX\",\n        symbol: \"NEX\",\n        decimals: 18\n    },\n    rpcUrls: [\n        \"https://testnet3.rpc.nexus.xyz\"\n    ],\n    blockExplorerUrls: [\n        \"https://explorer.nexus.xyz\"\n    ]\n};\n// Default contract addresses (will be replaced with deployed addresses)\nconst DEFAULT_ADDRESSES = {\n    TokenA: \"0x83FD4F92926AC4FB7a0b510aD5615Fe76e588cF8\",\n    TokenB: \"0x67bae4B1E5528Fb6C92E7E0BC243341dc71330db\",\n    TokenSwap: \"0xf18529580694Ff80e836132875a52505124358EC\"\n};\nfunction Home() {\n    var _contractAddresses_deployer;\n    _s();\n    // Basic states\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenABalance, setTokenABalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [tokenBBalance, setTokenBBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [lpTokenBalance, setLpTokenBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [stakedBalance, setStakedBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [earnedRewards, setEarnedRewards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_ADDRESSES);\n    const [liquidity, setLiquidity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [isCorrectNetwork, setIsCorrectNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab and form states\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"swap\");\n    const [swapAmount, setSwapAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountA, setLiquidityAmountA] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountB, setLiquidityAmountB] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [stakeAmount, setStakeAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Token selection states\n    const [selectedTokenIn, setSelectedTokenIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenA\");\n    const [selectedTokenOut, setSelectedTokenOut] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenB\");\n    const [availableTokens, setAvailableTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkIfWalletIsConnected();\n        loadContractAddresses();\n        setupEventListeners();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (account && isCorrectNetwork) {\n            updateBalances();\n            updateLiquidity();\n            loadAvailableTokens();\n        }\n    }, [\n        account,\n        contractAddresses,\n        isCorrectNetwork\n    ]);\n    async function loadAvailableTokens() {\n        if (!account || !isCorrectNetwork) return;\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const tokens = [\n                {\n                    symbol: \"TKNA\",\n                    name: \"Token A\",\n                    address: contractAddresses.TokenA,\n                    balance: tokenABalance,\n                    decimals: 18\n                },\n                {\n                    symbol: \"TKNB\",\n                    name: \"Token B\",\n                    address: contractAddresses.TokenB,\n                    balance: tokenBBalance,\n                    decimals: 18\n                }\n            ];\n            // Add LP token if available\n            if (contractAddresses.LiquidityPool) {\n                tokens.push({\n                    symbol: \"NLP\",\n                    name: \"Nexus LP Token\",\n                    address: contractAddresses.LiquidityPool,\n                    balance: lpTokenBalance,\n                    decimals: 18\n                });\n            }\n            setAvailableTokens(tokens);\n        } catch (error) {\n            console.error(\"Error loading available tokens:\", error);\n        }\n    }\n    function setupEventListeners() {\n        const { ethereum } = window;\n        if (!ethereum) return;\n        // Listen for account changes\n        ethereum.on(\"accountsChanged\", (accounts)=>{\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                checkNetwork();\n            } else {\n                setAccount(\"\");\n                setIsCorrectNetwork(false);\n            }\n        });\n        // Listen for network changes\n        ethereum.on(\"chainChanged\", (chainId)=>{\n            console.log(\"Network changed to:\", chainId);\n            checkNetwork();\n            // Reload page to reset state\n            window.location.reload();\n        });\n        // Cleanup function\n        return ()=>{\n            if (ethereum.removeListener) {\n                ethereum.removeListener(\"accountsChanged\", ()=>{});\n                ethereum.removeListener(\"chainChanged\", ()=>{});\n            }\n        };\n    }\n    async function loadContractAddresses() {\n        try {\n            // Try to load deployed addresses from public folder\n            const response = await fetch(\"/deployedAddresses.json\");\n            if (response.ok) {\n                const addresses = await response.json();\n                setContractAddresses(addresses);\n                console.log(\"Loaded contract addresses:\", addresses);\n            } else {\n                console.warn(\"Could not load deployed addresses, using defaults\");\n            }\n        } catch (error) {\n            console.warn(\"Could not load deployed addresses:\", error);\n        }\n    }\n    async function checkIfWalletIsConnected() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_accounts\"\n            });\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                await checkNetwork();\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setError(\"Error saat mengecek koneksi wallet\");\n        }\n    }\n    async function checkNetwork() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setIsCorrectNetwork(false);\n                return;\n            }\n            const chainId = await ethereum.request({\n                method: \"eth_chainId\"\n            });\n            console.log(\"Current chainId:\", chainId, \"Expected:\", NEXUS_NETWORK.chainId);\n            // Convert both to same format for comparison\n            const currentChainId = parseInt(chainId, 16);\n            const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);\n            if (currentChainId === expectedChainId) {\n                setIsCorrectNetwork(true);\n                setError(\"\");\n                console.log(\"✅ Connected to correct network\");\n            } else {\n                setIsCorrectNetwork(false);\n                setError(\"Wrong network. Current: \".concat(currentChainId, \", Expected: \").concat(expectedChainId, \" (Nexus Testnet III)\"));\n                console.log(\"❌ Wrong network detected\");\n            }\n        } catch (error) {\n            console.error(\"Error checking network:\", error);\n            setIsCorrectNetwork(false);\n            setError(\"Error checking network\");\n        }\n    }\n    async function switchToNexusNetwork() {\n        try {\n            const { ethereum } = window;\n            setError(\"\");\n            setSuccess(\"Switching to Nexus network...\");\n            try {\n                await ethereum.request({\n                    method: \"wallet_switchEthereumChain\",\n                    params: [\n                        {\n                            chainId: NEXUS_NETWORK.chainId\n                        }\n                    ]\n                });\n                console.log(\"✅ Network switch requested\");\n            } catch (switchError) {\n                console.log(\"Switch error code:\", switchError.code);\n                // Network belum ditambahkan, tambahkan dulu\n                if (switchError.code === 4902) {\n                    console.log(\"Adding Nexus network...\");\n                    await ethereum.request({\n                        method: \"wallet_addEthereumChain\",\n                        params: [\n                            NEXUS_NETWORK\n                        ]\n                    });\n                    console.log(\"✅ Network added\");\n                } else {\n                    throw switchError;\n                }\n            }\n            // Wait a bit for network to switch\n            setTimeout(async ()=>{\n                await checkNetwork();\n                setSuccess(\"\");\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error switching network:\", error);\n            setError(\"Gagal switch ke Nexus network: \" + (error.message || \"Unknown error\"));\n            setSuccess(\"\");\n        }\n    }\n    async function connectWallet() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            setAccount(accounts[0]);\n            await checkNetwork();\n            setError(\"\");\n            setSuccess(\"Wallet berhasil terhubung!\");\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n            setError(\"Gagal menghubungkan wallet\");\n        }\n    }\n    async function updateBalances() {\n        if (!account || !isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {\n            console.log(\"Contract addresses not loaded yet, skipping balance update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating all balances for:\", account);\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, provider);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, provider);\n            // Get basic token balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            setTokenABalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceA));\n            setTokenBBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceB));\n            // Get LP token balance if LiquidityPool exists\n            if (contractAddresses.LiquidityPool) {\n                const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, provider);\n                const lpBalance = await lpContract.balanceOf(account);\n                setLpTokenBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(lpBalance));\n            }\n            // Get staking info if Staking exists\n            if (contractAddresses.Staking) {\n                const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, provider);\n                const [stakedBal, earned] = await Promise.all([\n                    stakingContract.balances(account),\n                    stakingContract.earned(account)\n                ]);\n                setStakedBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(stakedBal));\n                setEarnedRewards(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(earned));\n            }\n            console.log(\"All balances updated successfully\");\n        } catch (error) {\n            console.error(\"Error updating balances:\", error);\n            setError(\"Error connecting to contracts\");\n        }\n    }\n    async function updateLiquidity() {\n        if (!isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {\n            console.log(\"TokenSwap address not loaded yet, skipping liquidity update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating liquidity for TokenSwap:\", contractAddresses.TokenSwap);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);\n            try {\n                const liquidityAmount = await swapContract.getTokenBLiquidity();\n                console.log(\"Raw liquidity:\", liquidityAmount.toString());\n                setLiquidity(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(liquidityAmount));\n                console.log(\"Liquidity updated successfully\");\n            } catch (contractError) {\n                console.error(\"TokenSwap contract call error:\", contractError);\n                setError(\"Error reading liquidity. TokenSwap contract may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating liquidity:\", error);\n            setError(\"Error connecting to TokenSwap contract\");\n        }\n    }\n    async function handleSwap() {\n        if (!swapAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            // Determine which tokens to use\n            const isTokenAToB = selectedTokenIn === \"TokenA\";\n            const inputTokenAddress = isTokenAToB ? contractAddresses.TokenA : contractAddresses.TokenB;\n            const inputTokenContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(inputTokenAddress, TokenA_ABI, signer);\n            // Use LiquidityPool for swapping (more advanced than TokenSwap)\n            const swapContract = contractAddresses.LiquidityPool ? new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer) : new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(swapAmount);\n            // Check balance\n            const balance = await inputTokenContract.balanceOf(account);\n            if (balance < amount) {\n                setError(\"Saldo \".concat(selectedTokenIn === \"TokenA\" ? \"Token A\" : \"Token B\", \" tidak mencukupi\"));\n                return;\n            }\n            // Check allowance\n            const swapAddress = contractAddresses.LiquidityPool || contractAddresses.TokenSwap;\n            const allowance = await inputTokenContract.allowance(account, swapAddress);\n            if (allowance < amount) {\n                setSuccess(\"Menyetujui penggunaan \".concat(selectedTokenIn === \"TokenA\" ? \"Token A\" : \"Token B\", \"...\"));\n                const approveTx = await inputTokenContract.approve(swapAddress, amount);\n                await approveTx.wait();\n                setSuccess(\"Approval berhasil! Melakukan swap...\");\n            } else {\n                setSuccess(\"Melakukan swap...\");\n            }\n            // Perform swap\n            if (contractAddresses.LiquidityPool) {\n                // Use LiquidityPool swap function\n                const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);\n                await swapTx.wait();\n            } else {\n                // Use old TokenSwap (only supports TokenA -> TokenB)\n                if (!isTokenAToB) {\n                    setError(\"TokenSwap hanya mendukung Token A → Token B\");\n                    return;\n                }\n                const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);\n                await swapTx.wait();\n            }\n            setSuccess(\"Swap \".concat(selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" → \").concat(selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" berhasil! \\uD83C\\uDF89\"));\n            setSwapAmount(\"\");\n            // Update balances\n            await updateBalances();\n            await updateLiquidity();\n        } catch (error) {\n            console.error(\"Error during swap:\", error);\n            if (error.code === \"ACTION_REJECTED\") {\n                setError(\"Transaksi dibatalkan oleh user\");\n            } else if (error.message.includes(\"insufficient funds\")) {\n                setError(\"Saldo NEX tidak mencukupi untuk gas fee\");\n            } else {\n                setError(\"Swap gagal: \" + (error.reason || error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleAddLiquidity() {\n        if (!liquidityAmountA || !liquidityAmountB || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const amountA = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountA);\n            const amountB = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountB);\n            // Check balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            if (balanceA < amountA) {\n                setError(\"Saldo Token A tidak mencukupi\");\n                return;\n            }\n            if (balanceB < amountB) {\n                setError(\"Saldo Token B tidak mencukupi\");\n                return;\n            }\n            // Approve tokens\n            setSuccess(\"Menyetujui Token A...\");\n            const approveATx = await tokenAContract.approve(contractAddresses.LiquidityPool, amountA);\n            await approveATx.wait();\n            setSuccess(\"Menyetujui Token B...\");\n            const approveBTx = await tokenBContract.approve(contractAddresses.LiquidityPool, amountB);\n            await approveBTx.wait();\n            // Add liquidity\n            setSuccess(\"Menambahkan likuiditas...\");\n            const addLiquidityTx = await lpContract.addLiquidity(amountA, amountB);\n            await addLiquidityTx.wait();\n            setSuccess(\"Likuiditas berhasil ditambahkan! \\uD83C\\uDF89\");\n            setLiquidityAmountA(\"\");\n            setLiquidityAmountB(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error adding liquidity:\", error);\n            setError(\"Gagal menambahkan likuiditas: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleStake() {\n        if (!stakeAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(stakeAmount);\n            // Check LP balance\n            const lpBalance = await lpContract.balanceOf(account);\n            if (lpBalance < amount) {\n                setError(\"Saldo LP Token tidak mencukupi\");\n                return;\n            }\n            // Approve LP tokens\n            setSuccess(\"Menyetujui LP Tokens...\");\n            const approveTx = await lpContract.approve(contractAddresses.Staking, amount);\n            await approveTx.wait();\n            // Stake\n            setSuccess(\"Melakukan stake...\");\n            const stakeTx = await stakingContract.stake(amount);\n            await stakeTx.wait();\n            setSuccess(\"Stake berhasil! \\uD83C\\uDF89\");\n            setStakeAmount(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error staking:\", error);\n            setError(\"Gagal melakukan stake: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleClaim() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            setSuccess(\"Mengklaim rewards...\");\n            const claimTx = await stakingContract.claimReward();\n            await claimTx.wait();\n            setSuccess(\"Rewards berhasil diklaim! \\uD83C\\uDF89\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error claiming:\", error);\n            setError(\"Gagal mengklaim rewards: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const clearMessages = ()=>{\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const isDeployerAccount = account && contractAddresses.deployer && account.toLowerCase() === contractAddresses.deployer.toLowerCase();\n    async function requestTestTokens() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"Requesting test tokens...\");\n        try {\n            // Check if user is the deployer\n            if (isDeployerAccount) {\n                setSuccess(\"You are the deployer! You already have all tokens \\uD83C\\uDF89\");\n                await updateBalances();\n                return;\n            }\n            // For non-deployer users, show instructions\n            setError(\"\");\n            setSuccess(\"\");\n            alert(\"To get test tokens:\\n\\n1. Switch to deployer account: \".concat(contractAddresses.deployer, \"\\n2. Or ask the deployer to send you tokens\\n3. Or use a faucet if available\\n\\nYour current address: \").concat(account, \"\\nDeployer address: \").concat(contractAddresses.deployer, \"\\n\\nThe deployer has 1,000,000 Token A available for distribution.\"));\n        } catch (error) {\n            console.error(\"Error requesting test tokens:\", error);\n            setError(\"Failed to get test tokens: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Batch Operations untuk Maximum Transactions\n    async function handleBatchSwaps() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            // Multiple small swaps untuk generate banyak transaksi\n            const amounts = [\n                \"10\",\n                \"20\",\n                \"30\",\n                \"40\",\n                \"50\"\n            ]; // 5 transaksi swap\n            let totalAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"0\");\n            for (const amount of amounts){\n                totalAmount += ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amount);\n            }\n            // Check balance\n            const balance = await tokenAContract.balanceOf(account);\n            if (balance < totalAmount) {\n                setError(\"Saldo Token A tidak mencukupi untuk batch swaps\");\n                return;\n            }\n            // Approve total amount\n            setSuccess(\"Menyetujui total amount untuk batch swaps...\");\n            const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, totalAmount);\n            await approveTx.wait();\n            // Execute multiple swaps\n            for(let i = 0; i < amounts.length; i++){\n                setSuccess(\"Melakukan swap \".concat(i + 1, \"/\").concat(amounts.length, \"...\"));\n                const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amounts[i]);\n                const swapTx = await swapContract.swap(contractAddresses.TokenA, amount, 0);\n                await swapTx.wait();\n                // Small delay between transactions\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            setSuccess(\"Batch swaps berhasil! \".concat(amounts.length, \" transaksi completed \\uD83C\\uDF89\"));\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error during batch swaps:\", error);\n            setError(\"Batch swaps gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleTransactionSpam() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            // Generate many approval transactions\n            const spamAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"1\");\n            const contracts = [\n                contractAddresses.TokenSwap,\n                contractAddresses.LiquidityPool,\n                contractAddresses.Staking\n            ];\n            let transactionCount = 0;\n            for (const contractAddr of contracts){\n                // Multiple approvals for each contract\n                for(let i = 0; i < 3; i++){\n                    setSuccess(\"Generating approval transaction \".concat(transactionCount + 1, \"...\"));\n                    const approveTx = await tokenAContract.approve(contractAddr, spamAmount);\n                    await approveTx.wait();\n                    transactionCount++;\n                    // Also approve TokenB\n                    const approveBTx = await tokenBContract.approve(contractAddr, spamAmount);\n                    await approveBTx.wait();\n                    transactionCount++;\n                    // Small delay\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                }\n            }\n            setSuccess(\"Transaction spam completed! \".concat(transactionCount, \" transactions generated \\uD83D\\uDE80\"));\n        } catch (error) {\n            console.error(\"Error during transaction spam:\", error);\n            setError(\"Transaction spam gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 relative overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-10 w-32 h-32 bg-cyan-400 transform rotate-12\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 804,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-24 h-24 bg-pink-500 transform -rotate-12\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 805,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 left-1/4 w-28 h-28 bg-yellow-400 transform rotate-45\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 806,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-1/3 w-20 h-20 bg-green-400 transform -rotate-45\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 807,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 803,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10 flex flex-col items-end space-y-3\",\n                children: [\n                    account && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-cyan-400 text-black px-4 py-2 border-4 border-black font-black text-sm shadow-[4px_4px_0px_0px_theme(colors.pink.500)] transform -rotate-1\",\n                        children: [\n                            \"\\uD83D\\uDD17 \",\n                            formatAddress(account)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 812,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 border-4 border-black font-black text-sm shadow-[4px_4px_0px_0px_theme(colors.yellow.400)] transform rotate-1 \".concat(isCorrectNetwork ? \"bg-green-400 text-black\" : \"bg-red-500 text-white animate-pulse\"),\n                        children: isCorrectNetwork ? \"✅ NEXUS TESTNET\" : \"⚠️ WRONG NETWORK!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 text-cyan-400 px-3 py-2 border-4 border-cyan-400 font-mono text-xs max-w-xs shadow-[4px_4px_0px_0px_theme(colors.gray.600)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"\\uD83D\\uDD17 Chain: 3940\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCC4 Contracts: \",\n                                    contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? \"✅\" : \"❌\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 825,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDC64 You: \",\n                                    account ? account.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 823,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 810,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-screen px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8 sm:mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative inline-block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl sm:text-6xl lg:text-7xl font-black mb-4 text-white transform -rotate-1\",\n                                        children: \"NEXUS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 bg-yellow-400 text-black px-3 py-1 border-4 border-black font-black text-lg sm:text-xl transform rotate-12 shadow-[4px_4px_0px_0px_theme(colors.pink.500)]\",\n                                        children: \"SWAP\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-cyan-400 text-black px-6 py-3 border-4 border-black font-black text-sm sm:text-lg max-w-2xl mx-auto mt-6 shadow-[8px_8px_0px_0px_theme(colors.purple.500)] transform rotate-1\",\n                                children: \"\\uD83D\\uDE80 DEFI ON NEXUS BLOCKCHAIN • MAXIMUM TRANSACTIONS • AIRDROP READY \\uD83C\\uDFAF\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 833,\n                        columnNumber: 9\n                    }, this),\n                    (error || success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mb-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-500 text-white px-6 py-4 border-4 border-black font-black text-sm mb-3 shadow-[8px_8px_0px_0px_theme(colors.yellow.400)] transform -rotate-1 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"❌ \",\n                                            error\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"bg-white text-red-500 px-2 py-1 border-2 border-black font-black hover:bg-yellow-400 hover:text-black transition-colors\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 853,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-400 text-black px-6 py-4 border-4 border-black font-black text-sm mb-3 shadow-[8px_8px_0px_0px_theme(colors.pink.500)] transform rotate-1 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"✅ \",\n                                            success\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"bg-white text-green-600 px-2 py-1 border-2 border-black font-black hover:bg-cyan-400 hover:text-black transition-colors\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 849,\n                        columnNumber: 11\n                    }, this),\n                    !account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: connectWallet,\n                                className: \"bg-yellow-400 text-black px-8 py-4 border-4 border-black font-black text-xl shadow-[8px_8px_0px_0px_theme(colors.cyan.400)] hover:shadow-[12px_12px_0px_0px_theme(colors.cyan.400)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200\",\n                                children: \"\\uD83D\\uDD17 CONNECT WALLET\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-pink-500 text-white px-6 py-3 border-4 border-black font-black text-sm mt-6 shadow-[4px_4px_0px_0px_theme(colors.purple.500)] transform -rotate-1 max-w-md mx-auto\",\n                                children: \"\\uD83D\\uDCAB CONNECT TO START DEFI JOURNEY ON NEXUS \\uD83D\\uDE80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 878,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 870,\n                        columnNumber: 11\n                    }, this) : !isCorrectNetwork ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: switchToNexusNetwork,\n                                        className: \"bg-orange-500 text-white px-8 py-4 border-4 border-black font-black text-xl shadow-[8px_8px_0px_0px_theme(colors.red.500)] hover:shadow-[12px_12px_0px_0px_theme(colors.red.500)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200 animate-pulse\",\n                                        children: \"\\uD83D\\uDD04 SWITCH TO NEXUS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: checkNetwork,\n                                            className: \"text-sm text-blue-600 hover:text-blue-800 underline\",\n                                            children: \"Already switched? Click to refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 893,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Please switch to Nexus Testnet III to continue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 883,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border-4 border-white p-4 mb-6 shadow-[8px_8px_0px_0px_theme(colors.cyan.400)] transform -rotate-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-400 text-black px-3 py-1 border-4 border-black font-black text-sm mb-4 shadow-[4px_4px_0px_0px_theme(colors.pink.500)] transform rotate-1 inline-block\",\n                                        children: \"PORTFOLIO\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 910,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-cyan-400 text-black p-3 border-4 border-black shadow-[4px_4px_0px_0px_theme(colors.blue.600)] transform rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs mb-1\",\n                                                        children: \"\\uD83D\\uDD35 TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-black\",\n                                                        children: parseFloat(tokenABalance).toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-pink-400 text-black p-3 border-4 border-black shadow-[4px_4px_0px_0px_theme(colors.purple.600)] transform -rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs mb-1\",\n                                                        children: \"\\uD83D\\uDFE3 TKNB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 921,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-black\",\n                                                        children: parseFloat(tokenBBalance).toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 920,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-400 text-black p-3 border-4 border-black shadow-[4px_4px_0px_0px_theme(colors.emerald.600)] transform rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs mb-1\",\n                                                        children: \"\\uD83D\\uDCA7 LP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-black\",\n                                                        children: parseFloat(lpTokenBalance).toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 928,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-400 text-black p-3 border-4 border-black shadow-[4px_4px_0px_0px_theme(colors.red.600)] transform -rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs mb-1\",\n                                                        children: \"\\uD83C\\uDFE6 STAKE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 933,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-black\",\n                                                        children: parseFloat(stakedBalance).toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(earnedRewards) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 bg-yellow-400 text-black p-4 border-4 border-black shadow-[6px_6px_0px_0px_theme(colors.orange.500)] transform rotate-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-black text-sm\",\n                                                children: \"\\uD83C\\uDF81 PENDING REWARDS\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-mono text-3xl font-black\",\n                                                children: [\n                                                    parseFloat(earnedRewards).toFixed(4),\n                                                    \" TKNB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 942,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 940,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 909,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border-4 border-white shadow-[8px_8px_0px_0px_theme(colors.pink.500)] transform rotate-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex border-b-4 border-white\",\n                                        children: [\n                                            {\n                                                id: \"swap\",\n                                                label: \"SWAP\",\n                                                icon: \"\\uD83D\\uDD04\",\n                                                color: \"bg-cyan-400\"\n                                            },\n                                            {\n                                                id: \"liquidity\",\n                                                label: \"POOL\",\n                                                icon: \"\\uD83D\\uDCA7\",\n                                                color: \"bg-blue-400\"\n                                            },\n                                            {\n                                                id: \"stake\",\n                                                label: \"STAKE\",\n                                                icon: \"\\uD83C\\uDFE6\",\n                                                color: \"bg-green-400\"\n                                            },\n                                            {\n                                                id: \"claim\",\n                                                label: \"CLAIM\",\n                                                icon: \"\\uD83C\\uDF81\",\n                                                color: \"bg-yellow-400\"\n                                            },\n                                            {\n                                                id: \"batch\",\n                                                label: \"BATCH\",\n                                                icon: \"⚡\",\n                                                color: \"bg-pink-400\"\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"flex-1 px-2 py-2 border-r-4 border-white last:border-r-0 font-black text-xs transition-all \".concat(activeTab === tab.id ? \"\".concat(tab.color, \" text-black shadow-[inset_4px_4px_0px_0px_theme(colors.gray.800)]\") : \"bg-gray-700 text-white hover:bg-gray-600\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm mb-1\",\n                                                            children: tab.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 970,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs\",\n                                                            children: tab.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 969,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, tab.id, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 951,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-700 border-4 border-cyan-400 p-4 shadow-[4px_4px_0px_0px_theme(colors.cyan.400)]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-black text-white text-xs\",\n                                                                        children: \"FROM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-black text-cyan-400 text-xs\",\n                                                                        children: selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance).toFixed(2) : parseFloat(tokenBBalance).toFixed(2)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 985,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: selectedTokenIn,\n                                                                        onChange: (e)=>setSelectedTokenIn(e.target.value),\n                                                                        className: \"bg-cyan-400 text-black border-2 border-black px-3 py-2 font-black text-sm focus:outline-none\",\n                                                                        disabled: loading,\n                                                                        title: \"Select token to swap from\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"TokenA\",\n                                                                                children: \"\\uD83D\\uDD35 TKNA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 997,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"TokenB\",\n                                                                                children: \"\\uD83D\\uDFE3 TKNB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 998,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 990,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: swapAmount,\n                                                                        onChange: (e)=>setSwapAmount(e.target.value),\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"flex-1 bg-transparent text-white font-mono text-xl font-black focus:outline-none text-right\",\n                                                                        disabled: loading\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1000,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center -my-2 relative z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>{\n                                                                const temp = selectedTokenIn;\n                                                                setSelectedTokenIn(selectedTokenOut);\n                                                                setSelectedTokenOut(temp);\n                                                            },\n                                                            className: \"bg-pink-500 text-white p-3 border-4 border-black font-black text-xl shadow-[4px_4px_0px_0px_theme(colors.yellow.400)] hover:shadow-[6px_6px_0px_0px_theme(colors.yellow.400)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200 rotate-45 hover:rotate-0\",\n                                                            disabled: loading,\n                                                            children: \"\\uD83D\\uDD04\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1013,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1012,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-700 border-4 border-pink-400 p-4 shadow-[4px_4px_0px_0px_theme(colors.pink.400)]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-black text-white text-xs\",\n                                                                        children: \"TO\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1030,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-black text-pink-400 text-xs\",\n                                                                        children: selectedTokenOut === \"TokenA\" ? parseFloat(tokenABalance).toFixed(2) : parseFloat(tokenBBalance).toFixed(2)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1031,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1029,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: selectedTokenOut,\n                                                                        onChange: (e)=>setSelectedTokenOut(e.target.value),\n                                                                        className: \"bg-pink-400 text-black border-2 border-black px-3 py-2 font-black text-sm focus:outline-none\",\n                                                                        disabled: loading,\n                                                                        title: \"Select token to swap to\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"TokenB\",\n                                                                                children: \"\\uD83D\\uDFE3 TKNB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1043,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"TokenA\",\n                                                                                children: \"\\uD83D\\uDD35 TKNA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1044,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1036,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 text-white font-mono text-xl font-black text-right\",\n                                                                        children: swapAmount ? (parseFloat(swapAmount) * 0.997).toFixed(4) : \"0.0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1046,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1035,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setSwapAmount(\"10\"),\n                                                                className: \"flex-1 bg-yellow-400 text-black py-2 border-2 border-black font-black text-xs hover:bg-yellow-300\",\n                                                                disabled: loading,\n                                                                children: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1054,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setSwapAmount(\"100\"),\n                                                                className: \"flex-1 bg-yellow-400 text-black py-2 border-2 border-black font-black text-xs hover:bg-yellow-300\",\n                                                                disabled: loading,\n                                                                children: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1062,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setSwapAmount(selectedTokenIn === \"TokenA\" ? tokenABalance : tokenBBalance),\n                                                                className: \"flex-1 bg-yellow-400 text-black py-2 border-2 border-black font-black text-xs hover:bg-yellow-300\",\n                                                                disabled: loading,\n                                                                children: \"MAX\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1070,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1053,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleSwap,\n                                                        disabled: loading || !swapAmount || parseFloat(swapAmount) <= 0,\n                                                        className: \"w-full bg-green-400 text-black py-4 border-4 border-black font-black text-lg shadow-[6px_6px_0px_0px_theme(colors.green.600)] hover:shadow-[8px_8px_0px_0px_theme(colors.green.600)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200 disabled:bg-gray-500 disabled:text-gray-300\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-4 border-black border-t-transparent mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1089,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"SWAPPING...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 25\n                                                        }, this) : \"\\uD83D\\uDD04 SWAP \".concat(selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" → \").concat(selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"liquidity\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1101,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token A Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1105,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: liquidityAmountA,\n                                                                                onChange: (e)=>{\n                                                                                    console.log(\"Liquidity A input changed:\", e.target.value);\n                                                                                    setLiquidityAmountA(e.target.value);\n                                                                                },\n                                                                                placeholder: \"0.0\",\n                                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                                disabled: loading,\n                                                                                min: \"0\",\n                                                                                step: \"0.01\",\n                                                                                autoComplete: \"off\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1109,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                                children: \"TKNA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1123,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1108,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"Available: \",\n                                                                                    parseFloat(tokenABalance).toFixed(4),\n                                                                                    \" TKNA\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1128,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(\"10\"),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"10\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1132,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(\"50\"),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"50\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1140,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(tokenABalance),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading || parseFloat(tokenABalance) === 0,\n                                                                                        children: \"Max\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1148,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1131,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1127,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            'Input: \"',\n                                                                            liquidityAmountA,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1158,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1104,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token B Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1164,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: liquidityAmountB,\n                                                                                onChange: (e)=>{\n                                                                                    console.log(\"Liquidity B input changed:\", e.target.value);\n                                                                                    setLiquidityAmountB(e.target.value);\n                                                                                },\n                                                                                placeholder: \"0.0\",\n                                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                                disabled: loading,\n                                                                                min: \"0\",\n                                                                                step: \"0.01\",\n                                                                                autoComplete: \"off\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1168,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                                children: \"TKNB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1182,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1167,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"Available: \",\n                                                                                    parseFloat(tokenBBalance).toFixed(4),\n                                                                                    \" TKNB\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1187,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(\"10\"),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"10\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1191,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(\"50\"),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"50\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1199,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(tokenBBalance),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading || parseFloat(tokenBBalance) === 0,\n                                                                                        children: \"Max\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1207,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1190,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1186,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            'Input: \"',\n                                                                            liquidityAmountB,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1217,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1163,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1103,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleAddLiquidity,\n                                                        disabled: loading || !liquidityAmountA || !liquidityAmountB,\n                                                        className: \"w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1223,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1100,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"stake\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1236,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Stake Amount (LP Tokens)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1239,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: stakeAmount,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Stake input changed:\", e.target.value);\n                                                                            setStakeAmount(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1243,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1257,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1242,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Available: \",\n                                                                            parseFloat(lpTokenBalance).toFixed(4),\n                                                                            \" NLP\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1262,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setStakeAmount(\"1\"),\n                                                                                className: \"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200\",\n                                                                                disabled: loading,\n                                                                                children: \"1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1266,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setStakeAmount(\"5\"),\n                                                                                className: \"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200\",\n                                                                                disabled: loading,\n                                                                                children: \"5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1274,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setStakeAmount(lpTokenBalance),\n                                                                                className: \"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200\",\n                                                                                disabled: loading || parseFloat(lpTokenBalance) === 0,\n                                                                                children: \"Max\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1282,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1265,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1261,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: [\n                                                                    'Input: \"',\n                                                                    stakeAmount,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1292,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1238,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleStake,\n                                                        disabled: loading || !stakeAmount || parseFloat(lpTokenBalance) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-orange-600 to-red-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1297,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1235,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"claim\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6 p-4 bg-yellow-50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mb-2\",\n                                                                children: \"Pending Rewards\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1313,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-mono text-3xl font-bold text-yellow-600\",\n                                                                children: parseFloat(earnedRewards).toFixed(6)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1314,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"TKNB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1317,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1312,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Staked\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1322,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold\",\n                                                                        children: parseFloat(stakedBalance).toFixed(4)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1323,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1326,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1321,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"APY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1329,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold text-green-600\",\n                                                                        children: \"~50%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1330,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"Estimated\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1333,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1328,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1320,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleClaim,\n                                                        disabled: loading || parseFloat(earnedRewards) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-yellow-600 to-orange-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-yellow-700 hover:to-orange-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1337,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1309,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"batch\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Batch Operations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-6\",\n                                                        children: \"Generate multiple transactions for maximum Nexus testnet interaction!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-blue-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-blue-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDD04 Batch Swaps\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1358,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-blue-700 mb-3\",\n                                                                        children: \"Execute 5 separate swap transactions (10, 20, 30, 40, 50 TKNA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1359,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleBatchSwaps,\n                                                                        disabled: loading || parseFloat(tokenABalance) < 150,\n                                                                        className: \"w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Execute Batch Swaps (5 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1362,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 mt-1\",\n                                                                        children: \"Requires: 150 TKNA minimum\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1370,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1357,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-purple-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-purple-800 mb-2\",\n                                                                        children: \"⚡ Transaction Spam\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1377,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-purple-700 mb-3\",\n                                                                        children: \"Generate 18 approval transactions across all contracts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1378,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleTransactionSpam,\n                                                                        disabled: loading,\n                                                                        className: \"w-full bg-purple-600 text-white py-3 rounded-lg font-medium hover:bg-purple-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Generate Transaction Spam (18 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1381,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-purple-600 mt-1\",\n                                                                        children: \"Generates many approval transactions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1389,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-green-50 rounded-xl text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-green-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCCA Transaction Counter\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1396,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-700 mb-2\",\n                                                                        children: \"Estimated transactions per full cycle:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1397,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"Basic Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1402,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"8-10 TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1403,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1401,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"With Batches\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1406,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"25+ TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1407,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1405,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1400,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1395,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-yellow-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCA1 Pro Tips\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1414,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-sm text-yellow-700 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Use batch operations to generate many transactions quickly\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1416,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Each operation creates multiple blockchain interactions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1417,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Perfect for maximizing Nexus testnet contribution\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1418,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Monitor your transaction count in MetaMask\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1419,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1415,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1413,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1355,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(tokenABalance) === 0 && activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-6 mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-yellow-800 mb-2\",\n                                                children: \"Need Test Tokens?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1430,\n                                                columnNumber: 19\n                                            }, this),\n                                            isDeployerAccount ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"You are the deployer! You have 1,000,000 Token A available.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1433,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: updateBalances,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200\",\n                                                        children: \"Refresh Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1436,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1432,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"To get test tokens, switch to the deployer account or ask for a transfer:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1446,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-yellow-600 mb-3 font-mono bg-yellow-100 p-2 rounded\",\n                                                        children: [\n                                                            \"Deployer: \",\n                                                            (_contractAddresses_deployer = contractAddresses.deployer) === null || _contractAddresses_deployer === void 0 ? void 0 : _contractAddresses_deployer.slice(0, 20),\n                                                            \"...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: requestTestTokens,\n                                                        disabled: loading,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                        children: \"Show Instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1452,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1445,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1429,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 950,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"DeFi Flow Guide:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"1. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Swap\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1470,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Trade Token A ⇄ Token B\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"2. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1471,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Provide both tokens → Get LP tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1471,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"3. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Stake\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1472,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Stake LP tokens → Earn rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"4. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Claim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1473,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Collect your earned rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1473,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"5. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Repeat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1474,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": More transactions = More Nexus interaction!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1474,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1469,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1467,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 907,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 831,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 801,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Xn9OxmB2iOgPw0us/9Bh41mAbgc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});