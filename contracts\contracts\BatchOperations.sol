// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

// Interfaces for interacting with other contracts
interface ILiquidityPool {
    function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256 amountOut);
    function addLiquidity(uint256 amountA, uint256 amountB) external returns (uint256 liquidity);
    function removeLiquidity(uint256 liquidity) external returns (uint256 amountA, uint256 amountB);
    function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256 amountOut);
}

interface IStaking {
    function stake(uint256 amount) external;
    function withdraw(uint256 amount) external;
    function claimReward() external;
}

/**
 * @title BatchOperations
 * @dev Advanced batch operations contract for Nexus Layer 1 DeFi
 * Features:
 * - Batch swaps with slippage protection
 * - Batch liquidity operations
 * - Batch staking operations
 * - Gas optimization through batching
 * - Emergency pause functionality
 * - Transaction fee collection
 */
contract BatchOperations is ReentrancyGuard, Ownable, Pausable {
    IERC20 public immutable tokenA;
    IERC20 public immutable tokenB;
    IERC20 public immutable lpToken;
    ILiquidityPool public immutable liquidityPool;
    IStaking public immutable stakingContract;
    
    // Fee configuration
    uint256 public batchFee = 10; // 0.1% fee for batch operations
    uint256 public constant FEE_DENOMINATOR = 10000;
    address public feeRecipient;
    
    // Statistics
    uint256 public totalBatchOperations;
    uint256 public totalFeesCollected;
    mapping(address => uint256) public userBatchCount;
    
    // Batch operation types
    enum OperationType {
        SWAP,
        ADD_LIQUIDITY,
        REMOVE_LIQUIDITY,
        STAKE,
        WITHDRAW,
        CLAIM
    }
    
    struct BatchSwap {
        address tokenIn;
        uint256 amountIn;
        uint256 minAmountOut;
    }
    
    struct BatchLiquidity {
        uint256 amountA;
        uint256 amountB;
        uint256 minLiquidity;
    }
    
    event BatchOperationExecuted(
        address indexed user,
        OperationType indexed opType,
        uint256 operationCount,
        uint256 totalGasUsed,
        uint256 feeCharged
    );
    event BatchSwapsExecuted(address indexed user, uint256 swapCount, uint256 totalAmountIn, uint256 totalAmountOut);
    event BatchLiquidityAdded(address indexed user, uint256 operationCount, uint256 totalLiquidity);
    event BatchStakingExecuted(address indexed user, uint256 operationCount, uint256 totalAmount);
    event FeeUpdated(uint256 oldFee, uint256 newFee);
    event FeeRecipientUpdated(address indexed oldRecipient, address indexed newRecipient);
    
    constructor(
        address _tokenA,
        address _tokenB,
        address _lpToken,
        address _liquidityPool,
        address _stakingContract,
        address _feeRecipient
    ) Ownable(msg.sender) {
        require(_tokenA != address(0), "TokenA cannot be zero address");
        require(_tokenB != address(0), "TokenB cannot be zero address");
        require(_lpToken != address(0), "LP token cannot be zero address");
        require(_liquidityPool != address(0), "Liquidity pool cannot be zero address");
        require(_stakingContract != address(0), "Staking contract cannot be zero address");
        require(_feeRecipient != address(0), "Fee recipient cannot be zero address");
        
        tokenA = IERC20(_tokenA);
        tokenB = IERC20(_tokenB);
        lpToken = IERC20(_lpToken);
        liquidityPool = ILiquidityPool(_liquidityPool);
        stakingContract = IStaking(_stakingContract);
        feeRecipient = _feeRecipient;
    }
    
    /**
     * @dev Execute multiple swaps in a single transaction
     * @param swaps Array of swap operations to execute
     */
    function batchSwap(BatchSwap[] calldata swaps) external nonReentrant whenNotPaused returns (uint256[] memory amountsOut) {
        require(swaps.length > 0, "No swaps provided");
        require(swaps.length <= 10, "Too many swaps"); // Gas limit protection
        
        uint256 gasStart = gasleft();
        amountsOut = new uint256[](swaps.length);
        uint256 totalAmountIn = 0;
        uint256 totalAmountOut = 0;
        
        for (uint256 i = 0; i < swaps.length; i++) {
            BatchSwap memory swapOp = swaps[i];
            require(swapOp.tokenIn == address(tokenA) || swapOp.tokenIn == address(tokenB), "Invalid token");
            require(swapOp.amountIn > 0, "Amount must be greater than 0");
            
            // Calculate and charge fee
            uint256 fee = (swapOp.amountIn * batchFee) / FEE_DENOMINATOR;
            uint256 amountAfterFee = swapOp.amountIn - fee;
            
            // Transfer tokens from user (including fee)
            IERC20(swapOp.tokenIn).transferFrom(msg.sender, address(this), swapOp.amountIn);
            
            // Transfer fee to recipient
            if (fee > 0) {
                IERC20(swapOp.tokenIn).transfer(feeRecipient, fee);
                totalFeesCollected += fee;
            }
            
            // Approve and execute swap
            IERC20(swapOp.tokenIn).approve(address(liquidityPool), amountAfterFee);
            amountsOut[i] = liquidityPool.swap(swapOp.tokenIn, amountAfterFee, swapOp.minAmountOut);
            
            totalAmountIn += swapOp.amountIn;
            totalAmountOut += amountsOut[i];
        }
        
        uint256 gasUsed = gasStart - gasleft();
        totalBatchOperations++;
        userBatchCount[msg.sender]++;
        
        emit BatchSwapsExecuted(msg.sender, swaps.length, totalAmountIn, totalAmountOut);
        emit BatchOperationExecuted(msg.sender, OperationType.SWAP, swaps.length, gasUsed, 0);
    }
    
    /**
     * @dev Execute multiple liquidity additions in a single transaction
     * @param liquidityOps Array of liquidity operations to execute
     */
    function batchAddLiquidity(BatchLiquidity[] calldata liquidityOps) external nonReentrant whenNotPaused returns (uint256[] memory liquidityAmounts) {
        require(liquidityOps.length > 0, "No liquidity operations provided");
        require(liquidityOps.length <= 5, "Too many operations"); // Gas limit protection
        
        uint256 gasStart = gasleft();
        liquidityAmounts = new uint256[](liquidityOps.length);
        uint256 totalLiquidity = 0;
        
        for (uint256 i = 0; i < liquidityOps.length; i++) {
            BatchLiquidity memory liqOp = liquidityOps[i];
            require(liqOp.amountA > 0 && liqOp.amountB > 0, "Amounts must be greater than 0");
            
            // Transfer tokens from user
            tokenA.transferFrom(msg.sender, address(this), liqOp.amountA);
            tokenB.transferFrom(msg.sender, address(this), liqOp.amountB);
            
            // Approve and execute liquidity addition
            tokenA.approve(address(liquidityPool), liqOp.amountA);
            tokenB.approve(address(liquidityPool), liqOp.amountB);
            liquidityAmounts[i] = liquidityPool.addLiquidity(liqOp.amountA, liqOp.amountB);
            
            require(liquidityAmounts[i] >= liqOp.minLiquidity, "Insufficient liquidity minted");
            totalLiquidity += liquidityAmounts[i];
        }
        
        uint256 gasUsed = gasStart - gasleft();
        totalBatchOperations++;
        userBatchCount[msg.sender]++;
        
        emit BatchLiquidityAdded(msg.sender, liquidityOps.length, totalLiquidity);
        emit BatchOperationExecuted(msg.sender, OperationType.ADD_LIQUIDITY, liquidityOps.length, gasUsed, 0);
    }
    
    /**
     * @dev Execute multiple staking operations in a single transaction
     * @param amounts Array of amounts to stake
     */
    function batchStake(uint256[] calldata amounts) external nonReentrant whenNotPaused {
        require(amounts.length > 0, "No amounts provided");
        require(amounts.length <= 10, "Too many operations");
        
        uint256 gasStart = gasleft();
        uint256 totalAmount = 0;
        
        for (uint256 i = 0; i < amounts.length; i++) {
            require(amounts[i] > 0, "Amount must be greater than 0");
            totalAmount += amounts[i];
        }
        
        // Transfer total LP tokens from user
        lpToken.transferFrom(msg.sender, address(this), totalAmount);
        
        // Approve and execute staking operations
        lpToken.approve(address(stakingContract), totalAmount);
        
        for (uint256 i = 0; i < amounts.length; i++) {
            stakingContract.stake(amounts[i]);
        }
        
        uint256 gasUsed = gasStart - gasleft();
        totalBatchOperations++;
        userBatchCount[msg.sender]++;
        
        emit BatchStakingExecuted(msg.sender, amounts.length, totalAmount);
        emit BatchOperationExecuted(msg.sender, OperationType.STAKE, amounts.length, gasUsed, 0);
    }
    
    /**
     * @dev Get estimated output for batch swaps
     * @param swaps Array of swap operations to estimate
     */
    function estimateBatchSwap(BatchSwap[] calldata swaps) external view returns (uint256[] memory estimatedOutputs, uint256 totalFees) {
        estimatedOutputs = new uint256[](swaps.length);
        
        for (uint256 i = 0; i < swaps.length; i++) {
            uint256 fee = (swaps[i].amountIn * batchFee) / FEE_DENOMINATOR;
            uint256 amountAfterFee = swaps[i].amountIn - fee;
            estimatedOutputs[i] = liquidityPool.getAmountOut(swaps[i].tokenIn, amountAfterFee);
            totalFees += fee;
        }
    }
    
    /**
     * @dev Get batch operation statistics for a user
     */
    function getUserBatchStats(address user) external view returns (
        uint256 batchCount,
        uint256 totalOperations,
        uint256 currentFeeRate
    ) {
        return (
            userBatchCount[user],
            totalBatchOperations,
            batchFee
        );
    }
    
    // Admin functions
    /**
     * @dev Update batch fee (only owner)
     */
    function setBatchFee(uint256 newFee) external onlyOwner {
        require(newFee <= 100, "Fee too high"); // Max 1%
        uint256 oldFee = batchFee;
        batchFee = newFee;
        emit FeeUpdated(oldFee, newFee);
    }
    
    /**
     * @dev Update fee recipient (only owner)
     */
    function setFeeRecipient(address newRecipient) external onlyOwner {
        require(newRecipient != address(0), "Cannot be zero address");
        address oldRecipient = feeRecipient;
        feeRecipient = newRecipient;
        emit FeeRecipientUpdated(oldRecipient, newRecipient);
    }
    
    /**
     * @dev Pause the contract (emergency function)
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause the contract
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev Emergency withdraw tokens (only when paused)
     */
    function emergencyWithdraw(address token) external onlyOwner whenPaused {
        IERC20(token).transfer(owner(), IERC20(token).balanceOf(address(this)));
    }
}
