{"version": 3, "sources": ["../../../../src/build/webpack/loaders/utils.ts"], "names": ["createHash", "RSC_MODULE_TYPES", "imageExtensions", "imageRegex", "RegExp", "join", "isClientComponentEntryModule", "mod", "rscInfo", "buildInfo", "rsc", "hasClientDirective", "isClientRef", "isActionLayerEntry", "actions", "type", "client", "test", "resource", "regexCSS", "isCSSMod", "loaders", "some", "loader", "includes", "getActions", "generateActionId", "filePath", "exportName", "update", "digest", "encodeToBase64", "obj", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "decodeFromBase64", "str", "parse"], "mappings": "AAAA,SAASA,UAAU,QAAQ,SAAQ;AACnC,SAASC,gBAAgB,QAAQ,gCAA+B;AAEhE,MAAMC,kBAAkB;IAAC;IAAO;IAAQ;IAAO;IAAQ;IAAQ;IAAO;CAAM;AAC5E,MAAMC,aAAa,IAAIC,OAAO,CAAC,IAAI,EAAEF,gBAAgBG,IAAI,CAAC,KAAK,EAAE,CAAC;AAElE,OAAO,SAASC,6BAA6BC,GAG5C;IACC,MAAMC,UAAUD,IAAIE,SAAS,CAACC,GAAG;IACjC,MAAMC,qBAAqBH,2BAAAA,QAASI,WAAW;IAC/C,MAAMC,qBACJL,CAAAA,2BAAAA,QAASM,OAAO,KAAIN,CAAAA,2BAAAA,QAASO,IAAI,MAAKd,iBAAiBe,MAAM;IAC/D,OACEL,sBAAsBE,sBAAsBV,WAAWc,IAAI,CAACV,IAAIW,QAAQ;AAE5E;AAEA,OAAO,MAAMC,WAAW,4BAA2B;AAEnD,6EAA6E;AAC7E,gDAAgD;AAChD,OAAO,SAASC,SAASb,GAIxB;QAIGA;IAHF,OAAO,CAAC,CACNA,CAAAA,IAAIQ,IAAI,KAAK,sBACZR,IAAIW,QAAQ,IAAIC,SAASF,IAAI,CAACV,IAAIW,QAAQ,OAC3CX,eAAAA,IAAIc,OAAO,qBAAXd,aAAae,IAAI,CACf,CAAC,EAAEC,MAAM,EAAE,GACTA,OAAOC,QAAQ,CAAC,iCAChBD,OAAOC,QAAQ,CAAC,wCAChBD,OAAOC,QAAQ,CAAC,4CACpB;AAEJ;AAEA,OAAO,SAASC,WAAWlB,GAG1B;QACQA,oBAAAA;IAAP,QAAOA,iBAAAA,IAAIE,SAAS,sBAAbF,qBAAAA,eAAeG,GAAG,qBAAlBH,mBAAoBO,OAAO;AACpC;AAEA,OAAO,SAASY,iBAAiBC,QAAgB,EAAEC,UAAkB;IACnE,OAAO5B,WAAW,QACf6B,MAAM,CAACF,WAAW,MAAMC,YACxBE,MAAM,CAAC;AACZ;AAEA,OAAO,SAASC,eAA6BC,GAAM;IACjD,OAAOC,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACJ,MAAMK,QAAQ,CAAC;AACnD;AAEA,OAAO,SAASC,iBAA+BC,GAAW;IACxD,OAAOJ,KAAKK,KAAK,CAACP,OAAOC,IAAI,CAACK,KAAK,UAAUF,QAAQ,CAAC;AACxD"}