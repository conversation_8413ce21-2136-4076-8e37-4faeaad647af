"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Contract ABIs - Updated with complete function signatures\nconst TokenA_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) external returns (bool)\",\n    \"function allowance(address owner, address spender) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) external returns (bool)\",\n    \"function transferFrom(address from, address to, uint256 amount) external returns (bool)\"\n];\nconst TokenB_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\"\n];\nconst TokenSwap_ABI = [\n    \"function swap(uint256 amount) external returns (bool)\",\n    \"function getTokenBLiquidity() external view returns (uint256)\",\n    \"function getTokenABalance() external view returns (uint256)\",\n    \"function tokenA() external view returns (address)\",\n    \"function tokenB() external view returns (address)\",\n    \"event Swap(address indexed user, uint256 amount)\"\n];\n// Nexus network configuration\nconst NEXUS_NETWORK = {\n    chainId: \"0xF64\",\n    chainName: \"Nexus Testnet III\",\n    nativeCurrency: {\n        name: \"NEX\",\n        symbol: \"NEX\",\n        decimals: 18\n    },\n    rpcUrls: [\n        \"https://testnet3.rpc.nexus.xyz\"\n    ],\n    blockExplorerUrls: [\n        \"https://explorer.nexus.xyz\"\n    ]\n};\n// Default contract addresses (will be replaced with deployed addresses)\nconst DEFAULT_ADDRESSES = {\n    TokenA: \"0x0000000000000000000000000000000000000000\",\n    TokenB: \"0x0000000000000000000000000000000000000000\",\n    TokenSwap: \"0x0000000000000000000000000000000000000000\"\n};\nfunction Home() {\n    var _contractAddresses_deployer;\n    _s();\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenABalance, setTokenABalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [tokenBBalance, setTokenBBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [swapAmount, setSwapAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_ADDRESSES);\n    const [liquidity, setLiquidity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [isCorrectNetwork, setIsCorrectNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkIfWalletIsConnected();\n        loadContractAddresses();\n        setupEventListeners();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (account && isCorrectNetwork) {\n            updateBalances();\n            updateLiquidity();\n        }\n    }, [\n        account,\n        contractAddresses,\n        isCorrectNetwork\n    ]);\n    function setupEventListeners() {\n        const { ethereum } = window;\n        if (!ethereum) return;\n        // Listen for account changes\n        ethereum.on(\"accountsChanged\", (accounts)=>{\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                checkNetwork();\n            } else {\n                setAccount(\"\");\n                setIsCorrectNetwork(false);\n            }\n        });\n        // Listen for network changes\n        ethereum.on(\"chainChanged\", (chainId)=>{\n            console.log(\"Network changed to:\", chainId);\n            checkNetwork();\n            // Reload page to reset state\n            window.location.reload();\n        });\n        // Cleanup function\n        return ()=>{\n            if (ethereum.removeListener) {\n                ethereum.removeListener(\"accountsChanged\", ()=>{});\n                ethereum.removeListener(\"chainChanged\", ()=>{});\n            }\n        };\n    }\n    async function loadContractAddresses() {\n        try {\n            // Try to load deployed addresses from public folder\n            const response = await fetch(\"/deployedAddresses.json\");\n            if (response.ok) {\n                const addresses = await response.json();\n                setContractAddresses(addresses);\n                console.log(\"Loaded contract addresses:\", addresses);\n            } else {\n                console.warn(\"Could not load deployed addresses, using defaults\");\n            }\n        } catch (error) {\n            console.warn(\"Could not load deployed addresses:\", error);\n        }\n    }\n    async function checkIfWalletIsConnected() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_accounts\"\n            });\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                await checkNetwork();\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setError(\"Error saat mengecek koneksi wallet\");\n        }\n    }\n    async function checkNetwork() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setIsCorrectNetwork(false);\n                return;\n            }\n            const chainId = await ethereum.request({\n                method: \"eth_chainId\"\n            });\n            console.log(\"Current chainId:\", chainId, \"Expected:\", NEXUS_NETWORK.chainId);\n            // Convert both to same format for comparison\n            const currentChainId = parseInt(chainId, 16);\n            const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);\n            if (currentChainId === expectedChainId) {\n                setIsCorrectNetwork(true);\n                setError(\"\");\n                console.log(\"✅ Connected to correct network\");\n            } else {\n                setIsCorrectNetwork(false);\n                setError(\"Wrong network. Current: \".concat(currentChainId, \", Expected: \").concat(expectedChainId, \" (Nexus Testnet III)\"));\n                console.log(\"❌ Wrong network detected\");\n            }\n        } catch (error) {\n            console.error(\"Error checking network:\", error);\n            setIsCorrectNetwork(false);\n            setError(\"Error checking network\");\n        }\n    }\n    async function switchToNexusNetwork() {\n        try {\n            const { ethereum } = window;\n            setError(\"\");\n            setSuccess(\"Switching to Nexus network...\");\n            try {\n                await ethereum.request({\n                    method: \"wallet_switchEthereumChain\",\n                    params: [\n                        {\n                            chainId: NEXUS_NETWORK.chainId\n                        }\n                    ]\n                });\n                console.log(\"✅ Network switch requested\");\n            } catch (switchError) {\n                console.log(\"Switch error code:\", switchError.code);\n                // Network belum ditambahkan, tambahkan dulu\n                if (switchError.code === 4902) {\n                    console.log(\"Adding Nexus network...\");\n                    await ethereum.request({\n                        method: \"wallet_addEthereumChain\",\n                        params: [\n                            NEXUS_NETWORK\n                        ]\n                    });\n                    console.log(\"✅ Network added\");\n                } else {\n                    throw switchError;\n                }\n            }\n            // Wait a bit for network to switch\n            setTimeout(async ()=>{\n                await checkNetwork();\n                setSuccess(\"\");\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error switching network:\", error);\n            setError(\"Gagal switch ke Nexus network: \" + (error.message || \"Unknown error\"));\n            setSuccess(\"\");\n        }\n    }\n    async function connectWallet() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            setAccount(accounts[0]);\n            await checkNetwork();\n            setError(\"\");\n            setSuccess(\"Wallet berhasil terhubung!\");\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n            setError(\"Gagal menghubungkan wallet\");\n        }\n    }\n    async function updateBalances() {\n        if (!account || !isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {\n            console.log(\"Contract addresses not loaded yet, skipping balance update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating balances for:\", account);\n            console.log(\"TokenA address:\", contractAddresses.TokenA);\n            console.log(\"TokenB address:\", contractAddresses.TokenB);\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, provider);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, provider);\n            // Test if contracts exist\n            try {\n                const [balanceA, balanceB] = await Promise.all([\n                    tokenAContract.balanceOf(account),\n                    tokenBContract.balanceOf(account)\n                ]);\n                console.log(\"Raw balanceA:\", balanceA.toString());\n                console.log(\"Raw balanceB:\", balanceB.toString());\n                setTokenABalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceA));\n                setTokenBBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceB));\n                console.log(\"Balances updated successfully\");\n            } catch (contractError) {\n                console.error(\"Contract call error:\", contractError);\n                setError(\"Error reading token balances. Contracts may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating balances:\", error);\n            setError(\"Error connecting to contracts\");\n        }\n    }\n    async function updateLiquidity() {\n        if (!isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {\n            console.log(\"TokenSwap address not loaded yet, skipping liquidity update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating liquidity for TokenSwap:\", contractAddresses.TokenSwap);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);\n            try {\n                const liquidityAmount = await swapContract.getTokenBLiquidity();\n                console.log(\"Raw liquidity:\", liquidityAmount.toString());\n                setLiquidity(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(liquidityAmount));\n                console.log(\"Liquidity updated successfully\");\n            } catch (contractError) {\n                console.error(\"TokenSwap contract call error:\", contractError);\n                setError(\"Error reading liquidity. TokenSwap contract may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating liquidity:\", error);\n            setError(\"Error connecting to TokenSwap contract\");\n        }\n    }\n    async function handleSwap() {\n        if (!swapAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(swapAmount);\n            // Check balance\n            const balance = await tokenAContract.balanceOf(account);\n            if (balance < amount) {\n                setError(\"Saldo Token A tidak mencukupi\");\n                return;\n            }\n            // Check liquidity\n            const availableLiquidity = await swapContract.getTokenBLiquidity();\n            if (availableLiquidity < amount) {\n                setError(\"Likuiditas Token B tidak mencukupi\");\n                return;\n            }\n            // Check allowance\n            const allowance = await tokenAContract.allowance(account, contractAddresses.TokenSwap);\n            if (allowance < amount) {\n                setSuccess(\"Menyetujui penggunaan Token A...\");\n                const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, amount);\n                await approveTx.wait();\n                setSuccess(\"Approval berhasil! Melakukan swap...\");\n            } else {\n                setSuccess(\"Melakukan swap...\");\n            }\n            // Perform swap\n            const swapTx = await swapContract.swap(amount);\n            await swapTx.wait();\n            setSuccess(\"Swap berhasil! \\uD83C\\uDF89\");\n            setSwapAmount(\"\");\n            // Update balances and liquidity\n            await updateBalances();\n            await updateLiquidity();\n        } catch (error) {\n            console.error(\"Error during swap:\", error);\n            if (error.code === \"ACTION_REJECTED\") {\n                setError(\"Transaksi dibatalkan oleh user\");\n            } else if (error.message.includes(\"insufficient funds\")) {\n                setError(\"Saldo NEX tidak mencukupi untuk gas fee\");\n            } else {\n                setError(\"Swap gagal: \" + (error.reason || error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    }\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const clearMessages = ()=>{\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const isDeployerAccount = account && contractAddresses.deployer && account.toLowerCase() === contractAddresses.deployer.toLowerCase();\n    async function requestTestTokens() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"Requesting test tokens...\");\n        try {\n            // Check if user is the deployer\n            if (isDeployerAccount) {\n                setSuccess(\"You are the deployer! You already have all tokens \\uD83C\\uDF89\");\n                await updateBalances();\n                return;\n            }\n            // For non-deployer users, show instructions\n            setError(\"\");\n            setSuccess(\"\");\n            alert(\"To get test tokens:\\n\\n1. Switch to deployer account: \".concat(contractAddresses.deployer, \"\\n2. Or ask the deployer to send you tokens\\n3. Or use a faucet if available\\n\\nYour current address: \").concat(account, \"\\nDeployer address: \").concat(contractAddresses.deployer, \"\\n\\nThe deployer has 1,000,000 Token A available for distribution.\"));\n        } catch (error) {\n            console.error(\"Error requesting test tokens:\", error);\n            setError(\"Failed to get test tokens: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-6 right-6 flex flex-col items-end space-y-2\",\n                children: [\n                    account && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Connected:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            formatAddress(account)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full \".concat(isCorrectNetwork ? \"bg-green-500\" : \"bg-red-500\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: isCorrectNetwork ? \"Nexus Testnet III\" : \"Wrong Network\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 max-w-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Expected Chain: 3940 (0xF64)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Contracts: \",\n                                    contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? \"✅\" : \"❌\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Deployer: \",\n                                    contractAddresses.deployer ? contractAddresses.deployer.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Your Address: \",\n                                    account ? account.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 446,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-screen px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"Nexus Swap\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Decentralized token exchange on Nexus blockchain. Swap Token A for Token B at a fixed 1:1 ratio.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 9\n                    }, this),\n                    (error || success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mb-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-red-500 hover:text-red-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-green-500 hover:text-green-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 11\n                    }, this),\n                    !account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: connectWallet,\n                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Connect your MetaMask wallet to start swapping tokens\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 11\n                    }, this) : !isCorrectNetwork ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: switchToNexusNetwork,\n                                        className: \"bg-orange-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                        children: \"Switch to Nexus Network\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: checkNetwork,\n                                            className: \"text-sm text-blue-600 hover:text-blue-800 underline\",\n                                            children: \"Already switched? Click to refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Please switch to Nexus Testnet III to continue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl shadow-xl p-8 border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-6 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-blue-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token A Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-2xl font-bold text-blue-600\",\n                                                        children: parseFloat(tokenABalance).toFixed(4)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-purple-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token B Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-2xl font-bold text-purple-600\",\n                                                        children: parseFloat(tokenBBalance).toFixed(4)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-6 p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Available Liquidity\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-mono text-lg font-semibold text-gray-800\",\n                                                children: [\n                                                    parseFloat(liquidity).toFixed(2),\n                                                    \" Token B\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(tokenABalance) === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-yellow-800 mb-2\",\n                                                children: \"Need Test Tokens?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 19\n                                            }, this),\n                                            isDeployerAccount ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"You are the deployer! You have 1,000,000 Token A available.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: updateBalances,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200\",\n                                                        children: \"Refresh Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"To get test tokens, switch to the deployer account or ask for a transfer:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-yellow-600 mb-3 font-mono bg-yellow-100 p-2 rounded\",\n                                                        children: [\n                                                            \"Deployer: \",\n                                                            (_contractAddresses_deployer = contractAddresses.deployer) === null || _contractAddresses_deployer === void 0 ? void 0 : _contractAddresses_deployer.slice(0, 20),\n                                                            \"...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: requestTestTokens,\n                                                        disabled: loading,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                        children: \"Show Instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Swap Amount (Token A → Token B)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: swapAmount,\n                                                        onChange: (e)=>{\n                                                            console.log(\"Input changed:\", e.target.value);\n                                                            setSwapAmount(e.target.value);\n                                                        },\n                                                        placeholder: \"0.0\",\n                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-4 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400 shadow-sm\",\n                                                        disabled: loading,\n                                                        min: \"0\",\n                                                        step: \"0.01\",\n                                                        autoComplete: \"off\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                        children: \"TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"Exchange rate: 1 TKNA = 1 TKNB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setSwapAmount(\"10\"),\n                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                disabled: loading,\n                                                                children: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setSwapAmount(\"100\"),\n                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                disabled: loading,\n                                                                children: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setSwapAmount(tokenABalance),\n                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                disabled: loading || parseFloat(tokenABalance) === 0,\n                                                                children: \"Max\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 mt-1\",\n                                                children: [\n                                                    'Current input: \"',\n                                                    swapAmount,\n                                                    '\" (Length: ',\n                                                    swapAmount.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleSwap,\n                                        disabled: loading || !swapAmount || parseFloat(swapAmount) <= 0,\n                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Processing...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 19\n                                        }, this) : \"Swap Tokens\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-6 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 text-center\",\n                                                children: \"Smart contracts deployed on Nexus Testnet III\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400 mt-2 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"TokenA: \",\n                                                            formatAddress(contractAddresses.TokenA)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"TokenB: \",\n                                                            formatAddress(contractAddresses.TokenB)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"Swap: \",\n                                                            formatAddress(contractAddresses.TokenSwap)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"How to use:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"1. Make sure you have Token A in your wallet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"2. Enter the amount you want to swap\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: '3. Click \"Swap Tokens\" and confirm the transaction'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"4. Wait for confirmation and see your new Token B balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 444,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"EjoYfBLQTh/yMLgch/KCk+Cuf6Y=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});