'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';

interface Token {
  symbol: string;
  name: string;
  address: string;
  balance: string;
  decimals: number;
}

interface SwapInterfaceProps {
  account: string;
  isConnected: boolean;
  isCorrectNetwork: boolean;
  contractAddresses: any;
  tokenABalance: string;
  tokenBBalance: string;
  onSwap: (tokenIn: string, tokenOut: string, amount: string) => Promise<void>;
  loading: boolean;
}

export default function SwapInterface({
  account,
  isConnected,
  isCorrectNetwork,
  contractAddresses,
  tokenABalance,
  tokenBBalance,
  onSwap,
  loading
}: SwapInterfaceProps) {
  const [fromToken, setFromToken] = useState<Token>({
    symbol: 'TKNA',
    name: 'Token A',
    address: contractAddresses.TokenA,
    balance: tokenABalance,
    decimals: 18
  });
  
  const [toToken, setToToken] = useState<Token>({
    symbol: 'TKNB',
    name: 'Token B',
    address: contractAddresses.TokenB,
    balance: tokenBBalance,
    decimals: 18
  });

  const [fromAmount, setFromAmount] = useState('');
  const [toAmount, setToAmount] = useState('');
  const [slippage, setSlippage] = useState('0.5');
  const [priceImpact, setPriceImpact] = useState('0.00');
  const [exchangeRate, setExchangeRate] = useState('1.00');
  const [minimumReceived, setMinimumReceived] = useState('0.00');
  const [tradeFee, setTradeFee] = useState('0.00');
  const [showSettings, setShowSettings] = useState(false);

  const tokens = [
    {
      symbol: 'TKNA',
      name: 'Token A',
      address: contractAddresses.TokenA,
      balance: tokenABalance,
      decimals: 18
    },
    {
      symbol: 'TKNB',
      name: 'Token B',
      address: contractAddresses.TokenB,
      balance: tokenBBalance,
      decimals: 18
    }
  ];

  useEffect(() => {
    if (fromAmount && parseFloat(fromAmount) > 0) {
      calculateSwapDetails();
    } else {
      setToAmount('');
      setPriceImpact('0.00');
      setMinimumReceived('0.00');
      setTradeFee('0.00');
    }
  }, [fromAmount, fromToken, toToken, slippage]);

  const calculateSwapDetails = async () => {
    if (!fromAmount || parseFloat(fromAmount) <= 0) return;

    try {
      // Simulate exchange rate calculation (1:1 for simplicity)
      const inputAmount = parseFloat(fromAmount);
      const fee = inputAmount * 0.003; // 0.3% fee
      const outputAmount = inputAmount - fee;
      
      setToAmount(outputAmount.toFixed(6));
      setExchangeRate('1.00');
      setPriceImpact('0.30');
      setTradeFee(fee.toFixed(6));
      
      const slippageAmount = outputAmount * (parseFloat(slippage) / 100);
      setMinimumReceived((outputAmount - slippageAmount).toFixed(6));
    } catch (error) {
      console.error('Error calculating swap details:', error);
    }
  };

  const handleSwapTokens = () => {
    const tempToken = fromToken;
    setFromToken(toToken);
    setToToken(tempToken);
    setFromAmount(toAmount);
    setToAmount(fromAmount);
  };

  const handleMaxClick = () => {
    setFromAmount(fromToken.balance);
  };

  const handleSwap = async () => {
    if (!fromAmount || parseFloat(fromAmount) <= 0) return;
    
    try {
      await onSwap(fromToken.address, toToken.address, fromAmount);
      setFromAmount('');
      setToAmount('');
    } catch (error) {
      console.error('Swap failed:', error);
    }
  };

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance);
    if (num === 0) return '0.00';
    if (num < 0.01) return '<0.01';
    return num.toFixed(4);
  };

  const isInsufficientBalance = fromAmount && parseFloat(fromAmount) > parseFloat(fromToken.balance);
  const canSwap = isConnected && isCorrectNetwork && fromAmount && parseFloat(fromAmount) > 0 && !isInsufficientBalance;

  return (
    <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white">Swap Tokens</h2>
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="p-2 hover:bg-slate-700/50 rounded-lg transition-colors"
        >
          <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="mb-6 p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm text-slate-300">Slippage Tolerance</span>
            <div className="flex items-center space-x-2">
              {['0.1', '0.5', '1.0'].map((value) => (
                <button
                  key={value}
                  onClick={() => setSlippage(value)}
                  className={`px-3 py-1 rounded text-xs ${
                    slippage === value
                      ? 'bg-nexus-600 text-white'
                      : 'bg-slate-600 text-slate-300 hover:bg-slate-500'
                  }`}
                >
                  {value}%
                </button>
              ))}
              <input
                type="number"
                value={slippage}
                onChange={(e) => setSlippage(e.target.value)}
                className="w-16 px-2 py-1 bg-slate-600 text-white text-xs rounded border-none focus:ring-2 focus:ring-nexus-500"
                step="0.1"
                min="0.1"
                max="50"
              />
              <span className="text-xs text-slate-400">%</span>
            </div>
          </div>
        </div>
      )}

      {/* From Token */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-slate-400">From</span>
          <span className="text-sm text-slate-400">
            Balance: {formatBalance(fromToken.balance)} {fromToken.symbol}
          </span>
        </div>
        <div className="bg-slate-700/50 rounded-xl p-4 border border-slate-600/50">
          <div className="flex items-center justify-between">
            <input
              type="number"
              value={fromAmount}
              onChange={(e) => setFromAmount(e.target.value)}
              placeholder="0.00"
              className="bg-transparent text-white text-2xl font-medium placeholder-slate-500 border-none outline-none flex-1"
              step="any"
            />
            <div className="flex items-center space-x-2">
              <button
                onClick={handleMaxClick}
                className="px-2 py-1 bg-nexus-600/20 text-nexus-400 text-xs rounded hover:bg-nexus-600/30 transition-colors"
              >
                MAX
              </button>
              <div className="flex items-center space-x-2 bg-slate-600/50 rounded-lg px-3 py-2">
                <div className="w-6 h-6 bg-gradient-to-r from-nexus-400 to-nexus-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">{fromToken.symbol[0]}</span>
                </div>
                <span className="text-white font-medium">{fromToken.symbol}</span>
              </div>
            </div>
          </div>
          {isInsufficientBalance && (
            <p className="text-red-400 text-sm mt-2">Insufficient {fromToken.symbol} balance</p>
          )}
        </div>
      </div>

      {/* Swap Button */}
      <div className="flex justify-center my-4">
        <button
          onClick={handleSwapTokens}
          className="p-2 bg-slate-700/50 hover:bg-slate-600/50 rounded-lg transition-colors border border-slate-600/50"
        >
          <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
          </svg>
        </button>
      </div>

      {/* To Token */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-slate-400">To</span>
          <span className="text-sm text-slate-400">
            Balance: {formatBalance(toToken.balance)} {toToken.symbol}
          </span>
        </div>
        <div className="bg-slate-700/50 rounded-xl p-4 border border-slate-600/50">
          <div className="flex items-center justify-between">
            <input
              type="number"
              value={toAmount}
              readOnly
              placeholder="0.00"
              className="bg-transparent text-white text-2xl font-medium placeholder-slate-500 border-none outline-none flex-1"
            />
            <div className="flex items-center space-x-2 bg-slate-600/50 rounded-lg px-3 py-2">
              <div className="w-6 h-6 bg-gradient-to-r from-nexus-400 to-nexus-600 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">{toToken.symbol[0]}</span>
              </div>
              <span className="text-white font-medium">{toToken.symbol}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Swap Details */}
      {fromAmount && parseFloat(fromAmount) > 0 && (
        <div className="mb-6 p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-slate-400">Exchange Rate</span>
              <span className="text-white">1 {fromToken.symbol} = {exchangeRate} {toToken.symbol}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Price Impact</span>
              <span className="text-white">{priceImpact}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Trading Fee</span>
              <span className="text-white">{tradeFee} {fromToken.symbol}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Minimum Received</span>
              <span className="text-white">{minimumReceived} {toToken.symbol}</span>
            </div>
          </div>
        </div>
      )}

      {/* Swap Button */}
      <button
        onClick={handleSwap}
        disabled={!canSwap || loading}
        className={`w-full py-4 rounded-xl font-medium text-lg transition-all ${
          !canSwap
            ? 'bg-slate-700/50 text-slate-500 cursor-not-allowed'
            : loading
            ? 'bg-nexus-600/50 text-white cursor-wait'
            : 'bg-nexus-600 hover:bg-nexus-700 text-white hover:shadow-lg hover:shadow-nexus-500/25'
        }`}
      >
        {loading ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
            <span>Swapping...</span>
          </div>
        ) : !isConnected ? (
          'Connect Wallet'
        ) : !isCorrectNetwork ? (
          'Switch to Nexus Network'
        ) : isInsufficientBalance ? (
          `Insufficient ${fromToken.symbol} Balance`
        ) : !fromAmount || parseFloat(fromAmount) <= 0 ? (
          'Enter Amount'
        ) : (
          'Swap Tokens'
        )}
      </button>
    </div>
  );
}