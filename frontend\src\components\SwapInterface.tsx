'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';

interface Token {
  symbol: string;
  name: string;
  address: string;
  balance: string;
  decimals: number;
  disabled?: boolean;
  isNative?: boolean;
}

interface SwapInterfaceProps {
  account: string;
  isConnected: boolean;
  isCorrectNetwork: boolean;
  contractAddresses: any;
  nexBalance: string;
  tokenABalance: string;
  tokenBBalance: string;
  nxiBalance: string;
  nxyBalance: string;
  onSwap: (tokenIn: string, tokenOut: string, amount: string) => Promise<void>;
  loading: boolean;
}

export default function SwapInterface({
  account,
  isConnected,
  isCorrectNetwork,
  contractAddresses,
  nexBalance,
  tokenABalance,
  tokenBBalance,
  nxiBalance,
  nxyBalance,
  onSwap,
  loading
}: SwapInterfaceProps) {
  // Use NEX as default from token to prioritize NEX swaps
  const [fromToken, setFromToken] = useState<Token>({
    symbol: 'NEX',
    name: 'Nexus',
    address: 'native',
    balance: nexBalance,
    decimals: 18,
    isNative: true
  });
  
  const [toToken, setToToken] = useState<Token>({
    symbol: 'NXI',
    name: 'Nexus Index Token',
    address: contractAddresses.NXI || contractAddresses.TokenA,
    balance: tokenABalance,
    decimals: 18
  });

  const [fromAmount, setFromAmount] = useState('');
  const [toAmount, setToAmount] = useState('');
  const [slippage, setSlippage] = useState('0.5');
  const [priceImpact, setPriceImpact] = useState('0.00');
  const [exchangeRate, setExchangeRate] = useState('100.00');
  const [minimumReceived, setMinimumReceived] = useState('0.00');
  const [tradeFee, setTradeFee] = useState('0.00');
  const [showSettings, setShowSettings] = useState(false);
  const [showFromTokenSelector, setShowFromTokenSelector] = useState(false);
  const [showToTokenSelector, setShowToTokenSelector] = useState(false);
  const [showCustomTokenInput, setShowCustomTokenInput] = useState(false);
  const [customTokenAddress, setCustomTokenAddress] = useState('');
  const [customTokenSymbol, setCustomTokenSymbol] = useState('');
  const [customTokenName, setCustomTokenName] = useState('');
  const [customTokenBalance, setCustomTokenBalance] = useState('0');
  const [isLoadingCustomToken, setIsLoadingCustomToken] = useState(false);
  const [customTokenError, setCustomTokenError] = useState('');

  // Updated token list with NEX integration priority
  const tokens = [
    {
      symbol: 'NEX',
      name: 'Nexus',
      address: 'native',
      balance: nexBalance,
      decimals: 18,
      isNative: true
    },
    {
      symbol: 'NXI',
      name: 'Nexus Index Token',
      address: contractAddresses.NXI || contractAddresses.TokenA,
      balance: nxiBalance || tokenABalance,
      decimals: 18
    },
    {
      symbol: 'NXY',
      name: 'Nexus Yield Token',
      address: contractAddresses.NXY || contractAddresses.TokenB,
      balance: nxyBalance || tokenBBalance,
      decimals: 18
    },
    // Legacy tokens (disabled by default to prioritize NEX ecosystem)
    {
      symbol: 'TKNA',
      name: 'Token A (Legacy)',
      address: contractAddresses.TokenA,
      balance: tokenABalance,
      decimals: 18,
      disabled: true
    },
    {
      symbol: 'TKNB',
      name: 'Token B (Legacy)',
      address: contractAddresses.TokenB,
      balance: tokenBBalance,
      decimals: 18,
      disabled: true
    }
  ];

  useEffect(() => {
    if (fromAmount && parseFloat(fromAmount) > 0) {
      calculateSwapDetails();
    } else {
      setToAmount('');
      setPriceImpact('0.00');
      setMinimumReceived('0.00');
      setTradeFee('0.00');
    }
  }, [fromAmount, fromToken, toToken, slippage]);

  // Update token balances when props change
  useEffect(() => {
    setFromToken(prev => ({
      ...prev,
      balance: prev.symbol === 'NEX' ? nexBalance : 
               prev.symbol === 'NXI' ? (nxiBalance || tokenABalance) :
               prev.symbol === 'NXY' ? (nxyBalance || tokenBBalance) :
               prev.symbol === 'TKNA' ? tokenABalance : tokenBBalance
    }));
    setToToken(prev => ({
      ...prev,
      balance: prev.symbol === 'NEX' ? nexBalance : 
               prev.symbol === 'NXI' ? (nxiBalance || tokenABalance) :
               prev.symbol === 'NXY' ? (nxyBalance || tokenBBalance) :
               prev.symbol === 'TKNA' ? tokenABalance : tokenBBalance
    }));
  }, [nexBalance, tokenABalance, tokenBBalance, nxiBalance, nxyBalance]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.token-selector')) {
        setShowFromTokenSelector(false);
        setShowToTokenSelector(false);
        resetCustomTokenForm();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const calculateSwapDetails = async () => {
    if (!fromAmount || parseFloat(fromAmount) <= 0) return;

    try {
      const inputAmount = parseFloat(fromAmount);
      let outputAmount = 0;
      let rate = '1.00';
      let fee = 0;

      // Calculate based on swap type
      if (fromToken.symbol === 'NEX' && toToken.symbol === 'NXI') {
        // NEX to NXI: 1 NEX = 100 NXI
        outputAmount = inputAmount * 100;
        rate = '100.00';
        fee = 0; // No fee for NEX to token swaps
      } else if (fromToken.symbol === 'NEX' && toToken.symbol === 'NXY') {
        // NEX to NXY: 1 NEX = 150 NXY
        outputAmount = inputAmount * 150;
        rate = '150.00';
        fee = 0; // No fee for NEX to token swaps
      } else if (fromToken.symbol === 'NXI' && toToken.symbol === 'NXY') {
        // NXI to NXY: 1 NXI = 1.2 NXY (with 0.3% fee)
        const grossOutput = inputAmount * 1.2;
        fee = grossOutput * 0.003; // 0.3% fee
        outputAmount = grossOutput - fee;
        rate = '1.20';
      } else if (fromToken.symbol === 'NXY' && toToken.symbol === 'NXI') {
        // NXY to NXI: 1 NXY = 0.8 NXI (with 0.3% fee)
        const grossOutput = inputAmount * 0.8;
        fee = grossOutput * 0.003; // 0.3% fee
        outputAmount = grossOutput - fee;
        rate = '0.80';
      } else {
        // Legacy 1:1 swaps
        fee = inputAmount * 0.003; // 0.3% fee
        outputAmount = inputAmount - fee;
        rate = '1.00';
      }
      
      setToAmount(outputAmount.toFixed(6));
      setExchangeRate(rate);
      setPriceImpact(fee > 0 ? '0.30' : '0.00');
      setTradeFee(fee.toFixed(6));
      
      const slippageAmount = outputAmount * (parseFloat(slippage) / 100);
      setMinimumReceived((outputAmount - slippageAmount).toFixed(6));
    } catch (error) {
      console.error('Error calculating swap details:', error);
    }
  };

  const handleSwapTokens = () => {
    const tempToken = fromToken;
    setFromToken(toToken);
    setToToken(tempToken);
    setFromAmount(toAmount);
    setToAmount(fromAmount);
  };

  const handleSelectFromToken = (token: Token) => {
    if (token.disabled) return;
    if (token.address !== toToken.address) {
      setFromToken(token);
      setFromAmount('');
      setToAmount('');
    }
    setShowFromTokenSelector(false);
  };

  const handleSelectToToken = (token: Token) => {
    if (token.disabled) return;
    if (token.address !== fromToken.address) {
      setToToken(token);
      setFromAmount('');
      setToAmount('');
    }
    setShowToTokenSelector(false);
  };

  const loadCustomToken = async (address: string) => {
    if (!address || !ethers.isAddress(address)) {
      setCustomTokenError('Invalid contract address');
      return;
    }

    setIsLoadingCustomToken(true);
    setCustomTokenError('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      
      const tokenContract = new ethers.Contract(address, [
        "function symbol() external view returns (string)",
        "function name() external view returns (string)",
        "function decimals() external view returns (uint8)",
        "function balanceOf(address owner) external view returns (uint256)"
      ], provider);

      const [symbol, name, decimals, balance] = await Promise.all([
        tokenContract.symbol(),
        tokenContract.name(),
        tokenContract.decimals(),
        account ? tokenContract.balanceOf(account) : 0
      ]);

      const formattedBalance = ethers.formatUnits(balance, decimals);

      setCustomTokenSymbol(symbol);
      setCustomTokenName(name);
      setCustomTokenBalance(formattedBalance);

    } catch (error) {
      console.error('Error loading custom token:', error);
      setCustomTokenError('Failed to load token. Please check the contract address.');
    } finally {
      setIsLoadingCustomToken(false);
    }
  };

  const addCustomToken = () => {
    if (!customTokenAddress || !customTokenSymbol) return;

    const customToken: Token = {
      symbol: customTokenSymbol,
      name: customTokenName || customTokenSymbol,
      address: customTokenAddress,
      balance: customTokenBalance,
      decimals: 18
    };

    if (showFromTokenSelector) {
      setFromToken(customToken);
      setFromAmount('');
      setToAmount('');
      setShowFromTokenSelector(false);
    }

    resetCustomTokenForm();
  };

  const resetCustomTokenForm = () => {
    setCustomTokenAddress('');
    setCustomTokenSymbol('');
    setCustomTokenName('');
    setCustomTokenBalance('0');
    setCustomTokenError('');
  };

  const getAvailableTokensForFrom = () => {
    return tokens;
  };

  const getAvailableTokensForTo = () => {
    return tokens.filter(token => token.address !== fromToken.address);
  };

  const handleMaxClick = () => {
    setFromAmount(fromToken.balance);
  };

  const handleSwap = async () => {
    if (!fromAmount || parseFloat(fromAmount) <= 0) return;
    
    try {
      await onSwap(fromToken.address, toToken.address, fromAmount);
      setFromAmount('');
      setToAmount('');
    } catch (error) {
      console.error('Swap failed:', error);
    }
  };

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance);
    if (num === 0) return '0.00';
    if (num < 0.01) return '<0.01';
    return num.toFixed(4);
  };

  const isInsufficientBalance = fromAmount && parseFloat(fromAmount) > parseFloat(fromToken.balance);
  const canSwap = isConnected && isCorrectNetwork && fromAmount && parseFloat(fromAmount) > 0 && !isInsufficientBalance;

  // Check if this is a NEX-based swap
  const isNEXSwap = fromToken.symbol === 'NEX' || toToken.symbol === 'NEX';
  const swapTypeMessage = () => {
    if (fromToken.symbol === 'NEX' && toToken.symbol === 'NXI') {
      return `Direct NEX → NXI swap (Rate: 1 NEX = 100 NXI)`;
    } else if (fromToken.symbol === 'NEX' && toToken.symbol === 'NXY') {
      return `Direct NEX → NXY swap (Rate: 1 NEX = 150 NXY)`;
    } else if (fromToken.symbol === 'NXI' && toToken.symbol === 'NXY') {
      return `NXI → NXY swap through NexusTokenSwap (Rate: 1 NXI = 1.2 NXY)`;
    } else if (fromToken.symbol === 'NXY' && toToken.symbol === 'NXI') {
      return `NXY → NXI swap through NexusTokenSwap (Rate: 1 NXY = 0.8 NXI)`;
    }
    return `Token swap (Rate: 1:1)`;
  };

  return (
    <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-bold text-white">Nexus Token Swap</h2>
          <p className="text-sm text-slate-400 mt-1">
            {isNEXSwap ? '🔥 NEX Integration Active' : 'Token-to-Token Swap'}
          </p>
        </div>
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="p-2 hover:bg-slate-700/50 rounded-lg transition-colors"
        >
          <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>
      </div>

      {/* NEX Integration Info */}
      {isNEXSwap && (
        <div className="mb-4 p-3 bg-gradient-to-r from-nexus-500/20 to-nexus-600/20 border border-nexus-500/30 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-nexus-400 rounded-full animate-pulse"></div>
            <span className="text-nexus-300 text-sm font-medium">
              {swapTypeMessage()}
            </span>
          </div>
          {fromToken.symbol === 'NEX' && (
            <p className="text-nexus-200 text-xs mt-1">
              ✨ No fees for NEX swaps! Tokens are minted directly from the contract.
            </p>
          )}
        </div>
      )}

      {/* Settings Panel */}
      {showSettings && (
        <div className="mb-6 p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm text-slate-300">Slippage Tolerance</span>
            <div className="flex items-center space-x-2">
              {['0.1', '0.5', '1.0'].map((value) => (
                <button
                  key={value}
                  onClick={() => setSlippage(value)}
                  className={`px-3 py-1 rounded text-xs ${
                    slippage === value
                      ? 'bg-nexus-600 text-white'
                      : 'bg-slate-600 text-slate-300 hover:bg-slate-500'
                  }`}
                >
                  {value}%
                </button>
              ))}
              <input
                type="number"
                value={slippage}
                onChange={(e) => setSlippage(e.target.value)}
                className="w-16 px-2 py-1 bg-slate-600 text-white text-xs rounded border-none focus:ring-2 focus:ring-nexus-500"
                step="0.1"
                min="0.1"
                max="50"
              />
              <span className="text-xs text-slate-400">%</span>
            </div>
          </div>
        </div>
      )}

      {/* From Token */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-slate-400">From</span>
          <span className="text-sm text-slate-400">
            Balance: {formatBalance(fromToken.balance)} {fromToken.symbol}
          </span>
        </div>
        <div className={`rounded-xl p-4 border ${
          fromToken.symbol === 'NEX' 
            ? 'bg-gradient-to-r from-nexus-500/10 to-nexus-600/10 border-nexus-500/30' 
            : 'bg-slate-700/50 border-slate-600/50'
        }`}>
          <div className="flex items-center justify-between">
            <input
              type="number"
              value={fromAmount}
              onChange={(e) => setFromAmount(e.target.value)}
              placeholder="0.00"
              className="bg-transparent text-white text-2xl font-medium placeholder-slate-500 border-none outline-none flex-1"
              step="any"
            />
            <div className="flex items-center space-x-2">
              <button
                onClick={handleMaxClick}
                className="px-2 py-1 bg-nexus-600/20 text-nexus-400 text-xs rounded hover:bg-nexus-600/30 transition-colors"
              >
                MAX
              </button>
              <div className="relative token-selector">
                <button
                  onClick={() => setShowFromTokenSelector(!showFromTokenSelector)}
                  className="flex items-center space-x-2 bg-slate-600/50 rounded-lg px-3 py-2 hover:bg-slate-600/70 transition-colors"
                >
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    fromToken.symbol === 'NEX' 
                      ? 'bg-gradient-to-r from-nexus-400 to-nexus-600' 
                      : fromToken.symbol === 'NXI'
                      ? 'bg-gradient-to-r from-blue-400 to-blue-600'
                      : fromToken.symbol === 'NXY'
                      ? 'bg-gradient-to-r from-green-400 to-green-600'
                      : 'bg-gradient-to-r from-gray-400 to-gray-600'
                  }`}>
                    <span className="text-white text-xs font-bold">{fromToken.symbol[0]}</span>
                  </div>
                  <span className="text-white font-medium">{fromToken.symbol}</span>
                  <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {/* From Token Dropdown */}
                {showFromTokenSelector && (
                  <div className="absolute right-0 top-full mt-2 w-64 bg-slate-700 border border-slate-600 rounded-lg shadow-xl z-50">
                    <div className="p-3 border-b border-slate-600">
                      <h3 className="text-sm font-medium text-white">Select Token</h3>
                      <p className="text-xs text-slate-400 mt-1">NEX swaps have priority and no fees</p>
                    </div>

                    <div className="max-h-60 overflow-y-auto">
                      {getAvailableTokensForFrom().map((token) => (
                        <button
                          key={token.address}
                          onClick={() => handleSelectFromToken(token)}
                          disabled={token.disabled}
                          className={`w-full flex items-center justify-between p-3 transition-colors ${
                            token.disabled 
                              ? 'opacity-50 cursor-not-allowed bg-slate-800/50' 
                              : token.symbol === 'NEX'
                              ? 'hover:bg-nexus-600/20 border-l-2 border-nexus-500'
                              : 'hover:bg-slate-600/50'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              token.symbol === 'NEX' 
                                ? 'bg-gradient-to-r from-nexus-400 to-nexus-600' 
                                : token.symbol === 'NXI'
                                ? 'bg-gradient-to-r from-blue-400 to-blue-600'
                                : token.symbol === 'NXY'
                                ? 'bg-gradient-to-r from-green-400 to-green-600'
                                : 'bg-gradient-to-r from-gray-400 to-gray-600'
                            }`}>
                              <span className="text-white text-sm font-bold">{token.symbol[0]}</span>
                            </div>
                            <div className="text-left">
                              <div className="flex items-center space-x-1">
                                <p className={`font-medium ${token.disabled ? 'text-slate-500' : 'text-white'}`}>
                                  {token.symbol}
                                </p>
                                {token.symbol === 'NEX' && (
                                  <span className="text-xs bg-nexus-500 text-white px-1 rounded">PRIORITY</span>
                                )}
                                {token.disabled && (
                                  <span className="text-xs bg-slate-600 text-slate-400 px-1 rounded">LEGACY</span>
                                )}
                              </div>
                              <p className="text-slate-400 text-xs">{token.name}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className={`text-sm ${token.disabled ? 'text-slate-500' : 'text-white'}`}>
                              {formatBalance(token.balance)}
                            </p>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          {isInsufficientBalance && (
            <p className="text-red-400 text-sm mt-2">Insufficient {fromToken.symbol} balance</p>
          )}
        </div>
      </div>

      {/* Swap Button */}
      <div className="flex justify-center my-4">
        <button
          onClick={handleSwapTokens}
          className="p-2 bg-slate-700/50 hover:bg-slate-600/50 rounded-lg transition-colors border border-slate-600/50"
        >
          <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
          </svg>
        </button>
      </div>

      {/* To Token */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-slate-400">To</span>
          <span className="text-sm text-slate-400">
            Balance: {formatBalance(toToken.balance)} {toToken.symbol}
          </span>
        </div>
        <div className={`rounded-xl p-4 border ${
          toToken.symbol === 'NEX' 
            ? 'bg-gradient-to-r from-nexus-500/10 to-nexus-600/10 border-nexus-500/30' 
            : 'bg-slate-700/50 border-slate-600/50'
        }`}>
          <div className="flex items-center justify-between">
            <input
              type="number"
              value={toAmount}
              readOnly
              placeholder="0.00"
              className="bg-transparent text-white text-2xl font-medium placeholder-slate-500 border-none outline-none flex-1"
            />
            <div className="relative token-selector">
              <button
                onClick={() => setShowToTokenSelector(!showToTokenSelector)}
                className="flex items-center space-x-2 bg-slate-600/50 rounded-lg px-3 py-2 hover:bg-slate-600/70 transition-colors"
              >
                <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                  toToken.symbol === 'NEX' 
                    ? 'bg-gradient-to-r from-nexus-400 to-nexus-600' 
                    : toToken.symbol === 'NXI'
                    ? 'bg-gradient-to-r from-blue-400 to-blue-600'
                    : toToken.symbol === 'NXY'
                    ? 'bg-gradient-to-r from-green-400 to-green-600'
                    : 'bg-gradient-to-r from-gray-400 to-gray-600'
                }`}>
                  <span className="text-white text-xs font-bold">{toToken.symbol[0]}</span>
                </div>
                <span className="text-white font-medium">{toToken.symbol}</span>
                <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              {/* To Token Dropdown */}
              {showToTokenSelector && (
                <div className="absolute right-0 top-full mt-2 w-64 bg-slate-700 border border-slate-600 rounded-lg shadow-xl z-50">
                  <div className="p-3 border-b border-slate-600">
                    <h3 className="text-sm font-medium text-white">Select Token</h3>
                  </div>
                  <div className="max-h-60 overflow-y-auto">
                    {getAvailableTokensForTo().map((token) => (
                      <button
                        key={token.address}
                        onClick={() => handleSelectToToken(token)}
                        disabled={token.disabled}
                        className={`w-full flex items-center justify-between p-3 transition-colors ${
                          token.disabled 
                            ? 'opacity-50 cursor-not-allowed bg-slate-800/50' 
                            : token.symbol === 'NEX'
                            ? 'hover:bg-nexus-600/20 border-l-2 border-nexus-500'
                            : 'hover:bg-slate-600/50'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            token.symbol === 'NEX' 
                              ? 'bg-gradient-to-r from-nexus-400 to-nexus-600' 
                              : token.symbol === 'NXI'
                              ? 'bg-gradient-to-r from-blue-400 to-blue-600'
                              : token.symbol === 'NXY'
                              ? 'bg-gradient-to-r from-green-400 to-green-600'
                              : 'bg-gradient-to-r from-gray-400 to-gray-600'
                          }`}>
                            <span className="text-white text-sm font-bold">{token.symbol[0]}</span>
                          </div>
                          <div className="text-left">
                            <div className="flex items-center space-x-1">
                              <p className={`font-medium ${token.disabled ? 'text-slate-500' : 'text-white'}`}>
                                {token.symbol}
                              </p>
                              {token.symbol === 'NEX' && (
                                <span className="text-xs bg-nexus-500 text-white px-1 rounded">PRIORITY</span>
                              )}
                              {token.disabled && (
                                <span className="text-xs bg-slate-600 text-slate-400 px-1 rounded">LEGACY</span>
                              )}
                            </div>
                            <p className="text-slate-400 text-xs">{token.name}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`text-sm ${token.disabled ? 'text-slate-500' : 'text-white'}`}>
                            {formatBalance(token.balance)}
                          </p>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Swap Details */}
      {fromAmount && parseFloat(fromAmount) > 0 && (
        <div className="mb-6 p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-slate-400">Exchange Rate</span>
              <span className="text-white">1 {fromToken.symbol} = {exchangeRate} {toToken.symbol}</span>
            </div>
            {parseFloat(tradeFee) > 0 && (
              <div className="flex justify-between">
                <span className="text-slate-400">Trade Fee (0.3%)</span>
                <span className="text-white">{tradeFee} {toToken.symbol}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-slate-400">Price Impact</span>
              <span className={`${parseFloat(priceImpact) > 1 ? 'text-yellow-400' : 'text-green-400'}`}>
                {priceImpact}%
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Minimum Received</span>
              <span className="text-white">{minimumReceived} {toToken.symbol}</span>
            </div>
          </div>
        </div>
      )}

      {/* Swap Button */}
      <button
        onClick={handleSwap}
        disabled={!canSwap || loading}
        className={`w-full py-4 rounded-xl font-medium text-lg transition-all ${
          canSwap && !loading
            ? isNEXSwap
              ? 'bg-gradient-to-r from-nexus-500 to-nexus-600 hover:from-nexus-600 hover:to-nexus-700 text-white shadow-lg shadow-nexus-500/25'
              : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white'
            : 'bg-slate-600 text-slate-400 cursor-not-allowed'
        }`}
      >
        {loading ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
            <span>Processing...</span>
          </div>
        ) : !isConnected ? (
          'Connect Wallet'
        ) : !isCorrectNetwork ? (
          'Switch to Nexus Network'
        ) : isInsufficientBalance ? (
          `Insufficient ${fromToken.symbol} Balance`
        ) : !fromAmount || parseFloat(fromAmount) <= 0 ? (
          'Enter Amount'
        ) : isNEXSwap ? (
          `🔥 Swap ${fromToken.symbol} for ${toToken.symbol}`
        ) : (
          `Swap ${fromToken.symbol} for ${toToken.symbol}`
        )}
      </button>

      {/* NEX Integration Notice */}
      {!isNEXSwap && (
        <div className="mt-4 p-3 bg-nexus-500/10 border border-nexus-500/30 rounded-lg">
          <p className="text-nexus-300 text-sm text-center">
            💡 Try swapping with NEX for better rates and no fees!
          </p>
        </div>
      )}
    </div>
  );
}