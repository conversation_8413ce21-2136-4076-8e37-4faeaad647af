{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/restore-reducer.ts"], "names": ["restoreReducer", "state", "action", "url", "tree", "href", "createHrefFromUrl", "extractPathFromFlightRouterState", "buildId", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "cache", "prefetchCache", "nextUrl", "pathname"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;mCARkB;oCAMe;AAE1C,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE,GAAGF;IACtB,MAAMG,OAAOC,IAAAA,oCAAiB,EAACH;QAiBpBI;IAfX,OAAO;QACLC,SAASP,MAAMO,OAAO;QACtB,oBAAoB;QACpBC,cAAcJ;QACdK,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,6FAA6F;YAC7FC,4BAA4B;QAC9B;QACAC,mBAAmBb,MAAMa,iBAAiB;QAC1CC,OAAOd,MAAMc,KAAK;QAClBC,eAAef,MAAMe,aAAa;QAClC,wBAAwB;QACxBZ,MAAMA;QACNa,SAASV,CAAAA,oCAAAA,IAAAA,oDAAgC,EAACH,iBAAjCG,oCAA0CJ,IAAIe,QAAQ;IACjE;AACF"}