'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// Contract ABIs - Updated with complete function signatures
const TokenA_ABI = [
  "function balanceOf(address owner) external view returns (uint256)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
  "function symbol() external view returns (string)",
  "function decimals() external view returns (uint8)",
  "function name() external view returns (string)",
  "function totalSupply() external view returns (uint256)",
  "function transfer(address to, uint256 amount) external returns (bool)",
  "function transferFrom(address from, address to, uint256 amount) external returns (bool)"
];

const TokenB_ABI = [
  "function balanceOf(address owner) external view returns (uint256)",
  "function symbol() external view returns (string)",
  "function decimals() external view returns (uint8)",
  "function name() external view returns (string)",
  "function totalSupply() external view returns (uint256)"
];

const TokenSwap_ABI = [
  "function swap(uint256 amount) external returns (bool)",
  "function getTokenBLiquidity() external view returns (uint256)",
  "function getTokenABalance() external view returns (uint256)",
  "function tokenA() external view returns (address)",
  "function tokenB() external view returns (address)",
  "event Swap(address indexed user, uint256 amount)"
];

// Nexus network configuration
const NEXUS_NETWORK = {
  chainId: '0xF64', // 3940 in hex
  chainName: 'Nexus Testnet III',
  nativeCurrency: {
    name: 'NEX',
    symbol: 'NEX',
    decimals: 18,
  },
  rpcUrls: ['https://testnet3.rpc.nexus.xyz'],
  blockExplorerUrls: ['https://explorer.nexus.xyz'],
};

// Default contract addresses (will be replaced with deployed addresses)
const DEFAULT_ADDRESSES = {
  TokenA: '0x0000000000000000000000000000000000000000',
  TokenB: '0x0000000000000000000000000000000000000000',
  TokenSwap: '0x0000000000000000000000000000000000000000',
};

interface ContractAddresses {
  TokenA: string;
  TokenB: string;
  TokenSwap: string;
  network?: string;
  chainId?: number;
  deployer?: string;
  deploymentBlock?: number;
  timestamp?: string;
}

export default function Home() {
  const [account, setAccount] = useState<string>('');
  const [tokenABalance, setTokenABalance] = useState<string>('0');
  const [tokenBBalance, setTokenBBalance] = useState<string>('0');
  const [swapAmount, setSwapAmount] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');
  const [contractAddresses, setContractAddresses] = useState<ContractAddresses>(DEFAULT_ADDRESSES);
  const [liquidity, setLiquidity] = useState<string>('0');
  const [isCorrectNetwork, setIsCorrectNetwork] = useState<boolean>(false);

  useEffect(() => {
    checkIfWalletIsConnected();
    loadContractAddresses();
    setupEventListeners();
  }, []);

  useEffect(() => {
    if (account && isCorrectNetwork) {
      updateBalances();
      updateLiquidity();
    }
  }, [account, contractAddresses, isCorrectNetwork]);

  function setupEventListeners() {
    const { ethereum } = window as any;
    if (!ethereum) return;

    // Listen for account changes
    ethereum.on('accountsChanged', (accounts: string[]) => {
      if (accounts.length > 0) {
        setAccount(accounts[0]);
        checkNetwork();
      } else {
        setAccount('');
        setIsCorrectNetwork(false);
      }
    });

    // Listen for network changes
    ethereum.on('chainChanged', (chainId: string) => {
      console.log('Network changed to:', chainId);
      checkNetwork();
      // Reload page to reset state
      window.location.reload();
    });

    // Cleanup function
    return () => {
      if (ethereum.removeListener) {
        ethereum.removeListener('accountsChanged', () => {});
        ethereum.removeListener('chainChanged', () => {});
      }
    };
  }

  async function loadContractAddresses() {
    try {
      // Try to load deployed addresses from public folder
      const response = await fetch('/deployedAddresses.json');
      if (response.ok) {
        const addresses = await response.json();
        setContractAddresses(addresses);
        console.log('Loaded contract addresses:', addresses);
      } else {
        console.warn('Could not load deployed addresses, using defaults');
      }
    } catch (error) {
      console.warn('Could not load deployed addresses:', error);
    }
  }

  async function checkIfWalletIsConnected() {
    try {
      const { ethereum } = window as any;
      if (!ethereum) {
        setError('MetaMask tidak terdeteksi. Silakan install MetaMask.');
        return;
      }

      const accounts = await ethereum.request({ method: 'eth_accounts' });
      if (accounts.length > 0) {
        setAccount(accounts[0]);
        await checkNetwork();
      }
    } catch (error) {
      console.error('Error checking wallet connection:', error);
      setError('Error saat mengecek koneksi wallet');
    }
  }

  async function checkNetwork() {
    try {
      const { ethereum } = window as any;
      if (!ethereum) {
        setIsCorrectNetwork(false);
        return;
      }

      const chainId = await ethereum.request({ method: 'eth_chainId' });
      console.log('Current chainId:', chainId, 'Expected:', NEXUS_NETWORK.chainId);

      // Convert both to same format for comparison
      const currentChainId = parseInt(chainId, 16);
      const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);

      if (currentChainId === expectedChainId) {
        setIsCorrectNetwork(true);
        setError('');
        console.log('✅ Connected to correct network');
      } else {
        setIsCorrectNetwork(false);
        setError(`Wrong network. Current: ${currentChainId}, Expected: ${expectedChainId} (Nexus Testnet III)`);
        console.log('❌ Wrong network detected');
      }
    } catch (error) {
      console.error('Error checking network:', error);
      setIsCorrectNetwork(false);
      setError('Error checking network');
    }
  }

  async function switchToNexusNetwork() {
    try {
      const { ethereum } = window as any;
      setError('');
      setSuccess('Switching to Nexus network...');

      try {
        await ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: NEXUS_NETWORK.chainId }],
        });
        console.log('✅ Network switch requested');
      } catch (switchError: any) {
        console.log('Switch error code:', switchError.code);
        // Network belum ditambahkan, tambahkan dulu
        if (switchError.code === 4902) {
          console.log('Adding Nexus network...');
          await ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [NEXUS_NETWORK],
          });
          console.log('✅ Network added');
        } else {
          throw switchError;
        }
      }

      // Wait a bit for network to switch
      setTimeout(async () => {
        await checkNetwork();
        setSuccess('');
      }, 1000);

    } catch (error: any) {
      console.error('Error switching network:', error);
      setError('Gagal switch ke Nexus network: ' + (error.message || 'Unknown error'));
      setSuccess('');
    }
  }

  async function connectWallet() {
    try {
      const { ethereum } = window as any;
      if (!ethereum) {
        setError('MetaMask tidak terdeteksi. Silakan install MetaMask.');
        return;
      }

      const accounts = await ethereum.request({ method: 'eth_requestAccounts' });
      setAccount(accounts[0]);
      await checkNetwork();
      setError('');
      setSuccess('Wallet berhasil terhubung!');
    } catch (error) {
      console.error('Error connecting wallet:', error);
      setError('Gagal menghubungkan wallet');
    }
  }

  async function updateBalances() {
    if (!account || !isCorrectNetwork) return;

    // Check if contract addresses are valid
    if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {
      console.log('Contract addresses not loaded yet, skipping balance update');
      return;
    }

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);

      console.log('Updating balances for:', account);
      console.log('TokenA address:', contractAddresses.TokenA);
      console.log('TokenB address:', contractAddresses.TokenB);

      const tokenAContract = new ethers.Contract(contractAddresses.TokenA, TokenA_ABI, provider);
      const tokenBContract = new ethers.Contract(contractAddresses.TokenB, TokenB_ABI, provider);

      // Test if contracts exist
      try {
        const [balanceA, balanceB] = await Promise.all([
          tokenAContract.balanceOf(account),
          tokenBContract.balanceOf(account)
        ]);

        console.log('Raw balanceA:', balanceA.toString());
        console.log('Raw balanceB:', balanceB.toString());

        setTokenABalance(ethers.formatEther(balanceA));
        setTokenBBalance(ethers.formatEther(balanceB));

        console.log('Balances updated successfully');
      } catch (contractError) {
        console.error('Contract call error:', contractError);
        setError('Error reading token balances. Contracts may not be deployed correctly.');
      }
    } catch (error) {
      console.error('Error updating balances:', error);
      setError('Error connecting to contracts');
    }
  }

  async function updateLiquidity() {
    if (!isCorrectNetwork) return;

    // Check if contract addresses are valid
    if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {
      console.log('TokenSwap address not loaded yet, skipping liquidity update');
      return;
    }

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);

      console.log('Updating liquidity for TokenSwap:', contractAddresses.TokenSwap);

      const swapContract = new ethers.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);

      try {
        const liquidityAmount = await swapContract.getTokenBLiquidity();
        console.log('Raw liquidity:', liquidityAmount.toString());

        setLiquidity(ethers.formatEther(liquidityAmount));
        console.log('Liquidity updated successfully');
      } catch (contractError) {
        console.error('TokenSwap contract call error:', contractError);
        setError('Error reading liquidity. TokenSwap contract may not be deployed correctly.');
      }
    } catch (error) {
      console.error('Error updating liquidity:', error);
      setError('Error connecting to TokenSwap contract');
    }
  }

  async function handleSwap() {
    if (!swapAmount || !account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const tokenAContract = new ethers.Contract(contractAddresses.TokenA, TokenA_ABI, signer);
      const swapContract = new ethers.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);

      const amount = ethers.parseEther(swapAmount);

      // Check balance
      const balance = await tokenAContract.balanceOf(account);
      if (balance < amount) {
        setError('Saldo Token A tidak mencukupi');
        return;
      }

      // Check liquidity
      const availableLiquidity = await swapContract.getTokenBLiquidity();
      if (availableLiquidity < amount) {
        setError('Likuiditas Token B tidak mencukupi');
        return;
      }

      // Check allowance
      const allowance = await tokenAContract.allowance(account, contractAddresses.TokenSwap);

      if (allowance < amount) {
        setSuccess('Menyetujui penggunaan Token A...');
        const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, amount);
        await approveTx.wait();
        setSuccess('Approval berhasil! Melakukan swap...');
      } else {
        setSuccess('Melakukan swap...');
      }

      // Perform swap
      const swapTx = await swapContract.swap(amount);
      await swapTx.wait();

      setSuccess('Swap berhasil! 🎉');
      setSwapAmount('');

      // Update balances and liquidity
      await updateBalances();
      await updateLiquidity();

    } catch (error: any) {
      console.error('Error during swap:', error);
      if (error.code === 'ACTION_REJECTED') {
        setError('Transaksi dibatalkan oleh user');
      } else if (error.message.includes('insufficient funds')) {
        setError('Saldo NEX tidak mencukupi untuk gas fee');
      } else {
        setError('Swap gagal: ' + (error.reason || error.message));
      }
    } finally {
      setLoading(false);
    }
  }

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const clearMessages = () => {
    setError('');
    setSuccess('');
  };

  const isDeployerAccount = account && contractAddresses.deployer &&
    account.toLowerCase() === contractAddresses.deployer.toLowerCase();

  async function requestTestTokens() {
    if (!account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');
    setSuccess('Requesting test tokens...');

    try {
      // Check if user is the deployer
      if (isDeployerAccount) {
        setSuccess('You are the deployer! You already have all tokens 🎉');
        await updateBalances();
        return;
      }

      // For non-deployer users, show instructions
      setError('');
      setSuccess('');
      alert(`To get test tokens:

1. Switch to deployer account: ${contractAddresses.deployer}
2. Or ask the deployer to send you tokens
3. Or use a faucet if available

Your current address: ${account}
Deployer address: ${contractAddresses.deployer}

The deployer has 1,000,000 Token A available for distribution.`);

    } catch (error: any) {
      console.error('Error requesting test tokens:', error);
      setError('Failed to get test tokens: ' + (error.reason || error.message));
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 relative">
      {/* Header */}
      <div className="absolute top-6 right-6 flex flex-col items-end space-y-2">
        {account && (
          <div className="text-sm text-gray-600">
            <span className="font-medium">Connected:</span> {formatAddress(account)}
          </div>
        )}
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isCorrectNetwork ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-sm text-gray-600">
            {isCorrectNetwork ? 'Nexus Testnet III' : 'Wrong Network'}
          </span>
        </div>
        {/* Debug info */}
        <div className="text-xs text-gray-400 max-w-xs">
          <div>Expected Chain: 3940 (0xF64)</div>
          <div>Contracts: {contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? '✅' : '❌'}</div>
          <div>Deployer: {contractAddresses.deployer ? contractAddresses.deployer.slice(0, 8) + '...' : 'N/A'}</div>
          <div>Your Address: {account ? account.slice(0, 8) + '...' : 'N/A'}</div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col items-center justify-center min-h-screen px-4">
        <div className="text-center mb-12">
          <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Nexus Swap
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Decentralized token exchange on Nexus blockchain. Swap Token A for Token B at a fixed 1:1 ratio.
          </p>
        </div>

        {/* Error/Success Messages */}
        {(error || success) && (
          <div className="w-full max-w-md mb-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between">
                <span>{error}</span>
                <button type="button" onClick={clearMessages} className="text-red-500 hover:text-red-700">
                  ✕
                </button>
              </div>
            )}
            {success && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between">
                <span>{success}</span>
                <button type="button" onClick={clearMessages} className="text-green-500 hover:text-green-700">
                  ✕
                </button>
              </div>
            )}
          </div>
        )}

        {!account ? (
          <div className="text-center">
            <button
              type="button"
              onClick={connectWallet}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              Connect Wallet
            </button>
            <p className="text-gray-500 mt-4">
              Connect your MetaMask wallet to start swapping tokens
            </p>
          </div>
        ) : !isCorrectNetwork ? (
          <div className="text-center">
            <div className="space-y-4">
              <button
                type="button"
                onClick={switchToNexusNetwork}
                className="bg-orange-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Switch to Nexus Network
              </button>
              <div className="text-center">
                <button
                  type="button"
                  onClick={checkNetwork}
                  className="text-sm text-blue-600 hover:text-blue-800 underline"
                >
                  Already switched? Click to refresh
                </button>
              </div>
            </div>
            <p className="text-gray-500 mt-4">
              Please switch to Nexus Testnet III to continue
            </p>
          </div>
        ) : (
          <div className="w-full max-w-lg">
            {/* Swap Interface */}
            <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-200">
              {/* Balances */}
              <div className="grid grid-cols-2 gap-6 mb-8">
                <div className="text-center p-4 bg-blue-50 rounded-xl">
                  <p className="text-sm text-gray-600 mb-1">Token A Balance</p>
                  <p className="font-mono text-2xl font-bold text-blue-600">
                    {parseFloat(tokenABalance).toFixed(4)}
                  </p>
                  <p className="text-xs text-gray-500">TKNA</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-xl">
                  <p className="text-sm text-gray-600 mb-1">Token B Balance</p>
                  <p className="font-mono text-2xl font-bold text-purple-600">
                    {parseFloat(tokenBBalance).toFixed(4)}
                  </p>
                  <p className="text-xs text-gray-500">TKNB</p>
                </div>
              </div>

              {/* Liquidity Info */}
              <div className="text-center mb-6 p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">Available Liquidity</p>
                <p className="font-mono text-lg font-semibold text-gray-800">
                  {parseFloat(liquidity).toFixed(2)} Token B
                </p>
              </div>

              {/* Test Tokens Info */}
              {parseFloat(tokenABalance) === 0 && (
                <div className="text-center mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h3 className="text-sm font-semibold text-yellow-800 mb-2">Need Test Tokens?</h3>
                  {isDeployerAccount ? (
                    <div>
                      <p className="text-xs text-yellow-700 mb-3">
                        You are the deployer! You have 1,000,000 Token A available.
                      </p>
                      <button
                        type="button"
                        onClick={updateBalances}
                        className="bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200"
                      >
                        Refresh Balance
                      </button>
                    </div>
                  ) : (
                    <div>
                      <p className="text-xs text-yellow-700 mb-3">
                        To get test tokens, switch to the deployer account or ask for a transfer:
                      </p>
                      <div className="text-xs text-yellow-600 mb-3 font-mono bg-yellow-100 p-2 rounded">
                        Deployer: {contractAddresses.deployer?.slice(0, 20)}...
                      </div>
                      <button
                        type="button"
                        onClick={requestTestTokens}
                        disabled={loading}
                        className="bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200 disabled:bg-gray-400"
                      >
                        Show Instructions
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Swap Input */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Swap Amount (Token A → Token B)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={swapAmount}
                    onChange={(e) => setSwapAmount(e.target.value)}
                    placeholder="0.0"
                    className="w-full bg-gray-50 border border-gray-300 px-4 py-4 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-lg"
                    disabled={loading}
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
                    TKNA
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Exchange rate: 1 TKNA = 1 TKNB
                </p>
              </div>

              {/* Swap Button */}
              <button
                type="button"
                onClick={handleSwap}
                disabled={loading || !swapAmount || parseFloat(swapAmount) <= 0}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Processing...
                  </div>
                ) : (
                  'Swap Tokens'
                )}
              </button>

              {/* Contract Info */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <p className="text-xs text-gray-500 text-center">
                  Smart contracts deployed on Nexus Testnet III
                </p>
                <div className="text-xs text-gray-400 mt-2 space-y-1">
                  <div>TokenA: {formatAddress(contractAddresses.TokenA)}</div>
                  <div>TokenB: {formatAddress(contractAddresses.TokenB)}</div>
                  <div>Swap: {formatAddress(contractAddresses.TokenSwap)}</div>
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="mt-8 text-center text-gray-600">
              <h3 className="font-semibold mb-2">How to use:</h3>
              <ol className="text-sm space-y-1">
                <li>1. Make sure you have Token A in your wallet</li>
                <li>2. Enter the amount you want to swap</li>
                <li>3. Click "Swap Tokens" and confirm the transaction</li>
                <li>4. Wait for confirmation and see your new Token B balance</li>
              </ol>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
