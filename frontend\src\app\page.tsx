'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import Header from '../components/Header';
import HeroSection from '../components/HeroSection';
import StatsSection from '../components/StatsSection';
import Footer from '../components/Footer';
import SwapInterface from '../components/SwapInterface';
import LiquidityInterface from '../components/LiquidityInterface';
import StakingInterface from '../components/StakingInterface';

// Contract ABIs (shortened for main components)
const TokenA_ABI = [
  "function balanceOf(address owner) external view returns (uint256)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
  "function symbol() external view returns (string)",
  "function decimals() external view returns (uint8)",
  "function name() external view returns (string)",
  "function transfer(address to, uint256 amount) external returns (bool)",
  "event Transfer(address indexed from, address indexed to, uint256 value)",
  "event Approval(address indexed owner, address indexed spender, uint256 value)"
];

const TokenB_ABI = [
  "function balanceOf(address owner) external view returns (uint256)",
  "function symbol() external view returns (string)",
  "function decimals() external view returns (uint8)",
  "function name() external view returns (string)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
  "function transfer(address to, uint256 amount) external returns (bool)",
  "event Transfer(address indexed from, address indexed to, uint256 value)"
];

const TokenSwap_ABI = [
  "function swapAForB(uint256 amountA) external",
  "function swapBForA(uint256 amountB) external",
  "function getTokenBLiquidity() external view returns (uint256)",
  "function getTokenABalance() external view returns (uint256)",
  "function tokenA() external view returns (address)",
  "function tokenB() external view returns (address)",
  "event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut)"
];

const NexusLiquidityPool_ABI = [
  "function addLiquidity(uint256 amountA, uint256 amountB) external returns (uint256)",
  "function removeLiquidity(uint256 liquidity) external returns (uint256, uint256)",
  "function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)",
  "function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256)",
  "function balanceOf(address owner) external view returns (uint256)",
  "function totalSupply() external view returns (uint256)",
  "function getPoolStats() external view returns (uint256, uint256, uint256, uint256, uint256, uint256, uint256)",
  "event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut, uint256 fee)"
];

// Nexus network configuration
const NEXUS_NETWORK = {
  chainId: '0xF64', // 3940 in hex
  chainName: 'Nexus Testnet III',
  nativeCurrency: {
    name: 'NEX',
    symbol: 'NEX',
    decimals: 18,
  },
  rpcUrls: ['https://testnet3.rpc.nexus.xyz'],
  blockExplorerUrls: ['https://testnet3.explorer.nexus.xyz'],
};

// Default contract addresses
const DEFAULT_ADDRESSES = {
  TokenA: '******************************************',
  TokenB: '******************************************',
  TokenSwap: '******************************************',
  LiquidityPool: '',
  Staking: '',
  BatchOperations: '',
};

interface ContractAddresses {
  TokenA: string;
  TokenB: string;
  TokenSwap: string;
  LiquidityPool?: string;
  Staking?: string;
  BatchOperations?: string;
}

export default function Home() {
  // Wallet states
  const [account, setAccount] = useState<string>('');
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isCorrectNetwork, setIsCorrectNetwork] = useState<boolean>(false);

  // Balance states
  const [tokenABalance, setTokenABalance] = useState<string>('0');
  const [tokenBBalance, setTokenBBalance] = useState<string>('0');
  const [lpTokenBalance, setLpTokenBalance] = useState<string>('0');

  // UI states
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');
  const [contractAddresses, setContractAddresses] = useState<ContractAddresses>(DEFAULT_ADDRESSES);

  // Pool stats
  const [poolStats, setPoolStats] = useState({
    reserveA: '0',
    reserveB: '0',
    totalSupply: '0',
    volumeA: '0',
    volumeB: '0',
    totalSwaps: '0',
    feeRate: '0.3'
  });

  // Recent transactions
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);

  // Active tab state
  const [activeTab, setActiveTab] = useState('swap');

  // Show landing page or trading interface
  const [showLandingPage, setShowLandingPage] = useState(true);

  // Staking states
  const [stakedBalance, setStakedBalance] = useState<string>('0');
  const [earnedRewards, setEarnedRewards] = useState<string>('0');

  useEffect(() => {
    checkIfWalletIsConnected();
    loadContractAddresses();
  }, []);

  useEffect(() => {
    if (account && isCorrectNetwork) {
      updateBalances();
      updatePoolStats();
    }
  }, [account, contractAddresses, isCorrectNetwork]);

  async function loadContractAddresses() {
    try {
      const response = await fetch('/deployedAddresses.json');
      if (response.ok) {
        const addresses = await response.json();
        setContractAddresses(addresses);
        console.log('Loaded contract addresses:', addresses);
      } else {
        console.warn('Could not load deployed addresses, using defaults');
      }
    } catch (error) {
      console.warn('Could not load deployed addresses:', error);
    }
  }

  async function checkIfWalletIsConnected() {
    try {
      const { ethereum } = window as any;
      if (!ethereum) {
        console.log('MetaMask not detected');
        return;
      }

      setIsConnecting(true);
      const accounts = await ethereum.request({ method: 'eth_accounts' });

      if (accounts.length > 0) {
        setAccount(accounts[0]);
        setIsConnected(true);
        console.log('✅ Wallet already connected:', accounts[0]);
        await checkNetwork();
      }
    } catch (error: any) {
      console.error('Error checking wallet connection:', error);
      setError('Error checking wallet connection');
    } finally {
      setIsConnecting(false);
    }
  }

  async function connectWallet() {
    try {
      const { ethereum } = window as any;
      if (!ethereum) {
        setError('MetaMask not detected. Please install MetaMask.');
        return;
      }

      setIsConnecting(true);
      setError('');

      const accounts = await ethereum.request({
        method: 'eth_requestAccounts',
      });

      if (accounts.length > 0) {
        setAccount(accounts[0]);
        setIsConnected(true);
        setSuccess('✅ Wallet connected successfully!');
        setTimeout(() => setSuccess(''), 3000);
        
        await checkNetwork();
      }
    } catch (error: any) {
      console.error('Error connecting wallet:', error);
      if (error.code === 4001) {
        setError('Connection rejected by user');
      } else {
        setError('Error connecting wallet: ' + (error.message || 'Unknown error'));
      }
    } finally {
      setIsConnecting(false);
    }
  }

  async function checkNetwork() {
    try {
      const { ethereum } = window as any;
      if (!ethereum) return;

      const chainId = await ethereum.request({ method: 'eth_chainId' });
      const currentChainId = parseInt(chainId, 16);
      const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);

      if (currentChainId === expectedChainId) {
        setIsCorrectNetwork(true);
        setError('');
        console.log('✅ Connected to Nexus Testnet III');
      } else {
        setIsCorrectNetwork(false);
        setError(`Wrong network. Please switch to Nexus Testnet III.`);
      }
    } catch (error: any) {
      console.error('Error checking network:', error);
      setIsCorrectNetwork(false);
      setError('Error checking network');
    }
  }

  async function switchToNexusNetwork() {
    try {
      const { ethereum } = window as any;
      if (!ethereum) return;

      setError('');
      setSuccess('🔄 Switching to Nexus Testnet III...');

      try {
        await ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: NEXUS_NETWORK.chainId }],
        });
      } catch (switchError: any) {
        if (switchError.code === 4902) {
          await ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [NEXUS_NETWORK],
          });
        } else {
          throw switchError;
        }
      }

      setSuccess('✅ Successfully switched to Nexus Testnet III!');
      setTimeout(() => setSuccess(''), 3000);
      await checkNetwork();
    } catch (error: any) {
      console.error('Error switching network:', error);
      setError('Error switching network: ' + (error.message || 'Unknown error'));
    }
  }

  async function updateBalances() {
    if (!account || !isCorrectNetwork) return;

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);

      // Get TokenA balance
      const tokenAContract = new ethers.Contract(contractAddresses.TokenA, TokenA_ABI, provider);
      const balanceA = await tokenAContract.balanceOf(account);
      setTokenABalance(ethers.formatEther(balanceA));

      // Get TokenB balance
      const tokenBContract = new ethers.Contract(contractAddresses.TokenB, TokenB_ABI, provider);
      const balanceB = await tokenBContract.balanceOf(account);
      setTokenBBalance(ethers.formatEther(balanceB));

      // Get LP token balance if available
      if (contractAddresses.LiquidityPool) {
        const lpContract = new ethers.Contract(contractAddresses.LiquidityPool, NexusLiquidityPool_ABI, provider);
        const balanceLP = await lpContract.balanceOf(account);
        setLpTokenBalance(ethers.formatEther(balanceLP));
      }

      console.log('Balances updated:', {
        tokenA: ethers.formatEther(balanceA),
        tokenB: ethers.formatEther(balanceB)
      });
    } catch (error) {
      console.error('Error updating balances:', error);
    }
  }

  async function updatePoolStats() {
    if (!contractAddresses.LiquidityPool) return;

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const lpContract = new ethers.Contract(contractAddresses.LiquidityPool, NexusLiquidityPool_ABI, provider);

      const stats = await lpContract.getPoolStats();
      setPoolStats({
        reserveA: ethers.formatEther(stats[0]),
        reserveB: ethers.formatEther(stats[1]),
        totalSupply: ethers.formatEther(stats[2]),
        volumeA: ethers.formatEther(stats[3]),
        volumeB: ethers.formatEther(stats[4]),
        totalSwaps: stats[5].toString(),
        feeRate: (Number(stats[6]) / 100).toString()
      });
    } catch (error) {
      console.error('Error updating pool stats:', error);
    }
  }

  async function handleSwap(tokenInAddress: string, tokenOutAddress: string, amount: string) {
    if (!account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const amountWei = ethers.parseEther(amount);

      // Determine which token is being swapped
      const isTokenAToB = tokenInAddress.toLowerCase() === contractAddresses.TokenA.toLowerCase();
      
      // Use appropriate contract and method
      if (contractAddresses.LiquidityPool) {
        // Use liquidity pool for swap
        const lpContract = new ethers.Contract(contractAddresses.LiquidityPool, NexusLiquidityPool_ABI, signer);
        
        // Approve token first
        const tokenContract = new ethers.Contract(tokenInAddress, TokenA_ABI, signer);
        const approveTx = await tokenContract.approve(contractAddresses.LiquidityPool, amountWei);
        await approveTx.wait();

        // Execute swap
        const swapTx = await lpContract.swap(tokenInAddress, amountWei, 0);
        await swapTx.wait();

        setSuccess('✅ Swap completed successfully!');
        
        // Add to recent transactions
        setRecentTransactions(prev => [{
          hash: swapTx.hash,
          type: 'Swap',
          amount: amount,
          tokenIn: isTokenAToB ? 'TKNA' : 'TKNB',
          tokenOut: isTokenAToB ? 'TKNB' : 'TKNA',
          timestamp: Date.now()
        }, ...prev.slice(0, 4)]);
      } else {
        // Use basic swap contract
        const swapContract = new ethers.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);
        
        // Approve token first
        const tokenContract = new ethers.Contract(tokenInAddress, TokenA_ABI, signer);
        const approveTx = await tokenContract.approve(contractAddresses.TokenSwap, amountWei);
        await approveTx.wait();

        // Execute swap
        let swapTx;
        if (isTokenAToB) {
          swapTx = await swapContract.swapAForB(amountWei);
        } else {
          swapTx = await swapContract.swapBForA(amountWei);
        }
        await swapTx.wait();

        setSuccess('✅ Swap completed successfully!');
      }

      // Update balances
      setTimeout(() => {
        updateBalances();
        updatePoolStats();
        setSuccess('');
      }, 2000);

    } catch (error: any) {
      console.error('Swap failed:', error);
      setError('Swap failed: ' + (error.reason || error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }

  // Wallet disconnect function
  async function disconnectWallet() {
    try {
      setAccount('');
      setIsConnected(false);
      setIsCorrectNetwork(false);
      setTokenABalance('0');
      setTokenBBalance('0');
      setLpTokenBalance('0');
      setStakedBalance('0');
      setEarnedRewards('0');
      setSuccess('✅ Wallet disconnected successfully!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (error: any) {
      console.error('Error disconnecting wallet:', error);
      setError('Error disconnecting wallet');
    }
  }

  // Claim rewards function
  async function handleClaimRewards() {
    if (!account || !isCorrectNetwork || !contractAddresses.Staking) return;

    setLoading(true);
    setError('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const stakingContract = new ethers.Contract(
        contractAddresses.Staking, 
        ['function claimRewards() external'], 
        signer
      );

      const claimTx = await stakingContract.claimRewards();
      await claimTx.wait();

      setSuccess('✅ Rewards claimed successfully!');

      // Add to recent transactions
      setRecentTransactions(prev => [{
        hash: claimTx.hash,
        type: 'Claim Rewards',
        amount: `${earnedRewards} TKNB`,
        tokenIn: 'Rewards',
        tokenOut: 'TKNB',
        timestamp: Date.now()
      }, ...prev.slice(0, 4)]);

      // Update balances
      setTimeout(() => {
        updateBalances();
        setSuccess('');
      }, 2000);

    } catch (error: any) {
      console.error('Claim rewards failed:', error);
      setError('Claim rewards failed: ' + (error.reason || error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }

  async function handleAddLiquidity(amountA: string, amountB: string) {
    if (!account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const amountAWei = ethers.parseEther(amountA);
      const amountBWei = ethers.parseEther(amountB);

      if (contractAddresses.LiquidityPool) {
        const tokenAContract = new ethers.Contract(contractAddresses.TokenA, TokenA_ABI, signer);
        const tokenBContract = new ethers.Contract(contractAddresses.TokenB, TokenB_ABI, signer);
        const lpContract = new ethers.Contract(contractAddresses.LiquidityPool, NexusLiquidityPool_ABI, signer);

        // Approve tokens
        const approveATx = await tokenAContract.approve(contractAddresses.LiquidityPool, amountAWei);
        await approveATx.wait();

        const approveBTx = await tokenBContract.approve(contractAddresses.LiquidityPool, amountBWei);
        await approveBTx.wait();

        // Add liquidity
        const addLiquidityTx = await lpContract.addLiquidity(amountAWei, amountBWei);
        await addLiquidityTx.wait();

        setSuccess('✅ Liquidity added successfully!');

        // Add to recent transactions
        setRecentTransactions(prev => [{
          hash: addLiquidityTx.hash,
          type: 'Add Liquidity',
          amount: `${amountA} TKNA + ${amountB} TKNB`,
          tokenIn: 'TKNA',
          tokenOut: 'TKNB',
          timestamp: Date.now()
        }, ...prev.slice(0, 4)]);
      }

      // Update balances
      setTimeout(() => {
        updateBalances();
        updatePoolStats();
        setSuccess('');
      }, 2000);

    } catch (error: any) {
      console.error('Add liquidity failed:', error);
      setError('Add liquidity failed: ' + (error.reason || error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }

  async function handleRemoveLiquidity(amount: string) {
    if (!account || !isCorrectNetwork || !contractAddresses.LiquidityPool) return;

    setLoading(true);
    setError('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const amountWei = ethers.parseEther(amount);
      const lpContract = new ethers.Contract(contractAddresses.LiquidityPool, NexusLiquidityPool_ABI, signer);

      const removeLiquidityTx = await lpContract.removeLiquidity(amountWei);
      await removeLiquidityTx.wait();

      setSuccess('✅ Liquidity removed successfully!');

      // Add to recent transactions
      setRecentTransactions(prev => [{
        hash: removeLiquidityTx.hash,
        type: 'Remove Liquidity',
        amount: `${amount} NLP`,
        tokenIn: 'NLP',
        tokenOut: 'TKNA+TKNB',
        timestamp: Date.now()
      }, ...prev.slice(0, 4)]);

      // Update balances
      setTimeout(() => {
        updateBalances();
        updatePoolStats();
        setSuccess('');
      }, 2000);

    } catch (error: any) {
      console.error('Remove liquidity failed:', error);
      setError('Remove liquidity failed: ' + (error.reason || error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }

  async function handleStake(amount: string) {
    if (!account || !isCorrectNetwork || !contractAddresses.Staking) return;

    setLoading(true);
    setError('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const amountWei = ethers.parseEther(amount);
      const lpContract = new ethers.Contract(contractAddresses.LiquidityPool!, NexusLiquidityPool_ABI, signer);
      const stakingContract = new ethers.Contract(contractAddresses.Staking, ['function stake(uint256 amount) external'], signer);

      // Approve LP tokens
      const approveTx = await lpContract.approve(contractAddresses.Staking, amountWei);
      await approveTx.wait();

      // Stake
      const stakeTx = await stakingContract.stake(amountWei);
      await stakeTx.wait();

      setSuccess('✅ Staking successful!');

      // Add to recent transactions
      setRecentTransactions(prev => [{
        hash: stakeTx.hash,
        type: 'Stake',
        amount: `${amount} NLP`,
        tokenIn: 'NLP',
        tokenOut: 'Staked',
        timestamp: Date.now()
      }, ...prev.slice(0, 4)]);

      // Update balances
      setTimeout(() => {
        updateBalances();
        setSuccess('');
      }, 2000);

    } catch (error: any) {
      console.error('Stake failed:', error);
      setError('Stake failed: ' + (error.reason || error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }

  async function handleWithdraw(amount: string) {
    if (!account || !isCorrectNetwork || !contractAddresses.Staking) return;

    setLoading(true);
    setError('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const amountWei = ethers.parseEther(amount);
      const stakingContract = new ethers.Contract(contractAddresses.Staking, ['function withdraw(uint256 amount) external'], signer);

      const withdrawTx = await stakingContract.withdraw(amountWei);
      await withdrawTx.wait();

      setSuccess('✅ Withdrawal successful!');

      // Add to recent transactions
      setRecentTransactions(prev => [{
        hash: withdrawTx.hash,
        type: 'Withdraw',
        amount: `${amount} NLP`,
        tokenIn: 'Staked',
        tokenOut: 'NLP',
        timestamp: Date.now()
      }, ...prev.slice(0, 4)]);

      // Update balances
      setTimeout(() => {
        updateBalances();
        setSuccess('');
      }, 2000);

    } catch (error: any) {
      console.error('Withdraw failed:', error);
      setError('Withdraw failed: ' + (error.reason || error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }

  async function handleClaim() {
    if (!account || !isCorrectNetwork || !contractAddresses.Staking) return;

    setLoading(true);
    setError('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const stakingContract = new ethers.Contract(contractAddresses.Staking, ['function claimReward() external'], signer);

      const claimTx = await stakingContract.claimReward();
      await claimTx.wait();

      setSuccess('✅ Rewards claimed successfully!');

      // Add to recent transactions
      setRecentTransactions(prev => [{
        hash: claimTx.hash,
        type: 'Claim Rewards',
        amount: earnedRewards,
        tokenIn: 'Rewards',
        tokenOut: 'TKNB',
        timestamp: Date.now()
      }, ...prev.slice(0, 4)]);

      // Update balances
      setTimeout(() => {
        updateBalances();
        setSuccess('');
      }, 2000);

    } catch (error: any) {
      console.error('Claim failed:', error);
      setError('Claim failed: ' + (error.reason || error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance);
    if (num === 0) return '0.00';
    if (num < 0.01) return '<0.01';
    return num.toFixed(4);
  };

    const formatNumber = (num: string) => {
    const n = parseFloat(num);
    if (n === 0) return '0.00';
    if (n < 0.01) return '<0.01';
    if (n < 1000) return n.toFixed(2);
    if (n < 1000000) return `${(n / 1000).toFixed(1)}K`;
    return `${(n / 1000000).toFixed(1)}M`;
  };

  const handleGetStarted = () => {
    setShowLandingPage(false);
    if (!isConnected) {
      connectWallet();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 safe-area-inset">
      {/* Header */}
      <Header
        account={account}
        isConnected={isConnected}
        isCorrectNetwork={isCorrectNetwork}
        onConnectWallet={connectWallet}
        onSwitchNetwork={switchToNexusNetwork}
        onDisconnectWallet={disconnectWallet}
        tokenABalance={tokenABalance}
        tokenBBalance={tokenBBalance}
        showLandingPage={showLandingPage}
        onBackToLanding={() => setShowLandingPage(true)}
      />

      {showLandingPage ? (
        <>
          {/* Landing Page */}
          <HeroSection onGetStarted={handleGetStarted} />
          <StatsSection />
          <Footer />
        </>
      ) : (
        <>
          {/* Trading Interface */}
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
            {/* Status Messages */}
            {error && (
              <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-900/50 border border-red-700/50 rounded-lg">
                <p className="text-red-300 text-sm sm:text-base">{error}</p>
              </div>
            )}

            {success && (
              <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-green-900/50 border border-green-700/50 rounded-lg">
                <p className="text-green-300 text-sm sm:text-base">{success}</p>
              </div>
            )}

            <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
              {/* Main Interface */}
              <div className="xl:col-span-2">
                {/* Tab Navigation */}
                <div className="flex items-center space-x-1 mb-4 sm:mb-6 bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-1">
                  {[
                    { id: 'swap', label: 'Swap', icon: '🔄' },
                    { id: 'liquidity', label: 'Liquidity', icon: '💧' },
                    { id: 'staking', label: 'Staking', icon: '🏦' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex-1 flex items-center justify-center space-x-1 sm:space-x-2 py-2 sm:py-3 px-2 sm:px-4 rounded-lg font-medium transition-all text-sm sm:text-base ${
                        activeTab === tab.id
                          ? 'bg-nexus-600 text-white shadow-lg'
                          : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                      }`}
                    >
                      <span className="text-sm sm:text-base">{tab.icon}</span>
                      <span className="hidden sm:inline">{tab.label}</span>
                      <span className="sm:hidden text-xs">{tab.label}</span>
                    </button>
                  ))}
                </div>

                {/* Tab Content */}
                {activeTab === 'swap' && (
                  <SwapInterface
                    account={account}
                    isConnected={isConnected}
                    isCorrectNetwork={isCorrectNetwork}
                    contractAddresses={contractAddresses}
                    tokenABalance={tokenABalance}
                    tokenBBalance={tokenBBalance}
                    onSwap={handleSwap}
                    loading={loading}
                  />
                )}

                {activeTab === 'liquidity' && (
                  <LiquidityInterface
                    account={account}
                    isConnected={isConnected}
                    isCorrectNetwork={isCorrectNetwork}
                    contractAddresses={contractAddresses}
                    tokenABalance={tokenABalance}
                    tokenBBalance={tokenBBalance}
                    lpTokenBalance={lpTokenBalance}
                    onAddLiquidity={handleAddLiquidity}
                    onRemoveLiquidity={handleRemoveLiquidity}
                    loading={loading}
                  />
                )}

                {activeTab === 'staking' && (
                  <StakingInterface
                    account={account}
                    isConnected={isConnected}
                    isCorrectNetwork={isCorrectNetwork}
                    lpTokenBalance={lpTokenBalance}
                    stakedBalance={stakedBalance}
                    earnedRewards={earnedRewards}
                    onStake={handleStake}
                    onWithdraw={handleWithdraw}
                    onClaim={handleClaim}
                    loading={loading}
                  />
                )}
              </div>

              {/* Sidebar - Stack on mobile */}
              <div className="space-y-4 sm:space-y-6">
                {/* Mobile Token Balances - Show on mobile when connected */}
                {isConnected && isCorrectNetwork && (
                  <div className="xl:hidden bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-4 sm:p-6">
                    <h3 className="text-lg font-bold text-white mb-4">Your Balances</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                        <p className="text-xs text-slate-400">TKNA</p>
                        <p className="text-sm font-mono text-white">{formatBalance(tokenABalance)}</p>
                      </div>
                      <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                        <p className="text-xs text-slate-400">TKNB</p>
                        <p className="text-sm font-mono text-white">{formatBalance(tokenBBalance)}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Pool Stats */}
                <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-4 sm:p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Pool Statistics</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-slate-400 text-sm">TVL</span>
                      <span className="text-white font-mono text-sm">
                        ${formatNumber((parseFloat(poolStats.reserveA) + parseFloat(poolStats.reserveB)).toString())}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400 text-sm">24h Volume</span>
                      <span className="text-white font-mono text-sm">
                        ${formatNumber((parseFloat(poolStats.volumeA) + parseFloat(poolStats.volumeB)).toString())}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400 text-sm">Total Swaps</span>
                      <span className="text-white font-mono text-sm">{poolStats.totalSwaps}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400 text-sm">Fee Rate</span>
                      <span className="text-white font-mono text-sm">{poolStats.feeRate}%</span>
                    </div>
                  </div>
                </div>

                {/* Recent Transactions */}
                <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-4 sm:p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Recent Transactions</h3>
                  {recentTransactions.length > 0 ? (
                    <div className="space-y-3">
                      {recentTransactions.slice(0, 3).map((tx, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                          <div>
                            <p className="text-white text-sm font-medium">{tx.type}</p>
                            <p className="text-slate-400 text-xs">
                              {tx.amount} {tx.tokenIn} → {tx.tokenOut}
                            </p>
                          </div>
                          <a
                            href={`https://testnet3.explorer.nexus.xyz/tx/${tx.hash}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-nexus-400 hover:text-nexus-300 text-xs"
                          >
                            View →
                          </a>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-slate-400 text-sm">No recent transactions</p>
                  )}
                </div>

                {/* Market Info */}
                <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-4 sm:p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Market Info</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-slate-400 text-sm">TKNA Reserve</span>
                      <span className="text-white font-mono text-sm">{formatNumber(poolStats.reserveA)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400 text-sm">TKNB Reserve</span>
                      <span className="text-white font-mono text-sm">{formatNumber(poolStats.reserveB)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400 text-sm">LP Supply</span>
                      <span className="text-white font-mono text-sm">{formatNumber(poolStats.totalSupply)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </>
      )}
    </div>
  );
}