'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// Contract ABIs - Complete for all DeFi operations
const TokenA_ABI = [
  "function balanceOf(address owner) external view returns (uint256)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
  "function symbol() external view returns (string)",
  "function decimals() external view returns (uint8)",
  "function name() external view returns (string)",
  "function totalSupply() external view returns (uint256)",
  "function transfer(address to, uint256 amount) external returns (bool)",
  "function transferFrom(address from, address to, uint256 amount) external returns (bool)"
];

const TokenB_ABI = [
  "function balanceOf(address owner) external view returns (uint256)",
  "function symbol() external view returns (string)",
  "function decimals() external view returns (uint8)",
  "function name() external view returns (string)",
  "function totalSupply() external view returns (uint256)"
];

const TokenSwap_ABI = [
  "function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)",
  "function getTokenBLiquidity() external view returns (uint256)",
  "function getTokenABalance() external view returns (uint256)",
  "function tokenA() external view returns (address)",
  "function tokenB() external view returns (address)",
  "event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut)"
];

const LiquidityPool_ABI = [
  "function addLiquidity(uint256 amountA, uint256 amountB) external returns (uint256)",
  "function removeLiquidity(uint256 liquidity) external returns (uint256, uint256)",
  "function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)",
  "function getReserves() external view returns (uint256, uint256)",
  "function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256)",
  "function balanceOf(address owner) external view returns (uint256)",
  "function totalSupply() external view returns (uint256)",
  "function symbol() external view returns (string)",
  "function name() external view returns (string)"
];

const Staking_ABI = [
  "function stake(uint256 amount) external",
  "function withdraw(uint256 amount) external",
  "function claimReward() external",
  "function exit() external",
  "function compound() external",
  "function multiStake(uint256[] amounts) external",
  "function multiWithdraw(uint256[] amounts) external",
  "function multiClaim(uint256 times) external",
  "function earned(address account) external view returns (uint256)",
  "function balances(address account) external view returns (uint256)",
  "function getStakingInfo(address user) external view returns (uint256, uint256, uint256, uint256)"
];

// Nexus network configuration
const NEXUS_NETWORK = {
  chainId: '0xF64', // 3940 in hex
  chainName: 'Nexus Testnet III',
  nativeCurrency: {
    name: 'NEX',
    symbol: 'NEX',
    decimals: 18,
  },
  rpcUrls: ['https://testnet3.rpc.nexus.xyz'],
  blockExplorerUrls: ['https://explorer.nexus.xyz'],
};

// Default contract addresses (will be replaced with deployed addresses)
const DEFAULT_ADDRESSES = {
  TokenA: '0x0000000000000000000000000000000000000000',
  TokenB: '0x0000000000000000000000000000000000000000',
  TokenSwap: '0x0000000000000000000000000000000000000000',
};

interface ContractAddresses {
  TokenA: string;
  TokenB: string;
  TokenSwap: string;
  LiquidityPool?: string;
  Staking?: string;
  network?: string;
  chainId?: number;
  deployer?: string;
  deploymentBlock?: number;
  timestamp?: string;
}

export default function Home() {
  // Basic states
  const [account, setAccount] = useState<string>('');
  const [tokenABalance, setTokenABalance] = useState<string>('0');
  const [tokenBBalance, setTokenBBalance] = useState<string>('0');
  const [lpTokenBalance, setLpTokenBalance] = useState<string>('0');
  const [stakedBalance, setStakedBalance] = useState<string>('0');
  const [earnedRewards, setEarnedRewards] = useState<string>('0');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');
  const [contractAddresses, setContractAddresses] = useState<ContractAddresses>(DEFAULT_ADDRESSES);
  const [liquidity, setLiquidity] = useState<string>('0');
  const [isCorrectNetwork, setIsCorrectNetwork] = useState<boolean>(false);

  // Tab and form states
  const [activeTab, setActiveTab] = useState<string>('swap');
  const [swapAmount, setSwapAmount] = useState<string>('');
  const [liquidityAmountA, setLiquidityAmountA] = useState<string>('');
  const [liquidityAmountB, setLiquidityAmountB] = useState<string>('');
  const [stakeAmount, setStakeAmount] = useState<string>('');
  const [withdrawAmount, setWithdrawAmount] = useState<string>('');

  // Token selection states
  const [selectedTokenIn, setSelectedTokenIn] = useState<string>('TokenA');
  const [selectedTokenOut, setSelectedTokenOut] = useState<string>('TokenB');
  const [availableTokens, setAvailableTokens] = useState<any[]>([]);

  useEffect(() => {
    checkIfWalletIsConnected();
    loadContractAddresses();
    setupEventListeners();
  }, []);

  useEffect(() => {
    if (account && isCorrectNetwork) {
      updateBalances();
      updateLiquidity();
      loadAvailableTokens();
    }
  }, [account, contractAddresses, isCorrectNetwork]);

  async function loadAvailableTokens() {
    if (!account || !isCorrectNetwork) return;

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);

      const tokens = [
        {
          symbol: 'TKNA',
          name: 'Token A',
          address: contractAddresses.TokenA,
          balance: tokenABalance,
          decimals: 18
        },
        {
          symbol: 'TKNB',
          name: 'Token B',
          address: contractAddresses.TokenB,
          balance: tokenBBalance,
          decimals: 18
        }
      ];

      // Add LP token if available
      if (contractAddresses.LiquidityPool) {
        tokens.push({
          symbol: 'NLP',
          name: 'Nexus LP Token',
          address: contractAddresses.LiquidityPool,
          balance: lpTokenBalance,
          decimals: 18
        });
      }

      setAvailableTokens(tokens);
    } catch (error) {
      console.error('Error loading available tokens:', error);
    }
  }

  function setupEventListeners() {
    const { ethereum } = window as any;
    if (!ethereum) return;

    // Listen for account changes
    ethereum.on('accountsChanged', (accounts: string[]) => {
      if (accounts.length > 0) {
        setAccount(accounts[0]);
        checkNetwork();
      } else {
        setAccount('');
        setIsCorrectNetwork(false);
      }
    });

    // Listen for network changes
    ethereum.on('chainChanged', (chainId: string) => {
      console.log('Network changed to:', chainId);
      checkNetwork();
      // Reload page to reset state
      window.location.reload();
    });

    // Cleanup function
    return () => {
      if (ethereum.removeListener) {
        ethereum.removeListener('accountsChanged', () => {});
        ethereum.removeListener('chainChanged', () => {});
      }
    };
  }

  async function loadContractAddresses() {
    try {
      // Try to load deployed addresses from public folder
      const response = await fetch('/deployedAddresses.json');
      if (response.ok) {
        const addresses = await response.json();
        setContractAddresses(addresses);
        console.log('Loaded contract addresses:', addresses);
      } else {
        console.warn('Could not load deployed addresses, using defaults');
      }
    } catch (error) {
      console.warn('Could not load deployed addresses:', error);
    }
  }

  async function checkIfWalletIsConnected() {
    try {
      const { ethereum } = window as any;
      if (!ethereum) {
        setError('MetaMask tidak terdeteksi. Silakan install MetaMask.');
        return;
      }

      const accounts = await ethereum.request({ method: 'eth_accounts' });
      if (accounts.length > 0) {
        setAccount(accounts[0]);
        await checkNetwork();
      }
    } catch (error) {
      console.error('Error checking wallet connection:', error);
      setError('Error saat mengecek koneksi wallet');
    }
  }

  async function checkNetwork() {
    try {
      const { ethereum } = window as any;
      if (!ethereum) {
        setIsCorrectNetwork(false);
        return;
      }

      const chainId = await ethereum.request({ method: 'eth_chainId' });
      console.log('Current chainId:', chainId, 'Expected:', NEXUS_NETWORK.chainId);

      // Convert both to same format for comparison
      const currentChainId = parseInt(chainId, 16);
      const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);

      if (currentChainId === expectedChainId) {
        setIsCorrectNetwork(true);
        setError('');
        console.log('✅ Connected to correct network');
      } else {
        setIsCorrectNetwork(false);
        setError(`Wrong network. Current: ${currentChainId}, Expected: ${expectedChainId} (Nexus Testnet III)`);
        console.log('❌ Wrong network detected');
      }
    } catch (error) {
      console.error('Error checking network:', error);
      setIsCorrectNetwork(false);
      setError('Error checking network');
    }
  }

  async function switchToNexusNetwork() {
    try {
      const { ethereum } = window as any;
      setError('');
      setSuccess('Switching to Nexus network...');

      try {
        await ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: NEXUS_NETWORK.chainId }],
        });
        console.log('✅ Network switch requested');
      } catch (switchError: any) {
        console.log('Switch error code:', switchError.code);
        // Network belum ditambahkan, tambahkan dulu
        if (switchError.code === 4902) {
          console.log('Adding Nexus network...');
          await ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [NEXUS_NETWORK],
          });
          console.log('✅ Network added');
        } else {
          throw switchError;
        }
      }

      // Wait a bit for network to switch
      setTimeout(async () => {
        await checkNetwork();
        setSuccess('');
      }, 1000);

    } catch (error: any) {
      console.error('Error switching network:', error);
      setError('Gagal switch ke Nexus network: ' + (error.message || 'Unknown error'));
      setSuccess('');
    }
  }

  async function connectWallet() {
    try {
      const { ethereum } = window as any;
      if (!ethereum) {
        setError('MetaMask tidak terdeteksi. Silakan install MetaMask.');
        return;
      }

      const accounts = await ethereum.request({ method: 'eth_requestAccounts' });
      setAccount(accounts[0]);
      await checkNetwork();
      setError('');
      setSuccess('Wallet berhasil terhubung!');
    } catch (error) {
      console.error('Error connecting wallet:', error);
      setError('Gagal menghubungkan wallet');
    }
  }

  async function updateBalances() {
    if (!account || !isCorrectNetwork) return;

    // Check if contract addresses are valid
    if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {
      console.log('Contract addresses not loaded yet, skipping balance update');
      return;
    }

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);

      console.log('Updating all balances for:', account);

      const tokenAContract = new ethers.Contract(contractAddresses.TokenA, TokenA_ABI, provider);
      const tokenBContract = new ethers.Contract(contractAddresses.TokenB, TokenB_ABI, provider);

      // Get basic token balances
      const [balanceA, balanceB] = await Promise.all([
        tokenAContract.balanceOf(account),
        tokenBContract.balanceOf(account)
      ]);

      setTokenABalance(ethers.formatEther(balanceA));
      setTokenBBalance(ethers.formatEther(balanceB));

      // Get LP token balance if LiquidityPool exists
      if (contractAddresses.LiquidityPool) {
        const lpContract = new ethers.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, provider);
        const lpBalance = await lpContract.balanceOf(account);
        setLpTokenBalance(ethers.formatEther(lpBalance));
      }

      // Get staking info if Staking exists
      if (contractAddresses.Staking) {
        const stakingContract = new ethers.Contract(contractAddresses.Staking, Staking_ABI, provider);
        const [stakedBal, earned] = await Promise.all([
          stakingContract.balances(account),
          stakingContract.earned(account)
        ]);
        setStakedBalance(ethers.formatEther(stakedBal));
        setEarnedRewards(ethers.formatEther(earned));
      }

      console.log('All balances updated successfully');
    } catch (error) {
      console.error('Error updating balances:', error);
      setError('Error connecting to contracts');
    }
  }

  async function updateLiquidity() {
    if (!isCorrectNetwork) return;

    // Check if contract addresses are valid
    if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {
      console.log('TokenSwap address not loaded yet, skipping liquidity update');
      return;
    }

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);

      console.log('Updating liquidity for TokenSwap:', contractAddresses.TokenSwap);

      const swapContract = new ethers.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);

      try {
        const liquidityAmount = await swapContract.getTokenBLiquidity();
        console.log('Raw liquidity:', liquidityAmount.toString());

        setLiquidity(ethers.formatEther(liquidityAmount));
        console.log('Liquidity updated successfully');
      } catch (contractError) {
        console.error('TokenSwap contract call error:', contractError);
        setError('Error reading liquidity. TokenSwap contract may not be deployed correctly.');
      }
    } catch (error) {
      console.error('Error updating liquidity:', error);
      setError('Error connecting to TokenSwap contract');
    }
  }

  async function handleSwap() {
    if (!swapAmount || !account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      // Determine which tokens to use
      const isTokenAToB = selectedTokenIn === 'TokenA';
      const inputTokenAddress = isTokenAToB ? contractAddresses.TokenA : contractAddresses.TokenB;
      const inputTokenContract = new ethers.Contract(inputTokenAddress, TokenA_ABI, signer);

      // Use LiquidityPool for swapping (more advanced than TokenSwap)
      const swapContract = contractAddresses.LiquidityPool
        ? new ethers.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer)
        : new ethers.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);

      const amount = ethers.parseEther(swapAmount);

      // Check balance
      const balance = await inputTokenContract.balanceOf(account);
      if (balance < amount) {
        setError(`Saldo ${selectedTokenIn === 'TokenA' ? 'Token A' : 'Token B'} tidak mencukupi`);
        return;
      }

      // Check allowance
      const swapAddress = contractAddresses.LiquidityPool || contractAddresses.TokenSwap;
      const allowance = await inputTokenContract.allowance(account, swapAddress);

      if (allowance < amount) {
        setSuccess(`Menyetujui penggunaan ${selectedTokenIn === 'TokenA' ? 'Token A' : 'Token B'}...`);
        const approveTx = await inputTokenContract.approve(swapAddress, amount);
        await approveTx.wait();
        setSuccess('Approval berhasil! Melakukan swap...');
      } else {
        setSuccess('Melakukan swap...');
      }

      // Perform swap
      if (contractAddresses.LiquidityPool) {
        // Use LiquidityPool swap function
        const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);
        await swapTx.wait();
      } else {
        // Use old TokenSwap (only supports TokenA -> TokenB)
        if (!isTokenAToB) {
          setError('TokenSwap hanya mendukung Token A → Token B');
          return;
        }
        const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);
        await swapTx.wait();
      }

      setSuccess(`Swap ${selectedTokenIn === 'TokenA' ? 'TKNA' : 'TKNB'} → ${selectedTokenOut === 'TokenA' ? 'TKNA' : 'TKNB'} berhasil! 🎉`);
      setSwapAmount('');

      // Update balances
      await updateBalances();
      await updateLiquidity();

    } catch (error: any) {
      console.error('Error during swap:', error);
      if (error.code === 'ACTION_REJECTED') {
        setError('Transaksi dibatalkan oleh user');
      } else if (error.message.includes('insufficient funds')) {
        setError('Saldo NEX tidak mencukupi untuk gas fee');
      } else {
        setError('Swap gagal: ' + (error.reason || error.message));
      }
    } finally {
      setLoading(false);
    }
  }

  async function handleAddLiquidity() {
    if (!liquidityAmountA || !liquidityAmountB || !account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const tokenAContract = new ethers.Contract(contractAddresses.TokenA, TokenA_ABI, signer);
      const tokenBContract = new ethers.Contract(contractAddresses.TokenB, TokenB_ABI, signer);
      const lpContract = new ethers.Contract(contractAddresses.LiquidityPool!, LiquidityPool_ABI, signer);

      const amountA = ethers.parseEther(liquidityAmountA);
      const amountB = ethers.parseEther(liquidityAmountB);

      // Check balances
      const [balanceA, balanceB] = await Promise.all([
        tokenAContract.balanceOf(account),
        tokenBContract.balanceOf(account)
      ]);

      if (balanceA < amountA) {
        setError('Saldo Token A tidak mencukupi');
        return;
      }
      if (balanceB < amountB) {
        setError('Saldo Token B tidak mencukupi');
        return;
      }

      // Approve tokens
      setSuccess('Menyetujui Token A...');
      const approveATx = await tokenAContract.approve(contractAddresses.LiquidityPool, amountA);
      await approveATx.wait();

      setSuccess('Menyetujui Token B...');
      const approveBTx = await tokenBContract.approve(contractAddresses.LiquidityPool, amountB);
      await approveBTx.wait();

      // Add liquidity
      setSuccess('Menambahkan likuiditas...');
      const addLiquidityTx = await lpContract.addLiquidity(amountA, amountB);
      await addLiquidityTx.wait();

      setSuccess('Likuiditas berhasil ditambahkan! 🎉');
      setLiquidityAmountA('');
      setLiquidityAmountB('');

      await updateBalances();

    } catch (error: any) {
      console.error('Error adding liquidity:', error);
      setError('Gagal menambahkan likuiditas: ' + (error.reason || error.message));
    } finally {
      setLoading(false);
    }
  }

  async function handleStake() {
    if (!stakeAmount || !account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const lpContract = new ethers.Contract(contractAddresses.LiquidityPool!, LiquidityPool_ABI, signer);
      const stakingContract = new ethers.Contract(contractAddresses.Staking!, Staking_ABI, signer);

      const amount = ethers.parseEther(stakeAmount);

      // Check LP balance
      const lpBalance = await lpContract.balanceOf(account);
      if (lpBalance < amount) {
        setError('Saldo LP Token tidak mencukupi');
        return;
      }

      // Approve LP tokens
      setSuccess('Menyetujui LP Tokens...');
      const approveTx = await lpContract.approve(contractAddresses.Staking, amount);
      await approveTx.wait();

      // Stake
      setSuccess('Melakukan stake...');
      const stakeTx = await stakingContract.stake(amount);
      await stakeTx.wait();

      setSuccess('Stake berhasil! 🎉');
      setStakeAmount('');

      await updateBalances();

    } catch (error: any) {
      console.error('Error staking:', error);
      setError('Gagal melakukan stake: ' + (error.reason || error.message));
    } finally {
      setLoading(false);
    }
  }

  async function handleClaim() {
    if (!account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const stakingContract = new ethers.Contract(contractAddresses.Staking!, Staking_ABI, signer);

      setSuccess('Mengklaim rewards...');
      const claimTx = await stakingContract.claimReward();
      await claimTx.wait();

      setSuccess('Rewards berhasil diklaim! 🎉');

      await updateBalances();

    } catch (error: any) {
      console.error('Error claiming:', error);
      setError('Gagal mengklaim rewards: ' + (error.reason || error.message));
    } finally {
      setLoading(false);
    }
  }

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const clearMessages = () => {
    setError('');
    setSuccess('');
  };

  const isDeployerAccount = account && contractAddresses.deployer &&
    account.toLowerCase() === contractAddresses.deployer.toLowerCase();

  async function requestTestTokens() {
    if (!account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');
    setSuccess('Requesting test tokens...');

    try {
      // Check if user is the deployer
      if (isDeployerAccount) {
        setSuccess('You are the deployer! You already have all tokens 🎉');
        await updateBalances();
        return;
      }

      // For non-deployer users, show instructions
      setError('');
      setSuccess('');
      alert(`To get test tokens:

1. Switch to deployer account: ${contractAddresses.deployer}
2. Or ask the deployer to send you tokens
3. Or use a faucet if available

Your current address: ${account}
Deployer address: ${contractAddresses.deployer}

The deployer has 1,000,000 Token A available for distribution.`);

    } catch (error: any) {
      console.error('Error requesting test tokens:', error);
      setError('Failed to get test tokens: ' + (error.reason || error.message));
    } finally {
      setLoading(false);
    }
  }

  // Batch Operations untuk Maximum Transactions
  async function handleBatchSwaps() {
    if (!account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const tokenAContract = new ethers.Contract(contractAddresses.TokenA, TokenA_ABI, signer);
      const swapContract = new ethers.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);

      // Multiple small swaps untuk generate banyak transaksi
      const amounts = ['10', '20', '30', '40', '50']; // 5 transaksi swap
      let totalAmount = ethers.parseEther('0');

      for (const amount of amounts) {
        totalAmount += ethers.parseEther(amount);
      }

      // Check balance
      const balance = await tokenAContract.balanceOf(account);
      if (balance < totalAmount) {
        setError('Saldo Token A tidak mencukupi untuk batch swaps');
        return;
      }

      // Approve total amount
      setSuccess('Menyetujui total amount untuk batch swaps...');
      const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, totalAmount);
      await approveTx.wait();

      // Execute multiple swaps
      for (let i = 0; i < amounts.length; i++) {
        setSuccess(`Melakukan swap ${i + 1}/${amounts.length}...`);
        const amount = ethers.parseEther(amounts[i]);
        const swapTx = await swapContract.swap(contractAddresses.TokenA, amount, 0);
        await swapTx.wait();

        // Small delay between transactions
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      setSuccess(`Batch swaps berhasil! ${amounts.length} transaksi completed 🎉`);
      await updateBalances();

    } catch (error: any) {
      console.error('Error during batch swaps:', error);
      setError('Batch swaps gagal: ' + (error.reason || error.message));
    } finally {
      setLoading(false);
    }
  }

  async function handleTransactionSpam() {
    if (!account || !isCorrectNetwork) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const { ethereum } = window as any;
      const provider = new ethers.BrowserProvider(ethereum);
      const signer = await provider.getSigner();

      const tokenAContract = new ethers.Contract(contractAddresses.TokenA, TokenA_ABI, signer);
      const tokenBContract = new ethers.Contract(contractAddresses.TokenB, TokenB_ABI, signer);

      // Generate many approval transactions
      const spamAmount = ethers.parseEther('1');
      const contracts = [
        contractAddresses.TokenSwap,
        contractAddresses.LiquidityPool,
        contractAddresses.Staking
      ];

      let transactionCount = 0;

      for (const contractAddr of contracts) {
        // Multiple approvals for each contract
        for (let i = 0; i < 3; i++) {
          setSuccess(`Generating approval transaction ${transactionCount + 1}...`);

          const approveTx = await tokenAContract.approve(contractAddr!, spamAmount);
          await approveTx.wait();
          transactionCount++;

          // Also approve TokenB
          const approveBTx = await tokenBContract.approve(contractAddr!, spamAmount);
          await approveBTx.wait();
          transactionCount++;

          // Small delay
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      setSuccess(`Transaction spam completed! ${transactionCount} transactions generated 🚀`);

    } catch (error: any) {
      console.error('Error during transaction spam:', error);
      setError('Transaction spam gagal: ' + (error.reason || error.message));
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 relative">
      {/* Header */}
      <div className="absolute top-6 right-6 flex flex-col items-end space-y-2">
        {account && (
          <div className="text-sm text-gray-600">
            <span className="font-medium">Connected:</span> {formatAddress(account)}
          </div>
        )}
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isCorrectNetwork ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-sm text-gray-600">
            {isCorrectNetwork ? 'Nexus Testnet III' : 'Wrong Network'}
          </span>
        </div>
        {/* Debug info */}
        <div className="text-xs text-gray-400 max-w-xs">
          <div>Expected Chain: 3940 (0xF64)</div>
          <div>Contracts: {contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? '✅' : '❌'}</div>
          <div>Deployer: {contractAddresses.deployer ? contractAddresses.deployer.slice(0, 8) + '...' : 'N/A'}</div>
          <div>Your Address: {account ? account.slice(0, 8) + '...' : 'N/A'}</div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col items-center justify-center min-h-screen px-4">
        <div className="text-center mb-12">
          <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Nexus Swap
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Decentralized token exchange on Nexus blockchain. Swap Token A for Token B at a fixed 1:1 ratio.
          </p>
        </div>

        {/* Error/Success Messages */}
        {(error || success) && (
          <div className="w-full max-w-md mb-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between">
                <span>{error}</span>
                <button type="button" onClick={clearMessages} className="text-red-500 hover:text-red-700">
                  ✕
                </button>
              </div>
            )}
            {success && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between">
                <span>{success}</span>
                <button type="button" onClick={clearMessages} className="text-green-500 hover:text-green-700">
                  ✕
                </button>
              </div>
            )}
          </div>
        )}

        {!account ? (
          <div className="text-center">
            <button
              type="button"
              onClick={connectWallet}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              Connect Wallet
            </button>
            <p className="text-gray-500 mt-4">
              Connect your MetaMask wallet to start swapping tokens
            </p>
          </div>
        ) : !isCorrectNetwork ? (
          <div className="text-center">
            <div className="space-y-4">
              <button
                type="button"
                onClick={switchToNexusNetwork}
                className="bg-orange-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Switch to Nexus Network
              </button>
              <div className="text-center">
                <button
                  type="button"
                  onClick={checkNetwork}
                  className="text-sm text-blue-600 hover:text-blue-800 underline"
                >
                  Already switched? Click to refresh
                </button>
              </div>
            </div>
            <p className="text-gray-500 mt-4">
              Please switch to Nexus Testnet III to continue
            </p>
          </div>
        ) : (
          <div className="w-full max-w-4xl">
            {/* Balances Overview */}
            <div className="bg-white rounded-2xl shadow-xl p-6 border border-gray-200 mb-6">
              <h2 className="text-xl font-semibold mb-4 text-center">Portfolio Overview</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-xl">
                  <p className="text-sm text-gray-600 mb-1">Token A</p>
                  <p className="font-mono text-lg font-bold text-blue-600">
                    {parseFloat(tokenABalance).toFixed(2)}
                  </p>
                  <p className="text-xs text-gray-500">TKNA</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-xl">
                  <p className="text-sm text-gray-600 mb-1">Token B</p>
                  <p className="font-mono text-lg font-bold text-purple-600">
                    {parseFloat(tokenBBalance).toFixed(2)}
                  </p>
                  <p className="text-xs text-gray-500">TKNB</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-xl">
                  <p className="text-sm text-gray-600 mb-1">LP Tokens</p>
                  <p className="font-mono text-lg font-bold text-green-600">
                    {parseFloat(lpTokenBalance).toFixed(2)}
                  </p>
                  <p className="text-xs text-gray-500">NLP</p>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-xl">
                  <p className="text-sm text-gray-600 mb-1">Staked</p>
                  <p className="font-mono text-lg font-bold text-orange-600">
                    {parseFloat(stakedBalance).toFixed(2)}
                  </p>
                  <p className="text-xs text-gray-500">NLP</p>
                </div>
              </div>
              {parseFloat(earnedRewards) > 0 && (
                <div className="mt-4 text-center p-3 bg-yellow-50 rounded-lg">
                  <p className="text-sm text-gray-600">Pending Rewards</p>
                  <p className="font-mono text-xl font-bold text-yellow-600">
                    {parseFloat(earnedRewards).toFixed(4)} TKNB
                  </p>
                </div>
              )}
            </div>

            {/* Tabs */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-200">
              <div className="flex border-b border-gray-200">
                {[
                  { id: 'swap', label: 'Swap', icon: '🔄' },
                  { id: 'liquidity', label: 'Add Liquidity', icon: '💧' },
                  { id: 'stake', label: 'Stake', icon: '🏦' },
                  { id: 'claim', label: 'Claim', icon: '🎁' },
                  { id: 'batch', label: 'Batch Ops', icon: '⚡' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    type="button"
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.label}
                  </button>
                ))}
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {activeTab === 'swap' && (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Swap Tokens</h3>

                    {/* Token Selection */}
                    <div className="mb-6 space-y-4">
                      {/* From Token */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">From</label>
                        <div className="flex space-x-2">
                          <select
                            value={selectedTokenIn}
                            onChange={(e) => setSelectedTokenIn(e.target.value)}
                            className="flex-1 bg-white border-2 border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            disabled={loading}
                          >
                            <option value="TokenA">TKNA - Token A</option>
                            <option value="TokenB">TKNB - Token B</option>
                          </select>
                          <div className="px-3 py-2 bg-gray-100 rounded-lg text-sm">
                            Balance: {selectedTokenIn === 'TokenA' ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4)}
                          </div>
                        </div>
                      </div>

                      {/* Swap Direction Button */}
                      <div className="flex justify-center">
                        <button
                          type="button"
                          onClick={() => {
                            const temp = selectedTokenIn;
                            setSelectedTokenIn(selectedTokenOut);
                            setSelectedTokenOut(temp);
                          }}
                          className="p-2 bg-blue-100 hover:bg-blue-200 rounded-full transition-colors"
                          disabled={loading}
                        >
                          🔄
                        </button>
                      </div>

                      {/* To Token */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">To</label>
                        <div className="flex space-x-2">
                          <select
                            value={selectedTokenOut}
                            onChange={(e) => setSelectedTokenOut(e.target.value)}
                            className="flex-1 bg-white border-2 border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            disabled={loading}
                          >
                            <option value="TokenB">TKNB - Token B</option>
                            <option value="TokenA">TKNA - Token A</option>
                          </select>
                          <div className="px-3 py-2 bg-gray-100 rounded-lg text-sm">
                            Balance: {selectedTokenOut === 'TokenA' ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4)}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Swap Amount Input */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Amount to Swap
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={swapAmount}
                          onChange={(e) => {
                            console.log('Swap input changed:', e.target.value);
                            setSwapAmount(e.target.value);
                          }}
                          placeholder="0.0"
                          className="w-full bg-white border-2 border-gray-300 px-4 py-4 pr-20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400 shadow-sm"
                          disabled={loading}
                          min="0"
                          step="0.01"
                          autoComplete="off"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none">
                          {selectedTokenIn === 'TokenA' ? 'TKNA' : 'TKNB'}
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <p className="text-xs text-gray-500">
                          Exchange rate: 1 {selectedTokenIn === 'TokenA' ? 'TKNA' : 'TKNB'} ≈ 1 {selectedTokenOut === 'TokenA' ? 'TKNA' : 'TKNB'}
                        </p>
                        <div className="flex space-x-2">
                          <button
                            type="button"
                            onClick={() => setSwapAmount('10')}
                            className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200"
                            disabled={loading}
                          >
                            10
                          </button>
                          <button
                            type="button"
                            onClick={() => setSwapAmount('100')}
                            className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200"
                            disabled={loading}
                          >
                            100
                          </button>
                          <button
                            type="button"
                            onClick={() => setSwapAmount(selectedTokenIn === 'TokenA' ? tokenABalance : tokenBBalance)}
                            className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200"
                            disabled={loading || (selectedTokenIn === 'TokenA' ? parseFloat(tokenABalance) === 0 : parseFloat(tokenBBalance) === 0)}
                          >
                            Max
                          </button>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Available: {selectedTokenIn === 'TokenA' ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4)} {selectedTokenIn === 'TokenA' ? 'TKNA' : 'TKNB'} | Input: "{swapAmount}"
                      </p>
                    </div>

                    {/* Liquidity Info */}
                    <div className="text-center mb-6 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600">Pool Liquidity</p>
                      <p className="font-mono text-lg font-semibold text-gray-800">
                        {parseFloat(liquidity).toFixed(2)} Token B
                      </p>
                    </div>

                    {/* Swap Button */}
                    <button
                      type="button"
                      onClick={handleSwap}
                      disabled={loading || !swapAmount || parseFloat(swapAmount) <= 0}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                    >
                      {loading ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          Processing...
                        </div>
                      ) : (
                        `Swap ${selectedTokenIn === 'TokenA' ? 'TKNA' : 'TKNB'} → ${selectedTokenOut === 'TokenA' ? 'TKNA' : 'TKNB'}`
                      )}
                    </button>
                  </div>
                )}

                {activeTab === 'liquidity' && (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Add Liquidity</h3>

                    <div className="space-y-4 mb-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Token A Amount
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            value={liquidityAmountA}
                            onChange={(e) => {
                              console.log('Liquidity A input changed:', e.target.value);
                              setLiquidityAmountA(e.target.value);
                            }}
                            placeholder="0.0"
                            className="w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400"
                            disabled={loading}
                            min="0"
                            step="0.01"
                            autoComplete="off"
                          />
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none">
                            TKNA
                          </div>
                        </div>
                        <div className="flex justify-between items-center mt-2">
                          <p className="text-xs text-gray-500">
                            Available: {parseFloat(tokenABalance).toFixed(4)} TKNA
                          </p>
                          <div className="flex space-x-2">
                            <button
                              type="button"
                              onClick={() => setLiquidityAmountA('10')}
                              className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200"
                              disabled={loading}
                            >
                              10
                            </button>
                            <button
                              type="button"
                              onClick={() => setLiquidityAmountA('50')}
                              className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200"
                              disabled={loading}
                            >
                              50
                            </button>
                            <button
                              type="button"
                              onClick={() => setLiquidityAmountA(tokenABalance)}
                              className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200"
                              disabled={loading || parseFloat(tokenABalance) === 0}
                            >
                              Max
                            </button>
                          </div>
                        </div>
                        <p className="text-xs text-gray-400 mt-1">
                          Input: "{liquidityAmountA}"
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Token B Amount
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            value={liquidityAmountB}
                            onChange={(e) => {
                              console.log('Liquidity B input changed:', e.target.value);
                              setLiquidityAmountB(e.target.value);
                            }}
                            placeholder="0.0"
                            className="w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400"
                            disabled={loading}
                            min="0"
                            step="0.01"
                            autoComplete="off"
                          />
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none">
                            TKNB
                          </div>
                        </div>
                        <div className="flex justify-between items-center mt-2">
                          <p className="text-xs text-gray-500">
                            Available: {parseFloat(tokenBBalance).toFixed(4)} TKNB
                          </p>
                          <div className="flex space-x-2">
                            <button
                              type="button"
                              onClick={() => setLiquidityAmountB('10')}
                              className="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200"
                              disabled={loading}
                            >
                              10
                            </button>
                            <button
                              type="button"
                              onClick={() => setLiquidityAmountB('50')}
                              className="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200"
                              disabled={loading}
                            >
                              50
                            </button>
                            <button
                              type="button"
                              onClick={() => setLiquidityAmountB(tokenBBalance)}
                              className="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200"
                              disabled={loading || parseFloat(tokenBBalance) === 0}
                            >
                              Max
                            </button>
                          </div>
                        </div>
                        <p className="text-xs text-gray-400 mt-1">
                          Input: "{liquidityAmountB}"
                        </p>
                      </div>
                    </div>

                    <button
                      type="button"
                      onClick={handleAddLiquidity}
                      disabled={loading || !liquidityAmountA || !liquidityAmountB}
                      className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                    >
                      {loading ? 'Processing...' : 'Add Liquidity'}
                    </button>
                  </div>
                )}

                {activeTab === 'stake' && (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Stake LP Tokens</h3>

                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Stake Amount (LP Tokens)
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={stakeAmount}
                          onChange={(e) => {
                            console.log('Stake input changed:', e.target.value);
                            setStakeAmount(e.target.value);
                          }}
                          placeholder="0.0"
                          className="w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400"
                          disabled={loading}
                          min="0"
                          step="0.01"
                          autoComplete="off"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none">
                          NLP
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <p className="text-xs text-gray-500">
                          Available: {parseFloat(lpTokenBalance).toFixed(4)} NLP
                        </p>
                        <div className="flex space-x-2">
                          <button
                            type="button"
                            onClick={() => setStakeAmount('1')}
                            className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200"
                            disabled={loading}
                          >
                            1
                          </button>
                          <button
                            type="button"
                            onClick={() => setStakeAmount('5')}
                            className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200"
                            disabled={loading}
                          >
                            5
                          </button>
                          <button
                            type="button"
                            onClick={() => setStakeAmount(lpTokenBalance)}
                            className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200"
                            disabled={loading || parseFloat(lpTokenBalance) === 0}
                          >
                            Max
                          </button>
                        </div>
                      </div>
                      <p className="text-xs text-gray-400 mt-1">
                        Input: "{stakeAmount}"
                      </p>
                    </div>

                    <button
                      type="button"
                      onClick={handleStake}
                      disabled={loading || !stakeAmount || parseFloat(lpTokenBalance) === 0}
                      className="w-full bg-gradient-to-r from-orange-600 to-red-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                    >
                      {loading ? 'Processing...' : 'Stake LP Tokens'}
                    </button>
                  </div>
                )}

                {activeTab === 'claim' && (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Claim Rewards</h3>

                    <div className="text-center mb-6 p-4 bg-yellow-50 rounded-xl">
                      <p className="text-sm text-gray-600 mb-2">Pending Rewards</p>
                      <p className="font-mono text-3xl font-bold text-yellow-600">
                        {parseFloat(earnedRewards).toFixed(6)}
                      </p>
                      <p className="text-xs text-gray-500">TKNB</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <p className="text-xs text-gray-600">Staked</p>
                        <p className="font-mono text-lg font-semibold">
                          {parseFloat(stakedBalance).toFixed(4)}
                        </p>
                        <p className="text-xs text-gray-500">NLP</p>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <p className="text-xs text-gray-600">APY</p>
                        <p className="font-mono text-lg font-semibold text-green-600">
                          ~50%
                        </p>
                        <p className="text-xs text-gray-500">Estimated</p>
                      </div>
                    </div>

                    <button
                      type="button"
                      onClick={handleClaim}
                      disabled={loading || parseFloat(earnedRewards) === 0}
                      className="w-full bg-gradient-to-r from-yellow-600 to-orange-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-yellow-700 hover:to-orange-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                    >
                      {loading ? 'Processing...' : 'Claim Rewards'}
                    </button>
                  </div>
                )}

                {activeTab === 'batch' && (
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Batch Operations</h3>
                    <p className="text-sm text-gray-600 mb-6">
                      Generate multiple transactions for maximum Nexus testnet interaction!
                    </p>

                    <div className="space-y-4">
                      {/* Batch Swaps */}
                      <div className="p-4 bg-blue-50 rounded-xl">
                        <h4 className="font-semibold text-blue-800 mb-2">🔄 Batch Swaps</h4>
                        <p className="text-sm text-blue-700 mb-3">
                          Execute 5 separate swap transactions (10, 20, 30, 40, 50 TKNA)
                        </p>
                        <button
                          type="button"
                          onClick={handleBatchSwaps}
                          disabled={loading || parseFloat(tokenABalance) < 150}
                          className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 disabled:bg-gray-400"
                        >
                          {loading ? 'Processing...' : 'Execute Batch Swaps (5 TXs)'}
                        </button>
                        <p className="text-xs text-blue-600 mt-1">
                          Requires: 150 TKNA minimum
                        </p>
                      </div>

                      {/* Transaction Spam */}
                      <div className="p-4 bg-purple-50 rounded-xl">
                        <h4 className="font-semibold text-purple-800 mb-2">⚡ Transaction Spam</h4>
                        <p className="text-sm text-purple-700 mb-3">
                          Generate 18 approval transactions across all contracts
                        </p>
                        <button
                          type="button"
                          onClick={handleTransactionSpam}
                          disabled={loading}
                          className="w-full bg-purple-600 text-white py-3 rounded-lg font-medium hover:bg-purple-700 transition-all duration-200 disabled:bg-gray-400"
                        >
                          {loading ? 'Processing...' : 'Generate Transaction Spam (18 TXs)'}
                        </button>
                        <p className="text-xs text-purple-600 mt-1">
                          Generates many approval transactions
                        </p>
                      </div>

                      {/* Transaction Counter */}
                      <div className="p-4 bg-green-50 rounded-xl text-center">
                        <h4 className="font-semibold text-green-800 mb-2">📊 Transaction Counter</h4>
                        <p className="text-sm text-green-700 mb-2">
                          Estimated transactions per full cycle:
                        </p>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div className="bg-white p-2 rounded">
                            <div className="font-semibold">Basic Flow</div>
                            <div>8-10 TXs</div>
                          </div>
                          <div className="bg-white p-2 rounded">
                            <div className="font-semibold">With Batches</div>
                            <div>25+ TXs</div>
                          </div>
                        </div>
                      </div>

                      {/* Instructions */}
                      <div className="p-4 bg-yellow-50 rounded-xl">
                        <h4 className="font-semibold text-yellow-800 mb-2">💡 Pro Tips</h4>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          <li>• Use batch operations to generate many transactions quickly</li>
                          <li>• Each operation creates multiple blockchain interactions</li>
                          <li>• Perfect for maximizing Nexus testnet contribution</li>
                          <li>• Monitor your transaction count in MetaMask</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Test Tokens Info */}
              {parseFloat(tokenABalance) === 0 && activeTab === 'swap' && (
                <div className="mx-6 mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h3 className="text-sm font-semibold text-yellow-800 mb-2">Need Test Tokens?</h3>
                  {isDeployerAccount ? (
                    <div>
                      <p className="text-xs text-yellow-700 mb-3">
                        You are the deployer! You have 1,000,000 Token A available.
                      </p>
                      <button
                        type="button"
                        onClick={updateBalances}
                        className="bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200"
                      >
                        Refresh Balance
                      </button>
                    </div>
                  ) : (
                    <div>
                      <p className="text-xs text-yellow-700 mb-3">
                        To get test tokens, switch to the deployer account or ask for a transfer:
                      </p>
                      <div className="text-xs text-yellow-600 mb-3 font-mono bg-yellow-100 p-2 rounded">
                        Deployer: {contractAddresses.deployer?.slice(0, 20)}...
                      </div>
                      <button
                        type="button"
                        onClick={requestTestTokens}
                        disabled={loading}
                        className="bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200 disabled:bg-gray-400"
                      >
                        Show Instructions
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Instructions */}
            <div className="mt-8 text-center text-gray-600">
              <h3 className="font-semibold mb-2">DeFi Flow Guide:</h3>
              <ol className="text-sm space-y-1">
                <li>1. <strong>Swap</strong>: Trade Token A ⇄ Token B</li>
                <li>2. <strong>Add Liquidity</strong>: Provide both tokens → Get LP tokens</li>
                <li>3. <strong>Stake</strong>: Stake LP tokens → Earn rewards</li>
                <li>4. <strong>Claim</strong>: Collect your earned rewards</li>
                <li>5. <strong>Repeat</strong>: More transactions = More Nexus interaction!</li>
              </ol>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
