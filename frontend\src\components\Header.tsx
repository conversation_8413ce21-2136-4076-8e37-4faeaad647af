'use client';

import { useState, useEffect } from 'react';

interface HeaderProps {
  account: string;
  isConnected: boolean;
  isCorrectNetwork: boolean;
  onConnectWallet: () => void;
  onSwitchNetwork: () => void;
  onDisconnectWallet?: () => void;
  nexBalance: string;
  tokenABalance: string;
  tokenBBalance: string;
  showLandingPage?: boolean;
  onBackToLanding?: () => void;
}

export default function Header({
  account,
  isConnected,
  isCorrectNetwork,
  onConnectWallet,
  onSwitchNetwork,
  onDisconnectWallet,
  nexBalance,
  tokenABalance,
  tokenBBalance,
  showLandingPage = true,
  onBackToLanding
}: HeaderProps) {
  const [currentTime, setCurrentTime] = useState<Date | null>(null);

  useEffect(() => {
    // Set initial time on client mount
    setCurrentTime(new Date());
    
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance);
    if (num === 0) return '0.00';
    if (num < 0.01) return '<0.01';
    if (num < 1) return num.toFixed(4);
    if (num < 1000) return num.toFixed(2);
    if (num < 1000000) return `${(num / 1000).toFixed(1)}K`;
    return `${(num / 1000000).toFixed(1)}M`;
  };

  return (
    <header className="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700/50 safe-area-inset">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo & Brand */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-nexus-400 to-nexus-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">N</span>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-white">Nexus DEX</h1>
                <p className="text-xs text-slate-400">Decentralized Exchange</p>
              </div>
              <div className="sm:hidden">
                <h1 className="text-lg font-bold text-white">Nexus</h1>
              </div>
            </div>
            
            {/* Navigation */}
            {!showLandingPage && onBackToLanding && (
              <button
                onClick={onBackToLanding}
                className="ml-2 sm:ml-6 text-slate-400 hover:text-white transition-colors text-sm"
              >
                ← <span className="hidden sm:inline">Back to Home</span>
              </button>
            )}
          </div>

          {/* Network Status & Time - Hidden on mobile */}
          <div className="hidden lg:flex items-center space-x-6">
            <div className="text-center">
              <p className="text-xs text-slate-400">Network</p>
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${isCorrectNetwork ? 'bg-green-400' : 'bg-red-400'}`}></div>
                <span className="text-sm text-white">
                  {isCorrectNetwork ? 'Nexus Testnet III' : 'Wrong Network'}
                </span>
              </div>
            </div>
            
            <div className="text-center">
              <p className="text-xs text-slate-400">Time</p>
              <p className="text-sm text-white font-mono">
                {currentTime ? currentTime.toLocaleTimeString() : '--:--:--'}
              </p>
            </div>
          </div>

          {/* Wallet Section */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Token Balances - Hidden on mobile */}
            {isConnected && isCorrectNetwork && (
              <div className="hidden xl:flex items-center space-x-4 bg-slate-700/50 rounded-lg px-3 py-2">
                <div className="text-center">
                  <p className="text-xs text-slate-400">NEX</p>
                  <p className="text-sm text-white font-mono">{formatBalance(nexBalance)}</p>
                </div>
                <div className="w-px h-8 bg-slate-600"></div>
                <div className="text-center">
                  <p className="text-xs text-slate-400">TKNA</p>
                  <p className="text-sm text-white font-mono">{formatBalance(tokenABalance)}</p>
                </div>
                <div className="w-px h-8 bg-slate-600"></div>
                <div className="text-center">
                  <p className="text-xs text-slate-400">TKNB</p>
                  <p className="text-sm text-white font-mono">{formatBalance(tokenBBalance)}</p>
                </div>
              </div>
            )}

            {/* Wallet Connection */}
            <div className="flex items-center space-x-2">
              {!isConnected ? (
                <button
                  onClick={onConnectWallet}
                  className="bg-nexus-600 hover:bg-nexus-700 text-white px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-sm sm:text-base"
                >
                  <span className="hidden sm:inline">Connect Wallet</span>
                  <span className="sm:hidden">Connect</span>
                </button>
              ) : !isCorrectNetwork ? (
                <button
                  onClick={onSwitchNetwork}
                  className="bg-red-600 hover:bg-red-700 text-white px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-sm sm:text-base"
                >
                  <span className="hidden sm:inline">Switch Network</span>
                  <span className="sm:hidden">Switch</span>
                </button>
              ) : (
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-2 bg-slate-700/50 rounded-lg px-2 sm:px-3 py-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-white font-mono text-xs sm:text-sm">
                      <span className="hidden sm:inline">{formatAddress(account)}</span>
                      <span className="sm:hidden">{account.slice(0, 4)}...{account.slice(-2)}</span>
                    </span>
                  </div>
                  {onDisconnectWallet && (
                    <button
                      onClick={onDisconnectWallet}
                      className="bg-red-600 hover:bg-red-700 text-white px-2 sm:px-3 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors"
                    >
                      <span className="hidden sm:inline">Disconnect</span>
                      <span className="sm:hidden">×</span>
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}