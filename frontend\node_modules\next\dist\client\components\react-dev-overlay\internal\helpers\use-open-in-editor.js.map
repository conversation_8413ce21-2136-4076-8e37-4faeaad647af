{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.ts"], "names": ["useOpenInEditor", "file", "lineNumber", "column", "openInEditor", "useCallback", "params", "URLSearchParams", "append", "String", "self", "fetch", "process", "env", "__NEXT_ROUTER_BASEPATH", "toString", "then", "console", "error"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;uBAFY;AAErB,SAASA,gBAAgB;IAAA,IAAA,EAC9BC,IAAI,EACJC,UAAU,EACVC,MAAM,EAKP,GAR+B,mBAQ5B,CAAC,IAR2B;IAS9B,MAAMC,eAAeC,IAAAA,kBAAW,EAAC;QAC/B,IAAIJ,QAAQ,QAAQC,cAAc,QAAQC,UAAU,MAAM;QAE1D,MAAMG,SAAS,IAAIC;QACnBD,OAAOE,MAAM,CAAC,QAAQP;QACtBK,OAAOE,MAAM,CAAC,cAAcC,OAAOP;QACnCI,OAAOE,MAAM,CAAC,UAAUC,OAAON;QAE/BO,KACGC,KAAK,CACJ,AACEC,CAAAA,QAAQC,GAAG,CAACC,sBAAsB,IAAI,EAAC,IACxC,6BAA0BR,OAAOS,QAAQ,IAE3CC,IAAI,CACH,KAAO,GACP;YACEC,QAAQC,KAAK,CAAC;QAChB;IAEN,GAAG;QAACjB;QAAMC;QAAYC;KAAO;IAE7B,OAAOC;AACT"}