const hre = require("hardhat");
const fs = require("fs");

async function main() {
  console.log("\n🧪 Testing Nexus DeFi Functionality...");
  console.log("=====================================");

  // Load deployed addresses
  const addresses = JSON.parse(fs.readFileSync("./deployedAddresses.json", "utf8"));
  
  // Get signer (deployer)
  const [deployer] = await hre.ethers.getSigners();
  console.log("👤 Testing with account:", deployer.address);

  try {
    // Get contract instances
    const TokenA = await hre.ethers.getContractAt("TokenA", addresses.TokenA);
    const TokenB = await hre.ethers.getContractAt("TokenB", addresses.TokenB);
    const TokenSwap = await hre.ethers.getContractAt("TokenSwap", addresses.TokenSwap);
    const LiquidityPool = await hre.ethers.getContractAt("NexusLiquidityPool", addresses.LiquidityPool);
    const Staking = await hre.ethers.getContractAt("NexusStaking", addresses.Staking);

    console.log("\n📊 Initial Balances:");
    const initialBalanceA = await TokenA.balanceOf(deployer.address);
    const initialBalanceB = await TokenB.balanceOf(deployer.address);
    console.log("├── TokenA:", hre.ethers.formatEther(initialBalanceA));
    console.log("└── TokenB:", hre.ethers.formatEther(initialBalanceB));

    // Test 1: Basic Token Swap
    console.log("\n🔄 Test 1: Token Swap");
    const swapAmount = hre.ethers.parseEther("100");
    
    // Approve and swap
    console.log("├── Approving TokenA for swap...");
    await TokenA.approve(addresses.TokenSwap, swapAmount);
    
    console.log("├── Executing swap: 100 TKNA → TKNB...");
    await TokenSwap.swapAForB(swapAmount);
    
    const balanceAfterSwap = await TokenB.balanceOf(deployer.address);
    console.log("└── ✅ Swap successful! New TKNB balance:", hre.ethers.formatEther(balanceAfterSwap));

    // Test 2: Liquidity Pool Operations
    console.log("\n💧 Test 2: Liquidity Pool");
    const liquidityAmountA = hre.ethers.parseEther("50");
    const liquidityAmountB = hre.ethers.parseEther("50");
    
    console.log("├── Approving tokens for liquidity...");
    await TokenA.approve(addresses.LiquidityPool, liquidityAmountA);
    await TokenB.approve(addresses.LiquidityPool, liquidityAmountB);
    
    console.log("├── Adding liquidity: 50 TKNA + 50 TKNB...");
    await LiquidityPool.addLiquidity(liquidityAmountA, liquidityAmountB);
    
    const lpBalance = await LiquidityPool.balanceOf(deployer.address);
    console.log("└── ✅ Liquidity added! LP tokens received:", hre.ethers.formatEther(lpBalance));

    // Test 3: Liquidity Pool Swap
    console.log("\n🔄 Test 3: Liquidity Pool Swap");
    const poolSwapAmount = hre.ethers.parseEther("10");
    
    console.log("├── Approving TokenA for pool swap...");
    await TokenA.approve(addresses.LiquidityPool, poolSwapAmount);
    
    console.log("├── Executing pool swap: 10 TKNA → TKNB...");
    await LiquidityPool.swap(addresses.TokenA, poolSwapAmount, 0);
    
    const balanceAfterPoolSwap = await TokenB.balanceOf(deployer.address);
    console.log("└── ✅ Pool swap successful! New TKNB balance:", hre.ethers.formatEther(balanceAfterPoolSwap));

    // Test 4: Staking
    console.log("\n🏦 Test 4: Staking");
    const stakeAmount = hre.ethers.parseEther("10");
    
    console.log("├── Approving LP tokens for staking...");
    await LiquidityPool.approve(addresses.Staking, stakeAmount);
    
    console.log("├── Staking 10 LP tokens...");
    await Staking.stake(stakeAmount);
    
    const stakedBalance = await Staking.balances(deployer.address);
    console.log("└── ✅ Staking successful! Staked balance:", hre.ethers.formatEther(stakedBalance));

    // Test 5: Check Tier Information
    console.log("\n🏆 Test 5: Staking Tier Info");
    const tierInfo = await Staking.getUserTierInfo(deployer.address);
    console.log("├── Current Tier:", Number(tierInfo[0]));
    console.log("├── Multiplier:", Number(tierInfo[1]) / 100 + "x");
    console.log("├── Staking Duration:", Number(tierInfo[2]), "seconds");
    console.log("└── Next Tier In:", Number(tierInfo[3]), "seconds");

    // Test 6: Enhanced Token Features
    console.log("\n🔧 Test 6: Enhanced Token Features");
    
    // Check if tokens are pausable
    const tokenAPaused = await TokenA.paused();
    const tokenBPaused = await TokenB.paused();
    console.log("├── TokenA Paused:", tokenAPaused);
    console.log("├── TokenB Paused:", tokenBPaused);
    
    // Get token info
    const tokenAInfo = await TokenA.getTokenInfo();
    console.log("├── TokenA Max Supply:", hre.ethers.formatEther(tokenAInfo[4]));
    console.log("└── TokenA Paused Status:", tokenAInfo[5]);

    // Test 7: Pool Statistics
    console.log("\n📈 Test 7: Pool Statistics");
    const poolStats = await LiquidityPool.getPoolStats();
    console.log("├── Reserve A:", hre.ethers.formatEther(poolStats[0]));
    console.log("├── Reserve B:", hre.ethers.formatEther(poolStats[1]));
    console.log("├── Total LP Supply:", hre.ethers.formatEther(poolStats[2]));
    console.log("├── Volume A:", hre.ethers.formatEther(poolStats[3]));
    console.log("├── Volume B:", hre.ethers.formatEther(poolStats[4]));
    console.log("├── Total Swaps:", Number(poolStats[5]));
    console.log("└── Current Fee Rate:", Number(poolStats[6]) / 100 + "%");

    // Test 8: Staking Statistics
    console.log("\n📊 Test 8: Staking Statistics");
    const stakingStats = await Staking.getPoolStats();
    console.log("├── Total Staked:", hre.ethers.formatEther(stakingStats[0]));
    console.log("├── Total Stakers:", Number(stakingStats[1]));
    console.log("├── Total Rewards Distributed:", hre.ethers.formatEther(stakingStats[2]));
    console.log("├── Current Reward Rate:", Number(stakingStats[3]), "tokens/second");
    console.log("├── Pool Capacity:", hre.ethers.formatEther(stakingStats[4]));
    console.log("└── Max User Stake:", hre.ethers.formatEther(stakingStats[5]));

    // Final balances
    console.log("\n📊 Final Balances:");
    const finalBalanceA = await TokenA.balanceOf(deployer.address);
    const finalBalanceB = await TokenB.balanceOf(deployer.address);
    const finalLPBalance = await LiquidityPool.balanceOf(deployer.address);
    const finalStakedBalance = await Staking.balances(deployer.address);
    
    console.log("├── TokenA:", hre.ethers.formatEther(finalBalanceA));
    console.log("├── TokenB:", hre.ethers.formatEther(finalBalanceB));
    console.log("├── LP Tokens:", hre.ethers.formatEther(finalLPBalance));
    console.log("└── Staked LP:", hre.ethers.formatEther(finalStakedBalance));

    console.log("\n✅ All Tests Passed!");
    console.log("\n🎯 Test Summary:");
    console.log("✅ Token Swap - Working");
    console.log("✅ Liquidity Pool - Working");
    console.log("✅ Pool Swaps - Working");
    console.log("✅ Staking - Working");
    console.log("✅ Tier System - Working");
    console.log("✅ Enhanced Features - Working");
    console.log("✅ Statistics - Working");

    console.log("\n🌐 Frontend Testing:");
    console.log("1. Open: http://localhost:3001");
    console.log("2. Connect MetaMask to Nexus Testnet III");
    console.log("3. Test all DeFi operations through the UI");
    console.log("4. Verify real-time events and transaction tracking");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
