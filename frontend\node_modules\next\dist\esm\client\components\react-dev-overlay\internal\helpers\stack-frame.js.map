{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/stack-frame.ts"], "names": ["getOriginalStackFrame", "source", "type", "errorMessage", "_getOriginalStackFrame", "body", "params", "URLSearchParams", "append", "String", "key", "toString", "controller", "AbortController", "tm", "setTimeout", "abort", "res", "self", "fetch", "process", "env", "__NEXT_ROUTER_BASEPATH", "signal", "finally", "clearTimeout", "ok", "status", "Promise", "reject", "Error", "text", "json", "error", "reason", "external", "expanded", "Boolean", "file", "includes", "originalStackFrame", "sourceStackFrame", "originalCodeFrame", "sourcePackage", "match", "resolve", "err", "catch", "message", "getOriginalStackFrames", "frames", "all", "map", "frame", "getFrameSource", "str", "globalThis", "u", "URL", "location", "origin", "protocol", "pathname", "lineNumber", "column", "slice"], "mappings": "AAmCA,OAAO,SAASA,sBACdC,MAAkB,EAClBC,IAAqC,EACrCC,YAAoB;QAkDlBF,cACAA;IAjDF,eAAeG;YAkCT,aAAa,GACZH,cACCI,+BAAAA;QAnCN,MAAMC,SAAS,IAAIC;QACnBD,OAAOE,MAAM,CAAC,YAAYC,OAAOP,SAAS;QAC1CI,OAAOE,MAAM,CAAC,gBAAgBC,OAAOP,SAAS;QAC9CI,OAAOE,MAAM,CAAC,kBAAkB;QAChCF,OAAOE,MAAM,CAAC,gBAAgBL;QAC9B,IAAK,MAAMO,OAAOT,OAAQ;gBACJ;YAApBK,OAAOE,MAAM,CAACE,KAAK,AAAC,CAAA,CAAA,cAAA,AAACT,MAAc,CAACS,IAAI,YAApB,cAAwB,EAAC,EAAGC,QAAQ;QAC1D;QAEA,MAAMC,aAAa,IAAIC;QACvB,MAAMC,KAAKC,WAAW,IAAMH,WAAWI,KAAK,IAAI;QAChD,MAAMC,MAAM,MAAMC,KACfC,KAAK,CACJ,AACEC,CAAAA,QAAQC,GAAG,CAACC,sBAAsB,IAAI,EAAC,IACxC,oCAAiChB,OAAOK,QAAQ,IACjD;YACEY,QAAQX,WAAWW,MAAM;QAC3B,GAEDC,OAAO,CAAC;YACPC,aAAaX;QACf;QACF,IAAI,CAACG,IAAIS,EAAE,IAAIT,IAAIU,MAAM,KAAK,KAAK;YACjC,OAAOC,QAAQC,MAAM,CAAC,IAAIC,MAAM,MAAMb,IAAIc,IAAI;QAChD;QAEA,MAAM1B,OAA6C,MAAMY,IAAIe,IAAI;YAO5D/B;QANL,OAAO;YACLgC,OAAO;YACPC,QAAQ;YACRC,UAAU;YACVC,UAAU,CAACC,QAET,CAACpC,OAAAA,EAAAA,eAAAA,OAAOqC,IAAI,qBAAXrC,aAAasC,QAAQ,CAAC,sBACrBlC,2BAAAA,KAAKmC,kBAAkB,sBAAvBnC,gCAAAA,yBAAyBiC,IAAI,qBAA7BjC,8BAA+BkC,QAAQ,CAAC,4BADzCtC,OAEC;YAEJwC,kBAAkBxC;YAClBuC,oBAAoBnC,KAAKmC,kBAAkB;YAC3CE,mBAAmBrC,KAAKqC,iBAAiB,IAAI;YAC7CC,eAAetC,KAAKsC,aAAa;QACnC;IACF;IAEA,IACE1C,OAAOqC,IAAI,KAAK,mBAChBrC,eAAAA,OAAOqC,IAAI,qBAAXrC,aAAa2C,KAAK,CAAC,gBACnB3C,gBAAAA,OAAOqC,IAAI,qBAAXrC,cAAa2C,KAAK,CAAC,iBACnB;QACA,OAAOhB,QAAQiB,OAAO,CAAC;YACrBZ,OAAO;YACPC,QAAQ;YACRC,UAAU;YACVC,UAAU;YACVK,kBAAkBxC;YAClBuC,oBAAoB;YACpBE,mBAAmB;QACrB;IACF;QAIUI,cAAAA;IAFV,OAAO1C,yBAAyB2C,KAAK,CAAC,CAACD,MAAgB,CAAA;YACrDb,OAAO;YACPC,QAAQY,CAAAA,OAAAA,CAAAA,eAAAA,uBAAAA,IAAKE,OAAO,YAAZF,eAAgBA,uBAAAA,IAAKnC,QAAQ,cAA7BmC,OAAmC;YAC3CX,UAAU;YACVC,UAAU;YACVK,kBAAkBxC;YAClBuC,oBAAoB;YACpBE,mBAAmB;QACrB,CAAA;AACF;AAEA,OAAO,SAASO,uBACdC,MAAoB,EACpBhD,IAAqC,EACrCC,YAAoB;IAEpB,OAAOyB,QAAQuB,GAAG,CAChBD,OAAOE,GAAG,CAAC,CAACC,QAAUrD,sBAAsBqD,OAAOnD,MAAMC;AAE7D;AAEA,OAAO,SAASmD,eAAeD,KAAiB;IAC9C,IAAIE,MAAM;IACV,IAAI;YAMAC;QALF,MAAMC,IAAI,IAAIC,IAAIL,MAAMf,IAAI;QAE5B,4CAA4C;QAC5C,IACE,OAAOkB,eAAe,eACtBA,EAAAA,uBAAAA,WAAWG,QAAQ,qBAAnBH,qBAAqBI,MAAM,MAAKH,EAAEG,MAAM,EACxC;YACA,gEAAgE;YAChE,8CAA8C;YAC9C,IAAIH,EAAEG,MAAM,KAAK,QAAQ;gBACvBL,OAAOE,EAAEI,QAAQ;YACnB,OAAO;gBACLN,OAAOE,EAAEG,MAAM;YACjB;QACF;QAEA,qEAAqE;QACrE,cAAc;QACdL,OAAOE,EAAEK,QAAQ;QACjBP,OAAO;IACT,EAAE,UAAM;QACNA,OAAO,AAACF,CAAAA,MAAMf,IAAI,IAAI,WAAU,IAAK;IACvC;IAEA,IAAIe,MAAMU,UAAU,IAAI,MAAM;QAC5B,IAAIV,MAAMW,MAAM,IAAI,MAAM;YACxBT,OAAO,AAAC,MAAGF,MAAMU,UAAU,GAAC,MAAGV,MAAMW,MAAM,GAAC;QAC9C,OAAO;YACLT,OAAO,AAAC,MAAGF,MAAMU,UAAU,GAAC;QAC9B;IACF;IACA,OAAOR,IAAIU,KAAK,CAAC,GAAG,CAAC;AACvB"}