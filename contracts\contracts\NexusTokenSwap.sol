// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title NexusTokenSwap
 * @dev Enhanced swap contract that enables users to swap between NXI, NXY, and NEX tokens
 * Features:
 * - NXI ⇄ NXY token swaps at configurable rates
 * - NEX ⇄ NXI swaps using NXI contract's built-in functionality
 * - NEX ⇄ NXY swaps using NXY contract's built-in functionality
 * - Configurable swap fees
 * - Emergency pause functionality
 * - Owner controls for rate adjustments
 */
contract NexusTokenSwap is Ownable, ReentrancyGuard, Pausable {
    IERC20 public nxiToken;
    IERC20 public nxyToken;

    // Swap rates (base rate * 1e18 for precision)
    uint256 public nxiToNxyRate = 1.2e18; // 1 NXI = 1.2 NXY
    uint256 public nxyToNxiRate = 0.8e18; // 1 NXY = 0.8 NXI
    
    // Fee settings (in basis points, 100 = 1%)
    uint256 public swapFee = 30; // 0.3% default fee
    uint256 public constant MAX_FEE = 500; // 5% maximum fee
    
    // Fee collection
    address public feeRecipient;
    uint256 public collectedFees;
    
    // Minimum swap amounts to prevent spam
    uint256 public minSwapAmount = 1e18; // 1 token minimum
    
    // Events
    event TokenSwap(
        address indexed user,
        address indexed tokenIn,
        address indexed tokenOut,
        uint256 amountIn,
        uint256 amountOut,
        uint256 fee
    );
    event NEXSwap(
        address indexed user,
        string swapType,
        uint256 nexAmount,
        uint256 tokenAmount
    );
    event SwapRateUpdated(string rateName, uint256 oldRate, uint256 newRate);
    event SwapFeeUpdated(uint256 oldFee, uint256 newFee);
    event FeeRecipientUpdated(address oldRecipient, address newRecipient);
    event FeesWithdrawn(address recipient, uint256 amount);
    event MinSwapAmountUpdated(uint256 oldAmount, uint256 newAmount);

    /**
     * @dev Constructor that sets up the swap contract
     * @param _nxiToken Address of the NXI token contract
     * @param _nxyToken Address of the NXY token contract
     * @param _feeRecipient Address to receive swap fees
     */
    constructor(
        address _nxiToken,
        address _nxyToken,
        address _feeRecipient
    ) Ownable(msg.sender) {
        require(_nxiToken != address(0), "NXI token address cannot be zero");
        require(_nxyToken != address(0), "NXY token address cannot be zero");
        require(_feeRecipient != address(0), "Fee recipient cannot be zero");
        
        nxiToken = IERC20(_nxiToken);
        nxyToken = IERC20(_nxyToken);
        feeRecipient = _feeRecipient;
    }

    /**
     * @dev Swap NXI tokens for NXY tokens
     * @param nxiAmount Amount of NXI tokens to swap
     * @return nxyAmount Amount of NXY tokens received
     */
    function swapNXIForNXY(uint256 nxiAmount) external nonReentrant whenNotPaused returns (uint256 nxyAmount) {
        require(nxiAmount >= minSwapAmount, "Amount below minimum swap threshold");
        require(nxiToken.balanceOf(msg.sender) >= nxiAmount, "Insufficient NXI balance");
        
        // Calculate output amount
        nxyAmount = (nxiAmount * nxiToNxyRate) / 1e18;
        
        // Calculate fee
        uint256 fee = (nxyAmount * swapFee) / 10000;
        uint256 nxyAmountAfterFee = nxyAmount - fee;
        
        require(nxyToken.balanceOf(address(this)) >= nxyAmountAfterFee, "Insufficient NXY liquidity");
        
        // Transfer NXI from user to this contract
        require(nxiToken.transferFrom(msg.sender, address(this), nxiAmount), "NXI transfer failed");
        
        // Transfer NXY from this contract to user (minus fee)
        require(nxyToken.transfer(msg.sender, nxyAmountAfterFee), "NXY transfer failed");
        
        // Track collected fees
        collectedFees += fee;
        
        emit TokenSwap(
            msg.sender,
            address(nxiToken),
            address(nxyToken),
            nxiAmount,
            nxyAmountAfterFee,
            fee
        );
        
        return nxyAmountAfterFee;
    }

    /**
     * @dev Swap NXY tokens for NXI tokens
     * @param nxyAmount Amount of NXY tokens to swap
     * @return nxiAmount Amount of NXI tokens received
     */
    function swapNXYForNXI(uint256 nxyAmount) external nonReentrant whenNotPaused returns (uint256 nxiAmount) {
        require(nxyAmount >= minSwapAmount, "Amount below minimum swap threshold");
        require(nxyToken.balanceOf(msg.sender) >= nxyAmount, "Insufficient NXY balance");
        
        // Calculate output amount
        nxiAmount = (nxyAmount * nxyToNxiRate) / 1e18;
        
        // Calculate fee
        uint256 fee = (nxiAmount * swapFee) / 10000;
        uint256 nxiAmountAfterFee = nxiAmount - fee;
        
        require(nxiToken.balanceOf(address(this)) >= nxiAmountAfterFee, "Insufficient NXI liquidity");
        
        // Transfer NXY from user to this contract
        require(nxyToken.transferFrom(msg.sender, address(this), nxyAmount), "NXY transfer failed");
        
        // Transfer NXI from this contract to user (minus fee)
        require(nxiToken.transfer(msg.sender, nxiAmountAfterFee), "NXI transfer failed");
        
        // Track collected fees
        collectedFees += fee;
        
        emit TokenSwap(
            msg.sender,
            address(nxyToken),
            address(nxiToken),
            nxyAmount,
            nxiAmountAfterFee,
            fee
        );
        
        return nxiAmountAfterFee;
    }

    /**
     * @dev Swap NEX for NXI tokens using NXI contract's built-in functionality
     * This is a convenience function that calls the NXI contract directly
     */
    function swapNEXForNXI() external payable nonReentrant whenNotPaused {
        require(msg.value > 0, "Must send NEX to swap");
        
        // Call NXI contract's swap function
        (bool success, ) = address(nxiToken).call{value: msg.value}("");
        require(success, "NEX to NXI swap failed");
        
        emit NEXSwap(msg.sender, "NEX_TO_NXI", msg.value, 0);
    }

    /**
     * @dev Swap NEX for NXY tokens using NXY contract's built-in functionality
     * This is a convenience function that calls the NXY contract directly
     */
    function swapNEXForNXY() external payable nonReentrant whenNotPaused {
        require(msg.value > 0, "Must send NEX to swap");
        
        // Call NXY contract's swap function
        (bool success, ) = address(nxyToken).call{value: msg.value}("");
        require(success, "NEX to NXY swap failed");
        
        emit NEXSwap(msg.sender, "NEX_TO_NXY", msg.value, 0);
    }

    /**
     * @dev Set NXI to NXY swap rate (only owner)
     * @param newRate New swap rate (scaled by 1e18)
     */
    function setNXIToNXYRate(uint256 newRate) external onlyOwner {
        require(newRate > 0, "Rate must be positive");
        uint256 oldRate = nxiToNxyRate;
        nxiToNxyRate = newRate;
        emit SwapRateUpdated("NXI_TO_NXY", oldRate, newRate);
    }

    /**
     * @dev Set NXY to NXI swap rate (only owner)
     * @param newRate New swap rate (scaled by 1e18)
     */
    function setNXYToNXIRate(uint256 newRate) external onlyOwner {
        require(newRate > 0, "Rate must be positive");
        uint256 oldRate = nxyToNxiRate;
        nxyToNxiRate = newRate;
        emit SwapRateUpdated("NXY_TO_NXI", oldRate, newRate);
    }

    /**
     * @dev Set swap fee (only owner)
     * @param newFee New fee in basis points (100 = 1%)
     */
    function setSwapFee(uint256 newFee) external onlyOwner {
        require(newFee <= MAX_FEE, "Fee exceeds maximum");
        uint256 oldFee = swapFee;
        swapFee = newFee;
        emit SwapFeeUpdated(oldFee, newFee);
    }

    /**
     * @dev Set fee recipient (only owner)
     * @param newRecipient New fee recipient address
     */
    function setFeeRecipient(address newRecipient) external onlyOwner {
        require(newRecipient != address(0), "Fee recipient cannot be zero");
        address oldRecipient = feeRecipient;
        feeRecipient = newRecipient;
        emit FeeRecipientUpdated(oldRecipient, newRecipient);
    }

    /**
     * @dev Set minimum swap amount (only owner)
     * @param newMinAmount New minimum swap amount
     */
    function setMinSwapAmount(uint256 newMinAmount) external onlyOwner {
        uint256 oldAmount = minSwapAmount;
        minSwapAmount = newMinAmount;
        emit MinSwapAmountUpdated(oldAmount, newMinAmount);
    }

    /**
     * @dev Withdraw collected fees (only owner)
     */
    function withdrawFees() external onlyOwner {
        require(collectedFees > 0, "No fees to withdraw");
        
        uint256 nxiBalance = nxiToken.balanceOf(address(this));
        uint256 nxyBalance = nxyToken.balanceOf(address(this));
        
        // Withdraw proportional amounts of both tokens
        if (nxiBalance > 0) {
            uint256 nxiToWithdraw = (nxiBalance * collectedFees) / (collectedFees + 1e18);
            nxiToken.transfer(feeRecipient, nxiToWithdraw);
        }
        
        if (nxyBalance > 0) {
            uint256 nxyToWithdraw = (nxyBalance * collectedFees) / (collectedFees + 1e18);
            nxyToken.transfer(feeRecipient, nxyToWithdraw);
        }
        
        emit FeesWithdrawn(feeRecipient, collectedFees);
        collectedFees = 0;
    }

    /**
     * @dev Emergency withdrawal of tokens (only owner)
     * @param token Token to withdraw
     * @param amount Amount to withdraw
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }

    /**
     * @dev Emergency withdrawal of NEX (only owner)
     * @param amount Amount of NEX to withdraw
     */
    function emergencyWithdrawNEX(uint256 amount) external onlyOwner {
        require(amount <= address(this).balance, "Insufficient NEX balance");
        payable(owner()).transfer(amount);
    }

    /**
     * @dev Pause contract (only owner)
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause contract (only owner)
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev Get quote for NXI to NXY swap
     * @param nxiAmount Amount of NXI to swap
     * @return nxyAmount Amount of NXY that would be received (after fees)
     * @return fee Fee amount in NXY
     */
    function getQuoteNXIToNXY(uint256 nxiAmount) external view returns (uint256 nxyAmount, uint256 fee) {
        nxyAmount = (nxiAmount * nxiToNxyRate) / 1e18;
        fee = (nxyAmount * swapFee) / 10000;
        nxyAmount = nxyAmount - fee;
    }

    /**
     * @dev Get quote for NXY to NXI swap
     * @param nxyAmount Amount of NXY to swap
     * @return nxiAmount Amount of NXI that would be received (after fees)
     * @return fee Fee amount in NXI
     */
    function getQuoteNXYToNXI(uint256 nxyAmount) external view returns (uint256 nxiAmount, uint256 fee) {
        nxiAmount = (nxyAmount * nxyToNxiRate) / 1e18;
        fee = (nxiAmount * swapFee) / 10000;
        nxiAmount = nxiAmount - fee;
    }

    /**
     * @dev Get contract liquidity information
     * @return nxiBalance NXI token balance of this contract
     * @return nxyBalance NXY token balance of this contract
     * @return nexBalance NEX balance of this contract
     */
    function getLiquidityInfo() external view returns (
        uint256 nxiBalance,
        uint256 nxyBalance,
        uint256 nexBalance
    ) {
        return (
            nxiToken.balanceOf(address(this)),
            nxyToken.balanceOf(address(this)),
            address(this).balance
        );
    }

    /**
     * @dev Get comprehensive swap information
     */
    function getSwapInfo() external view returns (
        uint256 nxiToNxySwapRate,
        uint256 nxyToNxiSwapRate,
        uint256 currentSwapFee,
        uint256 minimumSwapAmount,
        uint256 totalCollectedFees,
        address currentFeeRecipient,
        bool isPaused
    ) {
        return (
            nxiToNxyRate,
            nxyToNxiRate,
            swapFee,
            minSwapAmount,
            collectedFees,
            feeRecipient,
            paused()
        );
    }

    /**
     * @dev Receive function to handle direct NEX transfers
     * Automatically swaps NEX for NXI tokens
     */
    receive() external payable {
        if (msg.value > 0 && !paused()) {
            // Default to swapping for NXI tokens
            (bool success, ) = address(nxiToken).call{value: msg.value}("");
            if (success) {
                emit NEXSwap(msg.sender, "NEX_TO_NXI_AUTO", msg.value, 0);
            }
        }
    }
}