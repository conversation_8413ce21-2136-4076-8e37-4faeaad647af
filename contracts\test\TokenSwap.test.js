const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("TokenSwap", function () {
  let tokenA, tokenB, tokenSwap;
  let owner, user1, user2;
  const initialSupply = ethers.parseEther("1000000"); // 1M tokens
  const liquidityAmount = ethers.parseEther("500000"); // 500K tokens

  beforeEach(async function () {
    [owner, user1, user2] = await ethers.getSigners();

    // Deploy TokenA
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(initialSupply);
    await tokenA.waitForDeployment();

    // Deploy TokenB
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(initialSupply);
    await tokenB.waitForDeployment();

    // Deploy TokenSwap
    const TokenSwap = await ethers.getContractFactory("TokenSwap");
    tokenSwap = await TokenSwap.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress()
    );
    await tokenSwap.waitForDeployment();

    // Add liquidity to swap contract
    await tokenB.transfer(await tokenSwap.getAddress(), liquidityAmount);

    // Transfer some TokenA to user1 for testing
    await tokenA.transfer(user1.address, ethers.parseEther("1000"));
  });

  describe("Deployment", function () {
    it("Should set the correct token addresses", async function () {
      expect(await tokenSwap.tokenA()).to.equal(await tokenA.getAddress());
      expect(await tokenSwap.tokenB()).to.equal(await tokenB.getAddress());
    });

    it("Should have correct initial liquidity", async function () {
      const liquidity = await tokenSwap.getTokenBLiquidity();
      expect(liquidity).to.equal(liquidityAmount);
    });

    it("Should reject zero addresses in constructor", async function () {
      const TokenSwap = await ethers.getContractFactory("TokenSwap");
      
      await expect(
        TokenSwap.deploy(ethers.ZeroAddress, await tokenB.getAddress())
      ).to.be.revertedWith("TokenA address cannot be zero");

      await expect(
        TokenSwap.deploy(await tokenA.getAddress(), ethers.ZeroAddress)
      ).to.be.revertedWith("TokenB address cannot be zero");
    });
  });

  describe("Token Swap", function () {
    it("Should successfully swap tokens", async function () {
      const swapAmount = ethers.parseEther("100");
      
      // Approve TokenSwap to spend user1's TokenA
      await tokenA.connect(user1).approve(await tokenSwap.getAddress(), swapAmount);
      
      // Get initial balances
      const initialTokenABalance = await tokenA.balanceOf(user1.address);
      const initialTokenBBalance = await tokenB.balanceOf(user1.address);
      
      // Perform swap
      await tokenSwap.connect(user1).swap(swapAmount);
      
      // Check final balances
      const finalTokenABalance = await tokenA.balanceOf(user1.address);
      const finalTokenBBalance = await tokenB.balanceOf(user1.address);
      
      expect(finalTokenABalance).to.equal(initialTokenABalance - swapAmount);
      expect(finalTokenBBalance).to.equal(initialTokenBBalance + swapAmount);
    });

    it("Should emit Swap event", async function () {
      const swapAmount = ethers.parseEther("100");
      
      await tokenA.connect(user1).approve(await tokenSwap.getAddress(), swapAmount);
      
      await expect(tokenSwap.connect(user1).swap(swapAmount))
        .to.emit(tokenSwap, "Swap")
        .withArgs(user1.address, swapAmount);
    });

    it("Should fail if amount is zero", async function () {
      await expect(
        tokenSwap.connect(user1).swap(0)
      ).to.be.revertedWith("Amount must be greater than 0");
    });

    it("Should fail if insufficient allowance", async function () {
      const swapAmount = ethers.parseEther("100");
      
      // Don't approve or approve less than needed
      await tokenA.connect(user1).approve(await tokenSwap.getAddress(), swapAmount - 1n);
      
      await expect(
        tokenSwap.connect(user1).swap(swapAmount)
      ).to.be.revertedWith("Transfer Token A failed");
    });

    it("Should fail if insufficient TokenB liquidity", async function () {
      const swapAmount = liquidityAmount + ethers.parseEther("1"); // More than available liquidity
      
      // Transfer enough TokenA to user1
      await tokenA.transfer(user1.address, swapAmount);
      await tokenA.connect(user1).approve(await tokenSwap.getAddress(), swapAmount);
      
      await expect(
        tokenSwap.connect(user1).swap(swapAmount)
      ).to.be.revertedWith("Insufficient Token B liquidity");
    });

    it("Should fail if user has insufficient TokenA balance", async function () {
      const userBalance = await tokenA.balanceOf(user1.address);
      const swapAmount = userBalance + ethers.parseEther("1"); // More than user has
      
      await tokenA.connect(user1).approve(await tokenSwap.getAddress(), swapAmount);
      
      await expect(
        tokenSwap.connect(user1).swap(swapAmount)
      ).to.be.revertedWith("Transfer Token A failed");
    });
  });

  describe("Liquidity Management", function () {
    it("Should correctly track TokenB liquidity", async function () {
      const initialLiquidity = await tokenSwap.getTokenBLiquidity();
      const swapAmount = ethers.parseEther("100");
      
      await tokenA.connect(user1).approve(await tokenSwap.getAddress(), swapAmount);
      await tokenSwap.connect(user1).swap(swapAmount);
      
      const finalLiquidity = await tokenSwap.getTokenBLiquidity();
      expect(finalLiquidity).to.equal(initialLiquidity - swapAmount);
    });

    it("Should correctly track TokenA balance in contract", async function () {
      const initialBalance = await tokenSwap.getTokenABalance();
      const swapAmount = ethers.parseEther("100");
      
      await tokenA.connect(user1).approve(await tokenSwap.getAddress(), swapAmount);
      await tokenSwap.connect(user1).swap(swapAmount);
      
      const finalBalance = await tokenSwap.getTokenABalance();
      expect(finalBalance).to.equal(initialBalance + swapAmount);
    });
  });

  describe("Multiple Swaps", function () {
    it("Should handle multiple swaps correctly", async function () {
      const swapAmount = ethers.parseEther("50");
      
      // First swap
      await tokenA.connect(user1).approve(await tokenSwap.getAddress(), swapAmount);
      await tokenSwap.connect(user1).swap(swapAmount);
      
      // Second swap
      await tokenA.connect(user1).approve(await tokenSwap.getAddress(), swapAmount);
      await tokenSwap.connect(user1).swap(swapAmount);
      
      // Check final balances
      const finalTokenABalance = await tokenA.balanceOf(user1.address);
      const finalTokenBBalance = await tokenB.balanceOf(user1.address);
      
      expect(finalTokenABalance).to.equal(ethers.parseEther("900")); // 1000 - 100
      expect(finalTokenBBalance).to.equal(ethers.parseEther("100")); // 0 + 100
    });
  });
});
