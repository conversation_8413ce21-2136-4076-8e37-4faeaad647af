// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title Governance
 * @dev DAO governance contract with proposal creation and voting
 * Generates many transactions for Nexus testnet interaction
 */
contract Governance is ReentrancyGuard, Ownable {
    IERC20 public governanceToken;
    
    uint256 public proposalCount = 0;
    uint256 public constant VOTING_PERIOD = 3 days;
    uint256 public constant EXECUTION_DELAY = 1 days;
    uint256 public constant PROPOSAL_THRESHOLD = 1000 * 1e18; // 1000 tokens to create proposal
    uint256 public constant QUORUM = 10000 * 1e18; // 10000 tokens for quorum
    
    enum ProposalState {
        Pending,
        Active,
        Succeeded,
        Defeated,
        Executed,
        Cancelled
    }
    
    struct Proposal {
        uint256 id;
        address proposer;
        string title;
        string description;
        uint256 startTime;
        uint256 endTime;
        uint256 executionTime;
        uint256 forVotes;
        uint256 againstVotes;
        uint256 abstainVotes;
        bool executed;
        bool cancelled;
        mapping(address => bool) hasVoted;
        mapping(address => uint256) votes;
    }
    
    mapping(uint256 => Proposal) public proposals;
    mapping(address => uint256) public votingPower;
    mapping(address => uint256) public delegatedVotes;
    mapping(address => address) public delegates;
    
    event ProposalCreated(
        uint256 indexed proposalId,
        address indexed proposer,
        string title,
        string description,
        uint256 startTime,
        uint256 endTime
    );
    
    event VoteCast(
        address indexed voter,
        uint256 indexed proposalId,
        uint8 support,
        uint256 weight
    );
    
    event ProposalExecuted(uint256 indexed proposalId);
    event ProposalCancelled(uint256 indexed proposalId);
    event DelegateChanged(address indexed delegator, address indexed fromDelegate, address indexed toDelegate);
    event VotingPowerUpdated(address indexed user, uint256 newPower);
    
    constructor(address _governanceToken) Ownable(msg.sender) {
        governanceToken = IERC20(_governanceToken);
    }
    
    /**
     * @dev Create a new proposal
     * Generates transaction: createProposal
     */
    function createProposal(
        string memory title,
        string memory description
    ) external returns (uint256) {
        require(
            governanceToken.balanceOf(msg.sender) >= PROPOSAL_THRESHOLD,
            "Insufficient tokens to create proposal"
        );
        
        uint256 proposalId = proposalCount++;
        Proposal storage proposal = proposals[proposalId];
        
        proposal.id = proposalId;
        proposal.proposer = msg.sender;
        proposal.title = title;
        proposal.description = description;
        proposal.startTime = block.timestamp;
        proposal.endTime = block.timestamp + VOTING_PERIOD;
        proposal.executionTime = proposal.endTime + EXECUTION_DELAY;
        
        emit ProposalCreated(
            proposalId,
            msg.sender,
            title,
            description,
            proposal.startTime,
            proposal.endTime
        );
        
        return proposalId;
    }
    
    /**
     * @dev Vote on a proposal
     * Generates transaction: vote
     * @param proposalId The proposal to vote on
     * @param support 0=against, 1=for, 2=abstain
     */
    function vote(uint256 proposalId, uint8 support) external {
        require(proposalId < proposalCount, "Invalid proposal");
        require(support <= 2, "Invalid vote type");
        
        Proposal storage proposal = proposals[proposalId];
        require(block.timestamp >= proposal.startTime, "Voting not started");
        require(block.timestamp <= proposal.endTime, "Voting ended");
        require(!proposal.hasVoted[msg.sender], "Already voted");
        
        uint256 weight = getVotingPower(msg.sender);
        require(weight > 0, "No voting power");
        
        proposal.hasVoted[msg.sender] = true;
        proposal.votes[msg.sender] = weight;
        
        if (support == 0) {
            proposal.againstVotes += weight;
        } else if (support == 1) {
            proposal.forVotes += weight;
        } else {
            proposal.abstainVotes += weight;
        }
        
        emit VoteCast(msg.sender, proposalId, support, weight);
    }
    
    /**
     * @dev Delegate voting power to another address
     * Generates transaction: delegate
     */
    function delegate(address delegatee) external {
        address currentDelegate = delegates[msg.sender];
        require(delegatee != currentDelegate, "Already delegated to this address");
        
        uint256 delegatorBalance = governanceToken.balanceOf(msg.sender);
        
        // Remove votes from current delegate
        if (currentDelegate != address(0)) {
            delegatedVotes[currentDelegate] -= delegatorBalance;
        }
        
        // Add votes to new delegate
        if (delegatee != address(0)) {
            delegatedVotes[delegatee] += delegatorBalance;
        }
        
        delegates[msg.sender] = delegatee;
        
        emit DelegateChanged(msg.sender, currentDelegate, delegatee);
    }
    
    /**
     * @dev Update voting power (call after token balance changes)
     * Generates transaction: updateVotingPower
     */
    function updateVotingPower(address user) external {
        uint256 balance = governanceToken.balanceOf(user);
        uint256 oldPower = votingPower[user];
        
        votingPower[user] = balance;
        
        // Update delegated votes if user has delegated
        address delegatee = delegates[user];
        if (delegatee != address(0)) {
            delegatedVotes[delegatee] = delegatedVotes[delegatee] - oldPower + balance;
        }
        
        emit VotingPowerUpdated(user, balance);
    }
    
    /**
     * @dev Execute a successful proposal
     * Generates transaction: executeProposal
     */
    function executeProposal(uint256 proposalId) external {
        require(proposalId < proposalCount, "Invalid proposal");
        
        Proposal storage proposal = proposals[proposalId];
        require(block.timestamp >= proposal.executionTime, "Execution delay not met");
        require(!proposal.executed, "Already executed");
        require(!proposal.cancelled, "Proposal cancelled");
        require(getProposalState(proposalId) == ProposalState.Succeeded, "Proposal not succeeded");
        
        proposal.executed = true;
        
        emit ProposalExecuted(proposalId);
    }
    
    /**
     * @dev Cancel a proposal (proposer or owner only)
     * Generates transaction: cancelProposal
     */
    function cancelProposal(uint256 proposalId) external {
        require(proposalId < proposalCount, "Invalid proposal");
        
        Proposal storage proposal = proposals[proposalId];
        require(
            msg.sender == proposal.proposer || msg.sender == owner(),
            "Not authorized to cancel"
        );
        require(!proposal.executed, "Already executed");
        require(!proposal.cancelled, "Already cancelled");
        
        proposal.cancelled = true;
        
        emit ProposalCancelled(proposalId);
    }
    
    /**
     * @dev Batch voting on multiple proposals
     * Generates multiple transactions
     */
    function batchVote(uint256[] calldata proposalIds, uint8[] calldata supports) external {
        require(proposalIds.length == supports.length, "Array length mismatch");
        
        for (uint256 i = 0; i < proposalIds.length; i++) {
            vote(proposalIds[i], supports[i]);
        }
    }
    
    /**
     * @dev Create multiple proposals at once
     * Generates multiple transactions
     */
    function batchCreateProposals(
        string[] calldata titles,
        string[] calldata descriptions
    ) external returns (uint256[] memory) {
        require(titles.length == descriptions.length, "Array length mismatch");
        
        uint256[] memory proposalIds = new uint256[](titles.length);
        
        for (uint256 i = 0; i < titles.length; i++) {
            proposalIds[i] = createProposal(titles[i], descriptions[i]);
        }
        
        return proposalIds;
    }
    
    // View functions
    function getProposalState(uint256 proposalId) public view returns (ProposalState) {
        require(proposalId < proposalCount, "Invalid proposal");
        
        Proposal storage proposal = proposals[proposalId];
        
        if (proposal.cancelled) {
            return ProposalState.Cancelled;
        }
        
        if (proposal.executed) {
            return ProposalState.Executed;
        }
        
        if (block.timestamp < proposal.startTime) {
            return ProposalState.Pending;
        }
        
        if (block.timestamp <= proposal.endTime) {
            return ProposalState.Active;
        }
        
        uint256 totalVotes = proposal.forVotes + proposal.againstVotes + proposal.abstainVotes;
        
        if (totalVotes < QUORUM || proposal.forVotes <= proposal.againstVotes) {
            return ProposalState.Defeated;
        }
        
        return ProposalState.Succeeded;
    }
    
    function getVotingPower(address user) public view returns (uint256) {
        return governanceToken.balanceOf(user) + delegatedVotes[user];
    }
    
    function getProposalInfo(uint256 proposalId) external view returns (
        address proposer,
        string memory title,
        string memory description,
        uint256 startTime,
        uint256 endTime,
        uint256 forVotes,
        uint256 againstVotes,
        uint256 abstainVotes,
        ProposalState state
    ) {
        require(proposalId < proposalCount, "Invalid proposal");
        
        Proposal storage proposal = proposals[proposalId];
        
        return (
            proposal.proposer,
            proposal.title,
            proposal.description,
            proposal.startTime,
            proposal.endTime,
            proposal.forVotes,
            proposal.againstVotes,
            proposal.abstainVotes,
            getProposalState(proposalId)
        );
    }
    
    function hasVoted(uint256 proposalId, address voter) external view returns (bool) {
        require(proposalId < proposalCount, "Invalid proposal");
        return proposals[proposalId].hasVoted[voter];
    }
}
