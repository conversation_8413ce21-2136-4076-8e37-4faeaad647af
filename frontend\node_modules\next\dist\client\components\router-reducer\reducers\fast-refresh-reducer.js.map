{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/fast-refresh-reducer.ts"], "names": ["fastRefreshReducer", "fastRefreshReducerImpl", "state", "action", "origin", "mutable", "href", "canonicalUrl", "preserveCustomHistoryState", "cache", "createEmptyCacheNode", "data", "fetchServerResponse", "URL", "tree", "nextUrl", "buildId", "then", "flightData", "canonicalUrlOverride", "handleExternalUrl", "pushRef", "pendingPush", "currentTree", "currentCache", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "applied", "applyFlightData", "patchedTree", "handleMutable", "fastRefreshReducerNoop", "_action", "process", "env", "NODE_ENV"], "mappings": ";;;;+BAqHaA;;;eAAAA;;;qCArHuB;mCACF;6CACU;6CACA;iCAOV;+BACJ;iCACE;2BAEK;AAErC,wFAAwF;AACxF,SAASC,uBACPC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/BF,QAAQG,0BAA0B,GAAG;IAErC,MAAMC,QAAmBC,IAAAA,+BAAoB;IAC7C,uDAAuD;IACvD,wCAAwC;IACxCD,MAAME,IAAI,GAAGC,IAAAA,wCAAmB,EAC9B,IAAIC,IAAIP,MAAMF,SACd;QAACF,MAAMY,IAAI,CAAC,EAAE;QAAEZ,MAAMY,IAAI,CAAC,EAAE;QAAEZ,MAAMY,IAAI,CAAC,EAAE;QAAE;KAAU,EACxDZ,MAAMa,OAAO,EACbb,MAAMc,OAAO;IAGf,OAAOP,MAAME,IAAI,CAACM,IAAI,CACpB;YAAC,CAACC,YAAYC,qBAAqB;QACjC,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOE,IAAAA,kCAAiB,EACtBlB,OACAG,SACAa,YACAhB,MAAMmB,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3Db,MAAME,IAAI,GAAG;QAEb,IAAIY,cAAcrB,MAAMY,IAAI;QAC5B,IAAIU,eAAetB,MAAMO,KAAK;QAE9B,KAAK,MAAMgB,kBAAkBP,WAAY;YACvC,oFAAoF;YACpF,IAAIO,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO1B;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAAC2B,UAAU,GAAGJ;YACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJR,aACAM;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIE,MAAM;YAClB;YAEA,IAAIC,IAAAA,wDAA2B,EAACV,aAAaO,UAAU;gBACrD,OAAOV,IAAAA,kCAAiB,EACtBlB,OACAG,SACAC,MACAJ,MAAMmB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMY,2BAA2Bf,uBAC7BgB,IAAAA,oCAAiB,EAAChB,wBAClBiB;YAEJ,IAAIjB,sBAAsB;gBACxBd,QAAQE,YAAY,GAAG2B;YACzB;YACA,MAAMG,UAAUC,IAAAA,gCAAe,EAACd,cAAcf,OAAOgB;YAErD,IAAIY,SAAS;gBACXhC,QAAQI,KAAK,GAAGA;gBAChBe,eAAef;YACjB;YAEAJ,QAAQkC,WAAW,GAAGT;YACtBzB,QAAQE,YAAY,GAAGD;YAEvBiB,cAAcO;QAChB;QACA,OAAOU,IAAAA,4BAAa,EAACtC,OAAOG;IAC9B,GACA,IAAMH;AAEV;AAEA,SAASuC,uBACPvC,KAA2B,EAC3BwC,OAA0B;IAE1B,OAAOxC;AACT;AAEO,MAAMF,qBACX2C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrBJ,yBACAxC"}