// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title TokenSwapWithAdmin
 * @dev TokenSwap with proper access control and admin functions
 */
contract TokenSwapWithAdmin is ReentrancyGuard, Ownable, Pausable {
    IERC20 public tokenA;
    IERC20 public tokenB;
    
    // Emergency withdrawal settings
    uint256 public constant EMERGENCY_WITHDRAWAL_DELAY = 24 hours;
    uint256 public emergencyWithdrawalRequestTime;
    bool public emergencyWithdrawalRequested;

    event Swap(address indexed user, uint256 amount);
    event TokensWithdrawn(address indexed token, address indexed to, uint256 amount);
    event EmergencyWithdrawalRequested(uint256 timestamp);
    event EmergencyWithdrawalExecuted(address indexed token, uint256 amount);

    constructor(address _tokenA, address _tokenB) Ownable(msg.sender) {
        require(_tokenA != address(0), "TokenA address cannot be zero");
        require(_tokenB != address(0), "TokenB address cannot be zero");
        tokenA = IERC20(_tokenA);
        tokenB = IERC20(_tokenB);
    }

    /**
     * @dev Secure swap function with pause protection
     */
    function swap(uint256 amount) external nonReentrant whenNotPaused returns (bool success) {
        require(amount > 0, "Amount must be greater than 0");
        require(tokenB.balanceOf(address(this)) >= amount, "Insufficient Token B liquidity");
        
        emit Swap(msg.sender, amount);
        
        require(tokenA.transferFrom(msg.sender, address(this), amount), "Transfer Token A failed");
        require(tokenB.transfer(msg.sender, amount), "Transfer Token B failed");
        
        return true;
    }

    /**
     * @dev Admin function to withdraw accumulated TokenA
     */
    function withdrawTokenA(uint256 amount) external onlyOwner {
        require(amount > 0, "Amount must be greater than 0");
        uint256 balance = tokenA.balanceOf(address(this));
        require(balance >= amount, "Insufficient TokenA balance");
        
        require(tokenA.transfer(owner(), amount), "TokenA withdrawal failed");
        emit TokensWithdrawn(address(tokenA), owner(), amount);
    }

    /**
     * @dev Admin function to add TokenB liquidity
     */
    function addLiquidity(uint256 amount) external onlyOwner {
        require(amount > 0, "Amount must be greater than 0");
        require(tokenB.transferFrom(msg.sender, address(this), amount), "TokenB transfer failed");
    }

    /**
     * @dev Emergency pause function
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause function
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev Request emergency withdrawal (with time delay for security)
     */
    function requestEmergencyWithdrawal() external onlyOwner {
        emergencyWithdrawalRequested = true;
        emergencyWithdrawalRequestTime = block.timestamp;
        emit EmergencyWithdrawalRequested(block.timestamp);
    }

    /**
     * @dev Execute emergency withdrawal after delay
     */
    function executeEmergencyWithdrawal(address token) external onlyOwner {
        require(emergencyWithdrawalRequested, "Emergency withdrawal not requested");
        require(
            block.timestamp >= emergencyWithdrawalRequestTime + EMERGENCY_WITHDRAWAL_DELAY,
            "Emergency withdrawal delay not met"
        );

        IERC20 tokenContract = IERC20(token);
        uint256 balance = tokenContract.balanceOf(address(this));
        require(balance > 0, "No tokens to withdraw");

        emergencyWithdrawalRequested = false;
        require(tokenContract.transfer(owner(), balance), "Emergency withdrawal failed");
        emit EmergencyWithdrawalExecuted(token, balance);
    }

    function getTokenBLiquidity() external view returns (uint256) {
        return tokenB.balanceOf(address(this));
    }

    function getTokenABalance() external view returns (uint256) {
        return tokenA.balanceOf(address(this));
    }
}