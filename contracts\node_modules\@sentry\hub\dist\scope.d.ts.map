{"version": 3, "file": "scope.d.ts", "sourceRoot": "", "sources": ["../src/scope.ts"], "names": [], "mappings": "AACA,OAAO,EACL,UAAU,EACV,cAAc,EACd,OAAO,EACP,QAAQ,EACR,KAAK,EACL,SAAS,EACT,cAAc,EACd,KAAK,EACL,MAAM,EACN,SAAS,EACT,KAAK,IAAI,cAAc,EAEvB,QAAQ,EACR,IAAI,EACJ,WAAW,EACX,IAAI,EACL,MAAM,eAAe,CAAC;AAGvB,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAEpC;;;GAGG;AACH,qBAAa,KAAM,YAAW,cAAc;IAC1C,uCAAuC;IACvC,SAAS,CAAC,mBAAmB,EAAE,OAAO,CAAS;IAE/C,oDAAoD;IACpD,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAM;IAE9D,oEAAoE;IACpE,SAAS,CAAC,gBAAgB,EAAE,cAAc,EAAE,CAAM;IAElD,4BAA4B;IAC5B,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE,CAAM;IAE1C,WAAW;IACX,SAAS,CAAC,KAAK,EAAE,IAAI,CAAM;IAE3B,WAAW;IACX,SAAS,CAAC,KAAK,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAM;IAEnD,YAAY;IACZ,SAAS,CAAC,MAAM,EAAE,MAAM,CAAM;IAE9B,eAAe;IACf,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAM;IAEnC,kBAAkB;IAClB,SAAS,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IAElC,eAAe;IACf,SAAS,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC;IAE5B,uBAAuB;IACvB,SAAS,CAAC,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAEpC,WAAW;IACX,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAEvB,cAAc;IACd,SAAS,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;IAE7B;;;OAGG;WACW,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,KAAK;IAkBzC;;;OAGG;IACI,gBAAgB,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI;IAI/D;;OAEG;IACI,iBAAiB,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI;IAKxD;;OAEG;IACI,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI;IASvC;;OAEG;IACI,OAAO,IAAI,IAAI,GAAG,SAAS;IAIlC;;OAEG;IACI,OAAO,CAAC,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAAG,IAAI;IASxD;;OAEG;IACI,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI;IAMlD;;OAEG;IACI,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAStC;;OAEG;IACI,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI;IAMhD;;OAEG;IACI,cAAc,CAAC,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI;IAMlD;;OAEG;IACI,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI;IAMtC;;OAEG;IACI,kBAAkB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;IAM9C;;;OAGG;IACI,cAAc,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;IAI1C;;OAEG;IACI,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI;IAY7D;;OAEG;IACI,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI;IAMjC;;OAEG;IACI,OAAO,IAAI,IAAI,GAAG,SAAS;IAIlC;;OAEG;IACI,cAAc,IAAI,WAAW,GAAG,SAAS;IAkBhD;;OAEG;IACI,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI;IAU1C;;OAEG;IACI,UAAU,IAAI,OAAO,GAAG,SAAS;IAIxC;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI;IA2CpD;;OAEG;IACI,KAAK,IAAI,IAAI;IAepB;;OAEG;IACI,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI;IAc3E;;OAEG;IACI,gBAAgB,IAAI,IAAI;IAM/B;;;;;;;OAOG;IACI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;IAsC9E;;OAEG;IACH,SAAS,CAAC,sBAAsB,CAC9B,UAAU,EAAE,cAAc,EAAE,EAC5B,KAAK,EAAE,KAAK,GAAG,IAAI,EACnB,IAAI,CAAC,EAAE,SAAS,EAChB,KAAK,GAAE,MAAU,GAChB,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;IAoB5B;;OAEG;IACH,SAAS,CAAC,qBAAqB,IAAI,IAAI;IAavC;;;OAGG;IACH,OAAO,CAAC,iBAAiB;CAkB1B;AAcD;;;GAGG;AACH,wBAAgB,uBAAuB,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI,CAEtE"}