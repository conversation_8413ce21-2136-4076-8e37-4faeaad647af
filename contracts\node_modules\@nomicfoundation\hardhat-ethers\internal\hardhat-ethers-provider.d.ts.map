{"version": 3, "file": "hardhat-ethers-provider.d.ts", "sourceRoot": "", "sources": ["../src/internal/hardhat-ethers-provider.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,WAAW,EACX,QAAQ,EACR,kBAAkB,EAClB,MAAM,EACN,iBAAiB,EACjB,QAAQ,EACR,aAAa,EAQd,MAAM,QAAQ,CAAC;AAIhB,OAAO,EAIL,OAAO,IAAI,aAAa,EAIxB,MAAM,EAKP,MAAM,QAAQ,CAAC;AAChB,OAAO,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAEjD,OAAO,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AA6CjD,qBAAa,qBAAsB,YAAW,MAAM,CAAC,QAAQ;IAazD,OAAO,CAAC,QAAQ,CAAC,gBAAgB;IACjC,OAAO,CAAC,QAAQ,CAAC,YAAY;IAb/B,OAAO,CAAC,uBAAuB,CAAsB;IAGrD,OAAO,CAAC,wBAAwB,CAAqB;IACrD,OAAO,CAAC,eAAe,CAAsB;IAC7C,OAAO,CAAC,yBAAyB,CAA0C;IAC3E,OAAO,CAAC,eAAe,CAA2B;IAElD,OAAO,CAAC,8BAA8B,CAA6B;IACnE,OAAO,CAAC,oBAAoB,CAA6B;gBAGtC,gBAAgB,EAAE,gBAAgB,EAClC,YAAY,EAAE,MAAM;IAGvC,IAAW,QAAQ,IAAI,IAAI,CAE1B;IAEM,OAAO;IAED,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC;IAIlD,SAAS,CACpB,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GACxB,OAAO,CAAC,mBAAmB,CAAC;IAuBlB,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC;IAMjC,UAAU,IAAI,OAAO,CAAC,aAAa,CAAC;IAKpC,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;IA4BrC,UAAU,CACrB,OAAO,EAAE,WAAW,EACpB,QAAQ,CAAC,EAAE,QAAQ,GAAG,SAAS,GAC9B,OAAO,CAAC,MAAM,CAAC;IAaL,mBAAmB,CAC9B,OAAO,EAAE,WAAW,EACpB,QAAQ,CAAC,EAAE,QAAQ,GAAG,SAAS,GAC9B,OAAO,CAAC,MAAM,CAAC;IAaL,OAAO,CAClB,OAAO,EAAE,WAAW,EACpB,QAAQ,CAAC,EAAE,QAAQ,GAAG,SAAS,GAC9B,OAAO,CAAC,MAAM,CAAC;IAWL,UAAU,CACrB,OAAO,EAAE,WAAW,EACpB,QAAQ,EAAE,MAAM,CAAC,YAAY,EAC7B,QAAQ,CAAC,EAAE,QAAQ,GAAG,SAAS,GAC9B,OAAO,CAAC,MAAM,CAAC;IAaL,WAAW,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC;IAmBpD,IAAI,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC;IAc7C,oBAAoB,CAC/B,QAAQ,EAAE,MAAM,GACf,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;IA0BzB,QAAQ,CACnB,mBAAmB,EAAE,QAAQ,EAC7B,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,GAChC,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;IAclB,cAAc,CACzB,IAAI,EAAE,MAAM,GACX,OAAO,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAgBhC,qBAAqB,CAChC,IAAI,EAAE,MAAM,GACX,OAAO,CAAC,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC;IAc/B,oBAAoB,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAI3D,OAAO,CAClB,MAAM,EAAE,MAAM,GAAG,iBAAiB,GACjC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IAUX,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAIrD,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAIvD,kBAAkB,CAC7B,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,EAC9B,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,GAC5B,OAAO,CAAC,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC;IAI/B,YAAY,CACvB,SAAS,CAAC,EAAE,QAAQ,GAAG,SAAS,GAC/B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;IAQX,EAAE,CACb,WAAW,EAAE,aAAa,EAC1B,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,IAAI,CAAC;IAwBH,IAAI,CACf,WAAW,EAAE,aAAa,EAC1B,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,IAAI,CAAC;IAwBH,IAAI,CACf,WAAW,EAAE,aAAa,EAC1B,GAAG,IAAI,EAAE,GAAG,EAAE,GACb,OAAO,CAAC,OAAO,CAAC;IAeN,aAAa,CACxB,KAAK,CAAC,EAAE,aAAa,GAAG,SAAS,GAChC,OAAO,CAAC,MAAM,CAAC;IAML,SAAS,CACpB,WAAW,CAAC,EAAE,aAAa,GAAG,SAAS,GACtC,OAAO,CAAC,QAAQ,EAAE,CAAC;IA+BT,GAAG,CACd,WAAW,EAAE,aAAa,EAC1B,QAAQ,CAAC,EAAE,QAAQ,GAAG,SAAS,GAC9B,OAAO,CAAC,IAAI,CAAC;IAqBH,kBAAkB,CAC7B,WAAW,CAAC,EAAE,aAAa,GAAG,SAAS,GACtC,OAAO,CAAC,IAAI,CAAC;IA6BH,WAAW,CACtB,KAAK,EAAE,aAAa,EACpB,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,IAAI,CAAC;IAIH,cAAc,CACzB,KAAK,EAAE,aAAa,EACpB,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,IAAI,CAAC;IAIT,MAAM;IAIb,OAAO,CAAC,WAAW;IAInB,OAAO,CAAC,YAAY;IA0CpB,OAAO,CAAC,sBAAsB;IAiD9B,OAAO,CAAC,wBAAwB;YAMlB,SAAS;IAsBvB,OAAO,CAAC,UAAU;IAIlB,OAAO,CAAC,uBAAuB;IAM/B,OAAO,CAAC,UAAU;IAsGlB,OAAO,CAAC,QAAQ;IAIhB,OAAO,CAAC,eAAe;YAQT,iBAAiB;YAgBjB,kBAAkB;IAWhC,OAAO,CAAC,8BAA8B;YA8BxB,4BAA4B;IAI1C,OAAO,CAAC,2BAA2B;IAKnC;;;;OAIG;YACW,sBAAsB;YAwCtB,kBAAkB;IAKhC,OAAO,CAAC,iBAAiB;YAKX,WAAW;IAqDzB,OAAO,CAAC,oBAAoB;IAyB5B,OAAO,CAAC,UAAU;YAkBJ,QAAQ;IAUtB,OAAO,CAAC,oBAAoB;IAmB5B,OAAO,CAAC,yBAAyB;IA2CjC,OAAO,CAAC,iBAAiB;YAoBX,oBAAoB;YA+BpB,oBAAoB;CAsBnC"}