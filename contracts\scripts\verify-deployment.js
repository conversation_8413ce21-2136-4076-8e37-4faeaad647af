const hre = require("hardhat");
const fs = require("fs");

async function main() {
  console.log("\n🔍 Verifying Nexus DeFi Deployment...");
  console.log("=====================================");

  // Load deployed addresses
  const addressesPath = "./deployedAddresses.json";
  if (!fs.existsSync(addressesPath)) {
    console.error("❌ Deployed addresses file not found!");
    return;
  }

  const addresses = JSON.parse(fs.readFileSync(addressesPath, "utf8"));
  console.log("📋 Loaded addresses from:", addressesPath);
  console.log("🌐 Network:", addresses.network);
  console.log("🔗 Chain ID:", addresses.chainId);
  console.log("👤 Deployer:", addresses.deployer);

  try {
    // Get network info
    const provider = hre.ethers.provider;
    const network = await provider.getNetwork();
    const blockNumber = await provider.getBlockNumber();
    
    console.log("\n🌐 Network Information:");
    console.log("├── Chain ID:", Number(network.chainId));
    console.log("├── Current Block:", blockNumber);
    console.log("└── Deployment Block:", addresses.deploymentBlock);

    // Verify each contract
    console.log("\n🔍 Contract Verification:");
    
    // TokenA
    console.log("\n📄 TokenA Contract:");
    const TokenA = await hre.ethers.getContractAt("TokenA", addresses.TokenA);
    const tokenAName = await TokenA.name();
    const tokenASymbol = await TokenA.symbol();
    const tokenASupply = await TokenA.totalSupply();
    const tokenAMaxSupply = await TokenA.MAX_SUPPLY();
    console.log("├── Address:", addresses.TokenA);
    console.log("├── Name:", tokenAName);
    console.log("├── Symbol:", tokenASymbol);
    console.log("├── Total Supply:", hre.ethers.formatEther(tokenASupply));
    console.log("└── Max Supply:", hre.ethers.formatEther(tokenAMaxSupply));

    // TokenB
    console.log("\n📄 TokenB Contract:");
    const TokenB = await hre.ethers.getContractAt("TokenB", addresses.TokenB);
    const tokenBName = await TokenB.name();
    const tokenBSymbol = await TokenB.symbol();
    const tokenBSupply = await TokenB.totalSupply();
    const tokenBMaxSupply = await TokenB.MAX_SUPPLY();
    const totalRewards = await TokenB.totalRewardsDistributed();
    console.log("├── Address:", addresses.TokenB);
    console.log("├── Name:", tokenBName);
    console.log("├── Symbol:", tokenBSymbol);
    console.log("├── Total Supply:", hre.ethers.formatEther(tokenBSupply));
    console.log("├── Max Supply:", hre.ethers.formatEther(tokenBMaxSupply));
    console.log("└── Total Rewards Distributed:", hre.ethers.formatEther(totalRewards));

    // Liquidity Pool
    console.log("\n💧 Liquidity Pool Contract:");
    const LiquidityPool = await hre.ethers.getContractAt("NexusLiquidityPool", addresses.LiquidityPool);
    const lpName = await LiquidityPool.name();
    const lpSymbol = await LiquidityPool.symbol();
    const lpSupply = await LiquidityPool.totalSupply();
    const feeRate = await LiquidityPool.feeRate();
    const poolStats = await LiquidityPool.getPoolStats();
    console.log("├── Address:", addresses.LiquidityPool);
    console.log("├── Name:", lpName);
    console.log("├── Symbol:", lpSymbol);
    console.log("├── LP Supply:", hre.ethers.formatEther(lpSupply));
    console.log("├── Fee Rate:", Number(feeRate) / 100, "%");
    console.log("├── Reserve A:", hre.ethers.formatEther(poolStats[0]));
    console.log("├── Reserve B:", hre.ethers.formatEther(poolStats[1]));
    console.log("└── Total Volume A:", hre.ethers.formatEther(poolStats[3]));

    // Staking Contract
    console.log("\n🏦 Staking Contract:");
    const Staking = await hre.ethers.getContractAt("NexusStaking", addresses.Staking);
    const stakingStats = await Staking.getPoolStats();
    const baseRewardRate = await Staking.baseRewardRate();
    const maxStakePerUser = await Staking.maxStakePerUser();
    console.log("├── Address:", addresses.Staking);
    console.log("├── Total Staked:", hre.ethers.formatEther(stakingStats[0]));
    console.log("├── Total Stakers:", Number(stakingStats[1]));
    console.log("├── Total Rewards Given:", hre.ethers.formatEther(stakingStats[2]));
    console.log("├── Base Reward Rate:", Number(baseRewardRate), "tokens/second");
    console.log("└── Max Stake Per User:", hre.ethers.formatEther(maxStakePerUser));

    // Batch Operations
    console.log("\n⚡ Batch Operations Contract:");
    const BatchOps = await hre.ethers.getContractAt("BatchOperations", addresses.BatchOperations);
    const batchFee = await BatchOps.batchFee();
    const totalBatchOps = await BatchOps.totalBatchOperations();
    console.log("├── Address:", addresses.BatchOperations);
    console.log("├── Batch Fee:", Number(batchFee) / 100, "%");
    console.log("└── Total Batch Operations:", Number(totalBatchOps));

    // Check authorizations
    console.log("\n🔐 Authorization Checks:");
    const isStakingAuthorized = await TokenB.isRewardMinter(addresses.Staking);
    console.log("├── Staking → TokenB Rewards:", isStakingAuthorized ? "✅ Authorized" : "❌ Not Authorized");

    // Explorer links
    console.log("\n🔗 Nexus Explorer Links:");
    console.log("├── TokenA:", `https://testnet3.explorer.nexus.xyz/address/${addresses.TokenA}`);
    console.log("├── TokenB:", `https://testnet3.explorer.nexus.xyz/address/${addresses.TokenB}`);
    console.log("├── TokenSwap:", `https://testnet3.explorer.nexus.xyz/address/${addresses.TokenSwap}`);
    console.log("├── LiquidityPool:", `https://testnet3.explorer.nexus.xyz/address/${addresses.LiquidityPool}`);
    console.log("├── Staking:", `https://testnet3.explorer.nexus.xyz/address/${addresses.Staking}`);
    console.log("└── BatchOperations:", `https://testnet3.explorer.nexus.xyz/address/${addresses.BatchOperations}`);

    console.log("\n✅ Deployment Verification Complete!");
    console.log("\n🎯 Ready for Testing:");
    console.log("1. Frontend is running at: http://localhost:3001");
    console.log("2. All contracts are deployed and verified");
    console.log("3. Authorizations are properly set");
    console.log("4. Enhanced features are active:");
    console.log("   ✅ Enhanced Tokens (Burnable, Pausable)");
    console.log("   ✅ Advanced Liquidity Pool (Fee Collection, AMM)");
    console.log("   ✅ Tier-based Staking (1.2x, 1.5x, 2.0x multipliers)");
    console.log("   ✅ Batch Operations (Gas Optimization)");
    console.log("   ✅ On-demand Reward Minting");

  } catch (error) {
    console.error("❌ Verification failed:", error.message);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
