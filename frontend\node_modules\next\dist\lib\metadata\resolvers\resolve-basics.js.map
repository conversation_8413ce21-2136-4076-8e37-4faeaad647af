{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-basics.ts"], "names": ["resolveThemeColor", "resolveAlternates", "resolveRobots", "resolveVerification", "resolveAppleWebApp", "resolveAppLinks", "resolveItunes", "resolveAlternateUrl", "url", "metadataBase", "pathname", "URL", "resolveAbsoluteUrlWithPathname", "themeColor", "resolveAsArrayOrUndefined", "themeColorDescriptors", "for<PERSON>ach", "descriptor", "push", "color", "media", "resolveUrlValuesOfObject", "obj", "result", "key", "value", "Object", "entries", "item", "index", "title", "resolveCanonicalUrl", "urlOrDescriptor", "alternates", "canonical", "languages", "types", "robotsKeys", "resolveRobotsValue", "robots", "values", "follow", "join", "basic", "googleBot", "VerificationKeys", "verification", "res", "other", "otherKey", "otherValue", "appWebApp", "capable", "startupImages", "startupImage", "map", "statusBarStyle", "appLinks", "itunes", "appId", "appArgument", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;;;;IA2BaA,iBAAiB;eAAjBA;;IAsEAC,iBAAiB;eAAjBA;;IA0EAC,aAAa;eAAbA;;IAUAC,mBAAmB;eAAnBA;;IAuBAC,kBAAkB;eAAlBA;;IAsBAC,eAAe;eAAfA;;IASAC,aAAa;eAAbA;;;uBAhO6B;4BACK;AAE/C,SAASC,oBACPC,GAAiB,EACjBC,YAAwB,EACxBC,QAAgB;IAEhB,0CAA0C;IAC1C,8DAA8D;IAC9D,IAAIF,eAAeG,KAAK;QACtBH,MAAM,IAAIG,IAAID,UAAUF;IAC1B;IACA,OAAOI,IAAAA,0CAA8B,EAACJ,KAAKC,cAAcC;AAC3D;AAEO,MAAMV,oBAAiD,CAACa;QAI7DC;IAHA,IAAI,CAACD,YAAY,OAAO;IACxB,MAAME,wBAAwD,EAAE;KAEhED,6BAAAA,IAAAA,gCAAyB,EAACD,gCAA1BC,2BAAuCE,OAAO,CAAC,CAACC;QAC9C,IAAI,OAAOA,eAAe,UACxBF,sBAAsBG,IAAI,CAAC;YAAEC,OAAOF;QAAW;aAC5C,IAAI,OAAOA,eAAe,UAC7BF,sBAAsBG,IAAI,CAAC;YACzBC,OAAOF,WAAWE,KAAK;YACvBC,OAAOH,WAAWG,KAAK;QACzB;IACJ;IAEA,OAAOL;AACT;AAEA,SAASM,yBACPC,GAMa,EACbb,YAA8C,EAC9CC,QAAgB;IAEhB,IAAI,CAACY,KAAK,OAAO;IAEjB,MAAMC,SAAoD,CAAC;IAC3D,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACL,KAAM;QAC9C,IAAI,OAAOG,UAAU,YAAYA,iBAAiBd,KAAK;YACrDY,MAAM,CAACC,IAAI,GAAG;gBACZ;oBACEhB,KAAKD,oBAAoBkB,OAAOhB,cAAcC;gBAChD;aACD;QACH,OAAO;YACLa,MAAM,CAACC,IAAI,GAAG,EAAE;YAChBC,yBAAAA,MAAOT,OAAO,CAAC,CAACY,MAAMC;gBACpB,MAAMrB,MAAMD,oBAAoBqB,KAAKpB,GAAG,EAAEC,cAAcC;gBACxDa,MAAM,CAACC,IAAI,CAACK,MAAM,GAAG;oBACnBrB;oBACAsB,OAAOF,KAAKE,KAAK;gBACnB;YACF;QACF;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,oBACPC,eAA0E,EAC1EvB,YAAwB,EACxBC,QAAgB;IAEhB,IAAI,CAACsB,iBAAiB,OAAO;IAE7B,MAAMxB,MACJ,OAAOwB,oBAAoB,YAAYA,2BAA2BrB,MAC9DqB,kBACAA,gBAAgBxB,GAAG;IAEzB,qEAAqE;IACrE,OAAO;QACLA,KAAKD,oBAAoBC,KAAKC,cAAcC;IAC9C;AACF;AAEO,MAAMT,oBAGT,CAACgC,YAAYxB,cAAc,EAAEC,QAAQ,EAAE;IACzC,IAAI,CAACuB,YAAY,OAAO;IAExB,MAAMC,YAAYH,oBAChBE,WAAWC,SAAS,EACpBzB,cACAC;IAEF,MAAMyB,YAAYd,yBAChBY,WAAWE,SAAS,EACpB1B,cACAC;IAEF,MAAMU,QAAQC,yBACZY,WAAWb,KAAK,EAChBX,cACAC;IAEF,MAAM0B,QAAQf,yBACZY,WAAWG,KAAK,EAChB3B,cACAC;IAGF,MAAMa,SAAgC;QACpCW;QACAC;QACAf;QACAgB;IACF;IAEA,OAAOb;AACT;AAEA,MAAMc,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,qBAAoE,CACxEC;IAEA,IAAI,CAACA,QAAQ,OAAO;IACpB,IAAI,OAAOA,WAAW,UAAU,OAAOA;IAEvC,MAAMC,SAAmB,EAAE;IAE3B,IAAID,OAAOV,KAAK,EAAEW,OAAOtB,IAAI,CAAC;SACzB,IAAI,OAAOqB,OAAOV,KAAK,KAAK,WAAWW,OAAOtB,IAAI,CAAC;IAExD,IAAIqB,OAAOE,MAAM,EAAED,OAAOtB,IAAI,CAAC;SAC1B,IAAI,OAAOqB,OAAOE,MAAM,KAAK,WAAWD,OAAOtB,IAAI,CAAC;IAEzD,KAAK,MAAMM,OAAOa,WAAY;QAC5B,MAAMZ,QAAQc,MAAM,CAACf,IAAI;QACzB,IAAI,OAAOC,UAAU,eAAeA,UAAU,OAAO;YACnDe,OAAOtB,IAAI,CAAC,OAAOO,UAAU,YAAYD,MAAM,CAAC,EAAEA,IAAI,CAAC,EAAEC,MAAM,CAAC;QAClE;IACF;IAEA,OAAOe,OAAOE,IAAI,CAAC;AACrB;AAEO,MAAMxC,gBAAyC,CAACqC;IACrD,IAAI,CAACA,QAAQ,OAAO;IACpB,OAAO;QACLI,OAAOL,mBAAmBC;QAC1BK,WACE,OAAOL,WAAW,WAAWD,mBAAmBC,OAAOK,SAAS,IAAI;IACxE;AACF;AAEA,MAAMC,mBAAmB;IAAC;IAAU;IAAS;IAAU;IAAM;CAAQ;AAC9D,MAAM1C,sBAAqD,CAChE2C;IAEA,IAAI,CAACA,cAAc,OAAO;IAC1B,MAAMC,MAA4B,CAAC;IAEnC,KAAK,MAAMvB,OAAOqB,iBAAkB;QAClC,MAAMpB,QAAQqB,YAAY,CAACtB,IAAI;QAC/B,IAAIC,OAAO;YACT,IAAID,QAAQ,SAAS;gBACnBuB,IAAIC,KAAK,GAAG,CAAC;gBACb,IAAK,MAAMC,YAAYH,aAAaE,KAAK,CAAE;oBACzC,MAAME,aAAapC,IAAAA,gCAAyB,EAC1CgC,aAAaE,KAAK,CAACC,SAAS;oBAE9B,IAAIC,YAAYH,IAAIC,KAAK,CAACC,SAAS,GAAGC;gBACxC;YACF,OAAOH,GAAG,CAACvB,IAAI,GAAGV,IAAAA,gCAAyB,EAACW;QAC9C;IACF;IACA,OAAOsB;AACT;AAEO,MAAM3C,qBAAmD,CAAC+C;QAS3DrC;IARJ,IAAI,CAACqC,WAAW,OAAO;IACvB,IAAIA,cAAc,MAAM;QACtB,OAAO;YACLC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBF,UAAUG,YAAY,IACxCxC,6BAAAA,IAAAA,gCAAyB,EAACqC,UAAUG,YAAY,sBAAhDxC,2BAAmDyC,GAAG,CAAC,CAAC3B,OACtD,OAAOA,SAAS,WAAW;YAAEpB,KAAKoB;QAAK,IAAIA,QAE7C;IAEJ,OAAO;QACLwB,SAAS,aAAaD,YAAY,CAAC,CAACA,UAAUC,OAAO,GAAG;QACxDtB,OAAOqB,UAAUrB,KAAK,IAAI;QAC1BwB,cAAcD;QACdG,gBAAgBL,UAAUK,cAAc,IAAI;IAC9C;AACF;AAEO,MAAMnD,kBAA6C,CAACoD;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,IAAK,MAAMjC,OAAOiC,SAAU;QAC1B,iCAAiC;QACjCA,QAAQ,CAACjC,IAAI,GAAGV,IAAAA,gCAAyB,EAAC2C,QAAQ,CAACjC,IAAI;IACzD;IACA,OAAOiC;AACT;AAEO,MAAMnD,gBAGT,CAACoD,QAAQjD,cAAc,EAAEC,QAAQ,EAAE;IACrC,IAAI,CAACgD,QAAQ,OAAO;IACpB,OAAO;QACLC,OAAOD,OAAOC,KAAK;QACnBC,aAAaF,OAAOE,WAAW,GAC3BrD,oBAAoBmD,OAAOE,WAAW,EAAEnD,cAAcC,YACtDmD;IACN;AACF"}