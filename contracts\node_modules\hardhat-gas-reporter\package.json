{"name": "hardhat-gas-reporter", "version": "1.0.10", "description": "<PERSON>hat plugin for eth-gas-reporter, a mocha reporter for Ethereum test suites", "repository": "github:cgewecke/hardhat-gas-reporter", "author": "c<PERSON><PERSON><PERSON>", "license": "MIT", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "keywords": ["ethereum", "smart-contracts", "hardhat", "hardhat-plugin", "unit tests", "gas"], "scripts": {"lint:fix": "prettier --write 'src/**/*.{js,ts}' 'test/**/*.{js,ts}' && tslint --fix --config tslint.json --project tsconfig.json", "lint": "tslint --config tslint.json --project tsconfig.json", "test": "scripts/run-tests.sh", "prepublishOnly": "tsc --project tsconfig.prod.json", "build": "tsc --project tsconfig.prod.json", "buidl": "tsc", "watch": "tsc -w"}, "files": ["dist/src/", "src/", "LICENSE", "README.md"], "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.0.0", "@nomiclabs/hardhat-truffle5": "^2.0.0", "@nomiclabs/hardhat-waffle": "^2.0.1", "@nomiclabs/hardhat-web3": "^2.0.0", "@types/chai": "^4.2.14", "@types/fs-extra": "^5.0.4", "@types/mocha": "7", "@types/node": "^8.10.38", "chai": "^4.2.0", "dotenv": "^6.2.0", "ethereum-waffle": "^3.2.1", "ethers": "^5.0.0", "ganache-cli": "^6.4.3", "hardhat": "^2.19.4", "mocha": "7", "prettier": "^1.17.0", "source-map-support": "^0.5.12", "ts-node": "^8.1.0", "tslint": "^5.16.0", "tslint-config-prettier": "^1.18.0", "tslint-plugin-prettier": "^2.0.1", "typescript": "4.7.4", "web3": "^1.3.0"}, "peerDependencies": {"hardhat": "^2.0.2"}, "dependencies": {"array-uniq": "1.0.3", "eth-gas-reporter": "^0.2.25", "sha1": "^1.1.1"}}