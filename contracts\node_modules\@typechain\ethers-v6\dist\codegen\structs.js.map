{"version": 3, "file": "structs.js", "sourceRoot": "", "sources": ["../../src/codegen/structs.ts"], "names": [], "mappings": ";;;AAAA,sDAAsD;AACtD,mCAAgC;AAGhC,sCAAuE;AACvE,mCAA+D;AAE/D,SAAgB,mBAAmB,CAAC,OAAqB;IACvD,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAuB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;IAC/E,MAAM,UAAU,GAAG,IAAA,gBAAO,EAAC,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;IAEvE,MAAM,OAAO,GAAa,EAAE,CAAA;IAE5B,IAAI,WAAW,IAAI,UAAU,EAAE;QAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QAC/E,OAAO,UAAU,CAAC,WAAW,CAAC,CAAA;KAC/B;IAED,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;QAC/C,OAAO,CAAC,IAAI,CAAC,8BAA8B,SAAS;QAChD,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;MACjE,CAAC,CAAA;KACJ;IAED,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AAC3B,CAAC;AAlBD,kDAkBC;AAED,SAAS,eAAe,CAAC,MAAsB;IAC7C,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC,UAAU,CAAA;IAExC,MAAM,SAAS,GAAG,GAAG,UAAU,GAAG,6BAAoB,EAAE,CAAA;IACxD,MAAM,UAAU,GAAG,GAAG,UAAU,GAAG,8BAAqB,EAAE,CAAA;IAC1D,MAAM,SAAS,GAAG,IAAA,yBAAiB,EAAC,EAAE,UAAU,EAAE,KAAK,EAAE,yBAAyB,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA;IACnG,MAAM,UAAU,GAAG,IAAA,0BAAkB,EAAC,EAAE,UAAU,EAAE,KAAK,EAAE,yBAAyB,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA;IAErG,OAAO;kBACS,SAAS,MAAM,SAAS;;kBAExB,UAAU,MAAM,UAAU;GACzC,CAAA;AACH,CAAC"}