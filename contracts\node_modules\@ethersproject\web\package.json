{"_ethers.alias": {"geturl.js": "browser-geturl.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/geturl": "./lib/browser-geturl.js"}, "dependencies": {"@ethersproject/base64": "^5.8.0", "@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "@ethersproject/strings": "^5.8.0"}, "description": "Utility fucntions for managing web requests for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/web", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/web", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xda6c0adac359fcc39fa54a66f42e70ccbc4b942c976d961fdcb99b61ae29cde7", "types": "./lib/index.d.ts", "version": "5.8.0"}