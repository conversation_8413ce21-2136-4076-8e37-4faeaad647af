'use client';

import { useState, useEffect } from 'react';

interface LiquidityInterfaceProps {
  account: string;
  isConnected: boolean;
  isCorrectNetwork: boolean;
  contractAddresses: any;
  nexBalance: string;
  tokenABalance: string;
  tokenBBalance: string;
  lpTokenBalance: string;
  onAddLiquidity: (amountA: string, amountB: string) => Promise<void>;
  onRemoveLiquidity: (amount: string) => Promise<void>;
  loading: boolean;
}

export default function LiquidityInterface({
  account,
  isConnected,
  isCorrectNetwork,
  contractAddresses,
  nexBalance,
  tokenABalance,
  tokenBBalance,
  lpTokenBalance,
  onAddLiquidity,
  onRemoveLiquidity,
  loading
}: LiquidityInterfaceProps) {
  const [activeTab, setActiveTab] = useState('add');
  const [amountA, setAmountA] = useState('');
  const [amountB, setAmountB] = useState('');
  const [removeAmount, setRemoveAmount] = useState('');
  const [slippage, setSlippage] = useState('0.5');
  const [showTokenASelector, setShowTokenASelector] = useState(false);
  const [showTokenBSelector, setShowTokenBSelector] = useState(false);
  const [selectedTokenA, setSelectedTokenA] = useState({
    symbol: 'NEX',
    name: 'Nexus',
    address: 'native',
    balance: nexBalance,
    decimals: 18
  });
  const [selectedTokenB, setSelectedTokenB] = useState({
    symbol: 'TKNA',
    name: 'Token A',
    address: contractAddresses.TokenA,
    balance: tokenABalance,
    decimals: 18
  });

  const tokens = [
    {
      symbol: 'NEX',
      name: 'Nexus',
      address: 'native',
      balance: nexBalance,
      decimals: 18
    },
    {
      symbol: 'TKNA',
      name: 'Token A',
      address: contractAddresses.TokenA,
      balance: tokenABalance,
      decimals: 18
    },
    {
      symbol: 'TKNB',
      name: 'Token B',
      address: contractAddresses.TokenB,
      balance: tokenBBalance,
      decimals: 18
    }
  ];

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance);
    if (num === 0) return '0.00';
    if (num < 0.01) return '<0.01';
    return num.toFixed(4);
  };

  const handleMaxA = () => setAmountA(selectedTokenA.balance);
  const handleMaxB = () => setAmountB(selectedTokenB.balance);
  const handleMaxRemove = () => setRemoveAmount(lpTokenBalance);

  const handleSelectTokenA = (token: any) => {
    if (token.address !== selectedTokenB.address) {
      setSelectedTokenA(token);
      setAmountA('');
    }
    setShowTokenASelector(false);
  };

  const handleSelectTokenB = (token: any) => {
    if (token.address !== selectedTokenA.address) {
      setSelectedTokenB(token);
      setAmountB('');
    }
    setShowTokenBSelector(false);
  };

  const getAvailableTokensForA = () => {
    return tokens.filter(token => token.address !== selectedTokenB.address);
  };

  const getAvailableTokensForB = () => {
    return tokens.filter(token => token.address !== selectedTokenA.address);
  };

  // Update token balances when props change
  useEffect(() => {
    setSelectedTokenA(prev => ({
      ...prev,
      balance: prev.symbol === 'NEX' ? nexBalance : 
               prev.symbol === 'TKNA' ? tokenABalance : tokenBBalance
    }));
    setSelectedTokenB(prev => ({
      ...prev,
      balance: prev.symbol === 'NEX' ? nexBalance : 
               prev.symbol === 'TKNA' ? tokenABalance : tokenBBalance
    }));
  }, [nexBalance, tokenABalance, tokenBBalance]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.token-selector')) {
        setShowTokenASelector(false);
        setShowTokenBSelector(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const canAddLiquidity = isConnected && isCorrectNetwork && amountA && amountB && 
    parseFloat(amountA) > 0 && parseFloat(amountB) > 0 &&
    parseFloat(amountA) <= parseFloat(selectedTokenA.balance) &&
    parseFloat(amountB) <= parseFloat(selectedTokenB.balance);

  const canRemoveLiquidity = isConnected && isCorrectNetwork && removeAmount && 
    parseFloat(removeAmount) > 0 && parseFloat(removeAmount) <= parseFloat(lpTokenBalance);

  const handleAddLiquidity = async () => {
    if (!canAddLiquidity) return;
    try {
      await onAddLiquidity(amountA, amountB);
      setAmountA('');
      setAmountB('');
    } catch (error) {
      console.error('Add liquidity failed:', error);
    }
  };

  const handleRemoveLiquidity = async () => {
    if (!canRemoveLiquidity) return;
    try {
      await onRemoveLiquidity(removeAmount);
      setRemoveAmount('');
    } catch (error) {
      console.error('Remove liquidity failed:', error);
    }
  };

  return (
    <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white">Liquidity Pool</h2>
        <div className="flex items-center space-x-2 bg-slate-700/50 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('add')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'add'
                ? 'bg-nexus-600 text-white'
                : 'text-slate-400 hover:text-white'
            }`}
          >
            Add
          </button>
          <button
            onClick={() => setActiveTab('remove')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'remove'
                ? 'bg-nexus-600 text-white'
                : 'text-slate-400 hover:text-white'
            }`}
          >
            Remove
          </button>
        </div>
      </div>

      {activeTab === 'add' ? (
        <div className="space-y-6">
          {/* Settings */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-400">Slippage Tolerance</span>
            <div className="flex items-center space-x-2">
              {['0.1', '0.5', '1.0'].map((value) => (
                <button
                  key={value}
                  onClick={() => setSlippage(value)}
                  className={`px-3 py-1 rounded text-xs ${
                    slippage === value
                      ? 'bg-nexus-600 text-white'
                      : 'bg-slate-600 text-slate-300 hover:bg-slate-500'
                  }`}
                >
                  {value}%
                </button>
              ))}
            </div>
          </div>

          {/* Token A Input */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-slate-400">Token A</span>
              <span className="text-sm text-slate-400">
                Balance: {formatBalance(selectedTokenA.balance)} {selectedTokenA.symbol}
              </span>
            </div>
            <div className="bg-slate-700/50 rounded-xl p-4 border border-slate-600/50">
              <div className="flex items-center justify-between">
                <input
                  type="number"
                  value={amountA}
                  onChange={(e) => setAmountA(e.target.value)}
                  placeholder="0.00"
                  className="bg-transparent text-white text-xl font-medium placeholder-slate-500 border-none outline-none flex-1"
                />
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleMaxA}
                    className="px-2 py-1 bg-nexus-600/20 text-nexus-400 text-xs rounded hover:bg-nexus-600/30"
                  >
                    MAX
                  </button>
                  <div className="relative token-selector">
                    <button
                      onClick={() => setShowTokenASelector(!showTokenASelector)}
                      className="flex items-center space-x-2 bg-slate-600/50 rounded-lg px-3 py-2 hover:bg-slate-600/70 transition-colors"
                    >
                      <div className="w-6 h-6 bg-gradient-to-r from-nexus-400 to-nexus-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">{selectedTokenA.symbol[0]}</span>
                      </div>
                      <span className="text-white font-medium">{selectedTokenA.symbol}</span>
                      <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                    
                    {/* Token A Dropdown */}
                    {showTokenASelector && (
                      <div className="absolute right-0 top-full mt-2 w-64 bg-slate-700 border border-slate-600 rounded-lg shadow-xl z-50">
                        <div className="p-3 border-b border-slate-600">
                          <h3 className="text-sm font-medium text-white">Select Token A</h3>
                        </div>
                        <div className="max-h-60 overflow-y-auto">
                          {getAvailableTokensForA().map((token) => (
                            <button
                              key={token.address}
                              onClick={() => handleSelectTokenA(token)}
                              className="w-full flex items-center justify-between p-3 hover:bg-slate-600/50 transition-colors"
                            >
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-gradient-to-r from-nexus-400 to-nexus-600 rounded-full flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">{token.symbol[0]}</span>
                                </div>
                                <div className="text-left">
                                  <p className="text-white font-medium">{token.symbol}</p>
                                  <p className="text-slate-400 text-xs">{token.name}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="text-white text-sm">{formatBalance(token.balance)}</p>
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Plus Icon */}
          <div className="flex justify-center">
            <div className="w-8 h-8 bg-slate-700 rounded-full flex items-center justify-center border border-slate-600">
              <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </div>
          </div>

          {/* Token B Input */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-slate-400">Token B</span>
              <span className="text-sm text-slate-400">
                Balance: {formatBalance(selectedTokenB.balance)} {selectedTokenB.symbol}
              </span>
            </div>
            <div className="bg-slate-700/50 rounded-xl p-4 border border-slate-600/50">
              <div className="flex items-center justify-between">
                <input
                  type="number"
                  value={amountB}
                  onChange={(e) => setAmountB(e.target.value)}
                  placeholder="0.00"
                  className="bg-transparent text-white text-xl font-medium placeholder-slate-500 border-none outline-none flex-1"
                />
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleMaxB}
                    className="px-2 py-1 bg-nexus-600/20 text-nexus-400 text-xs rounded hover:bg-nexus-600/30"
                  >
                    MAX
                  </button>
                  <div className="relative token-selector">
                    <button
                      onClick={() => setShowTokenBSelector(!showTokenBSelector)}
                      className="flex items-center space-x-2 bg-slate-600/50 rounded-lg px-3 py-2 hover:bg-slate-600/70 transition-colors"
                    >
                      <div className="w-6 h-6 bg-gradient-to-r from-nexus-400 to-nexus-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">{selectedTokenB.symbol[0]}</span>
                      </div>
                      <span className="text-white font-medium">{selectedTokenB.symbol}</span>
                      <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                    
                    {/* Token B Dropdown */}
                    {showTokenBSelector && (
                      <div className="absolute right-0 top-full mt-2 w-64 bg-slate-700 border border-slate-600 rounded-lg shadow-xl z-50">
                        <div className="p-3 border-b border-slate-600">
                          <h3 className="text-sm font-medium text-white">Select Token B</h3>
                        </div>
                        <div className="max-h-60 overflow-y-auto">
                          {getAvailableTokensForB().map((token) => (
                            <button
                              key={token.address}
                              onClick={() => handleSelectTokenB(token)}
                              className="w-full flex items-center justify-between p-3 hover:bg-slate-600/50 transition-colors"
                            >
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-gradient-to-r from-nexus-400 to-nexus-600 rounded-full flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">{token.symbol[0]}</span>
                                </div>
                                <div className="text-left">
                                  <p className="text-white font-medium">{token.symbol}</p>
                                  <p className="text-slate-400 text-xs">{token.name}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="text-white text-sm">{formatBalance(token.balance)}</p>
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Add Liquidity Button */}
          <button
            onClick={handleAddLiquidity}
            disabled={!canAddLiquidity || loading}
            className={`w-full py-4 rounded-xl font-medium text-lg transition-all ${
              !canAddLiquidity
                ? 'bg-slate-700/50 text-slate-500 cursor-not-allowed'
                : loading
                ? 'bg-nexus-600/50 text-white cursor-wait'
                : 'bg-nexus-600 hover:bg-nexus-700 text-white hover:shadow-lg hover:shadow-nexus-500/25'
            }`}
          >
            {loading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span>Adding Liquidity...</span>
              </div>
            ) : !isConnected ? (
              'Connect Wallet'
            ) : !isCorrectNetwork ? (
              'Switch to Nexus Network'
            ) : (
              'Add Liquidity'
            )}
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* LP Token Input */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-slate-400">LP Tokens</span>
              <span className="text-sm text-slate-400">
                Balance: {formatBalance(lpTokenBalance)} NLP
              </span>
            </div>
            <div className="bg-slate-700/50 rounded-xl p-4 border border-slate-600/50">
              <div className="flex items-center justify-between">
                <input
                  type="number"
                  value={removeAmount}
                  onChange={(e) => setRemoveAmount(e.target.value)}
                  placeholder="0.00"
                  className="bg-transparent text-white text-xl font-medium placeholder-slate-500 border-none outline-none flex-1"
                />
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleMaxRemove}
                    className="px-2 py-1 bg-red-600/20 text-red-400 text-xs rounded hover:bg-red-600/30"
                  >
                    MAX
                  </button>
                  <div className="flex items-center space-x-2 bg-slate-600/50 rounded-lg px-3 py-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-red-400 to-red-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">LP</span>
                    </div>
                    <span className="text-white font-medium">NLP</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Percentage Buttons */}
          <div className="grid grid-cols-4 gap-2">
            {['25%', '50%', '75%', '100%'].map((percentage) => (
              <button
                key={percentage}
                onClick={() => {
                  const percent = parseFloat(percentage) / 100;
                  setRemoveAmount((parseFloat(lpTokenBalance) * percent).toString());
                }}
                className="py-2 bg-slate-700 hover:bg-slate-600 text-white text-sm rounded-lg transition-colors"
              >
                {percentage}
              </button>
            ))}
          </div>

          {/* Remove Liquidity Button */}
          <button
            onClick={handleRemoveLiquidity}
            disabled={!canRemoveLiquidity || loading}
            className={`w-full py-4 rounded-xl font-medium text-lg transition-all ${
              !canRemoveLiquidity
                ? 'bg-slate-700/50 text-slate-500 cursor-not-allowed'
                : loading
                ? 'bg-red-600/50 text-white cursor-wait'
                : 'bg-red-600 hover:bg-red-700 text-white hover:shadow-lg hover:shadow-red-500/25'
            }`}
          >
            {loading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span>Removing Liquidity...</span>
              </div>
            ) : !isConnected ? (
              'Connect Wallet'
            ) : !isCorrectNetwork ? (
              'Switch to Nexus Network'
            ) : (
              'Remove Liquidity'
            )}
          </button>
        </div>
      )}
    </div>
  );
}