{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/codegen/index.ts"], "names": [], "mappings": ";;;AAAA,sDAAsD;AACtD,mCAAyC;AACzC,yCAQkB;AAElB,sCAAiE;AACjE,qCAQiB;AACjB,2CASoB;AACpB,2DAAsD;AACtD,uCAA+C;AAC/C,mCAA4C;AAE5C,SAAgB,sBAAsB,CAAC,QAAkB,EAAE,aAA4B;IACrF,MAAM,EAAE,uBAAuB,EAAE,GAAG,aAAa,CAAA;IAEjD,MAAM,MAAM,GAAG;IACb,IAAA,6BAAmB,EAAC,IAAA,eAAM,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;qBAE7C,QAAQ,CAAC,IAAI;MAC5B,IAAA,2CAA+B,EAC/B,IAAA,eAAM,EAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CACvC,kBAAkB,CAAC,CAAC,EAAE,uBAAuB,EAAE,2CAA+B,CAAC,CAChF,CACF;;MAEC,IAAA,qCAA4B,EAC5B,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CACpC,kBAAkB,CAAC,CAAC,EAAE,uBAAuB,EAAE,qCAA4B,CAAC,CAC7E,CACF;;MAEC,IAAA,eAAM,EAAC,QAAQ,CAAC,SAAS,CAAC;SACzB,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,uBAAuB,EAAE,8CAAkC,CAAC,CAAC;SAClG,IAAI,CAAC,IAAI,CAAC;;MAEX,IAAA,eAAM,EAAC,QAAQ,CAAC,SAAS,CAAC;SACzB,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,uBAAuB,EAAE,gDAAoC,CAAC,CAAC;SACpG,IAAI,CAAC,IAAI,CAAC;;;IAGb,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,iCAAwB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;qBAE/C,QAAQ,CAAC,IAAI;MAC5B,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,kBAAkB,QAAQ,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;+CACnC,QAAQ,CAAC,IAAI;;;iBAG3C,QAAQ,CAAC,IAAI;;MAExB,+BAAsB;;MAEtB,IAAA,eAAM,EAAC,QAAQ,CAAC,SAAS,CAAC;SACzB,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,oCAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SAC/C,GAAG,CAAC,4BAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;SACnD,IAAI,CAAC,IAAI,CAAC;;;;;MAKX,IAAA,eAAM,EAAC,QAAQ,CAAC,SAAS,CAAC;SACzB,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,uBAAuB,EAAE,0CAA8B,CAAC,CAAC;SAC9F,IAAI,CAAC,IAAI,CAAC;;MAEX,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC;SACtB,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,uBAAuB,EAAE,oCAA2B,CAAC,CAAC;SAC3F,IAAI,CAAC,IAAI,CAAC;;;QAGT,IAAA,eAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,6BAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;IAEhE,CAAA;IAEF,MAAM,YAAY,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA;IAC7D,MAAM,UAAU,GACd,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC;QACtG,YAAY,CAAA;IAEd,MAAM,OAAO,GACX,IAAA,2CAA+B,EAC7B;QACE,aAAa,EAAE;YACb,cAAc;YACd,cAAc;YACd,WAAW;YACX,qBAAqB;YACrB,kBAAkB;YAClB,QAAQ;YACR,WAAW;YACX,eAAe;YACf,aAAa;YACb,gBAAgB;YAChB,oBAAoB;YACpB,eAAe;YACf,gBAAgB;YAChB,UAAU;YACV,UAAU;SACX;KACF,EACD,MAAM,CACP;QACD,IAAI;QACJ,IAAA,2CAA+B,EAAC,EAAE,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,CAAC,GAAG,sBAAa,EAAE,GAAG,4BAAgB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;IAE9G,OAAO,OAAO,GAAG,MAAM,CAAA;AACzB,CAAC;AA5FD,wDA4FC;AAED,SAAgB,sBAAsB,CACpC,aAA4B,EAC5B,QAAkB,EAClB,GAAQ,EACR,QAAqC;;IAErC,MAAM,YAAY,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA;IAC7D,MAAM,eAAe,GACnB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,0BAAkB,EAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzG,eACE,CAAA,MAAA,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,0CAAE,eAAe,MAAK,SAAS;YACpD,CAAC,CAAC,sCAAsC;YACxC,CAAC,CAAC,yCACN,EAAE,CAAA;IACJ,MAAM,mCAAmC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,IAAA,8BAAkB,EAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACpD,CAAC,CAAC,EAAE,CAAA;IACN,MAAM,mBAAmB,GAAG,mCAAmC;QAC7D,CAAC,CAAC,GAAG,mCAAmC,mBAAmB;QAC3D,CAAC,CAAC,iBAAiB,CAAA;IACrB,IAAI,CAAC,QAAQ;QAAE,OAAO,8BAA8B,CAAC,QAAQ,EAAE,GAAG,EAAE,YAAY,CAAC,CAAA;IAEjF,8DAA8D;IAE9D,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,4BAA4B,CAAC,QAAQ,EAAE,GAAG,EAAE,YAAY,CAAC,CAAA;IAElF,MAAM,MAAM,GAAG;IACb,MAAM;;uBAEa,QAAQ,CAAC,QAAQ;;IAEpC,qCAAqC,CAAC,QAAQ,EAAE,QAAQ,CAAC;;iBAE5C,QAAQ,CAAC,IAAI,GAAG,wBAAe;MAC1C,0BAA0B,CAAC,aAAa,EAAE,QAAQ,EAAE,QAAQ,CAAC;oCAC/B,eAAe;0CACT,mBAAmB;;sBAEvC,eAAe;4BACT,mBAAmB,gBAAgB,QAAQ,CAAC,IAAI;;;;uDAIrB,QAAQ,CAAC,IAAI,GAAG,wBAAe;wCAC9C,QAAQ,CAAC,IAAI,GAAG,wBAAe;;MAEjE,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,kCAAkC,QAAQ,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;MAC5F,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,kCAAkC,QAAQ,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;;MAE5F,IAAI;;;IAGN,iCAAiC,CAAC,QAAQ,EAAE,QAAQ,CAAC;GACtD,CAAA;IAED,MAAM,UAAU,GAAG,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,YAAY,EAAE,CAAA;IAEtG,MAAM,OAAO,GACX,IAAA,2CAA+B,EAC7B;QACE,MAAM,EAAE,CAAC,UAAU,EAAE,iBAAiB,EAAE,6BAA6B,EAAE,WAAW,CAAC;QACnF,aAAa,EAAE;YACb,QAAQ;YACR,WAAW;YACX,cAAc;YACd,WAAW;YACX,aAAa;YACb,2BAA2B;YAC3B,UAAU;YACV,oBAAoB;YACpB,gBAAgB;SACjB;KACF,EACD,MAAM,CACP;QACD,IAAI;QACJ,IAAA,2CAA+B,EAAC,EAAE,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,CAAC,GAAG,sBAAa,EAAE,GAAG,4BAAgB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;IAE9G,OAAO,OAAO,GAAG,MAAM,CAAA;AACzB,CAAC;AA/ED,wDA+EC;AAED,SAAgB,8BAA8B,CAAC,QAAkB,EAAE,GAAQ,EAAE,YAAoB;IAC/F,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,4BAA4B,CAAC,QAAQ,EAAE,GAAG,EAAE,YAAY,CAAC,CAAA;IAClF,OAAO;;IAEL,MAAM;;iBAEO,QAAQ,CAAC,IAAI,GAAG,wBAAe;MAC1C,IAAI;;GAEP,CAAA;AACH,CAAC;AAVD,wEAUC;AAED,SAAS,4BAA4B,CACnC,QAAkB,EAClB,GAAQ,EACR,YAAoB;;IAEpB,MAAM,OAAO,GAAgB,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC,CAAA;IAElF,MAAA,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,0CAAE,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;QACnD,MAAM,EAAE,UAAU,EAAE,GAAG,IAAkB,CAAA;QACzC,IAAI,UAAU,EAAE;YACd,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,UAAU,GAAG,6BAAoB,CAAC,CAAA;SAClF;IACH,CAAC,CAAC,CAAA;IAEF,MAAM,uBAAuB,GAAG;QAC9B,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAC7C,GAAG,QAAQ,CAAC,IAAI;QAChB,QAAQ,CAAC,IAAI,GAAG,YAAY;KAC7B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAEX,MAAM,MAAM,GAAG;kBACC,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,uBAAuB;;iBAEpE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;GAC1C,CAAC,IAAI,EAAE,CAAA;IAER,MAAM,IAAI,GAAG;;gCAEiB,QAAQ,CAAC,IAAI;sCACP,QAAQ,CAAC,IAAI;;uEAEoB,QAAQ,CAAC,IAAI;iEACnB,QAAQ,CAAC,IAAI;;GAE3E,CAAC,IAAI,EAAE,CAAA;IAER,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAA;AACzB,CAAC;AAED,SAAS,0BAA0B,CACjC,aAA4B,EAC5B,QAAkB,EAClB,QAAoC;IAEpC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;QAC5B,OAAO;6BACkB,QAAQ,CAAC,IAAI;;;;;;UAMhC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,wBAAwB,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE;;KAErF,CAAA;KACF;IAED,MAAM,mBAAmB,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;QAClE,6FAA6F;QAC7F,8FAA8F;QAC9F,MAAM,mBAAmB,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAA;QACtF,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,SAAS,CAAA;QACpD,OAAO;;sBAEW,mBAAmB;gCACT,UAAU;SACjC,CAAA;IACP,CAAC,CAAC,CAAA;IAEF,MAAM,SAAS,GAAG,GAAG,QAAQ,CAAC,IAAI,GAAG,wBAAe,EAAE,CAAA;IACtD,MAAM,gBAAgB,GAAG,GAAG,QAAQ,CAAC,IAAI,kBAAkB,CAAA;IAE3D,OAAO;;iBAEQ,QAAQ,CAAC,IAAI;;;;;;;;YAQlB,SAAS;;;;QAIb,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,wBAAwB,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE;;;gDAGxC,gBAAgB;;QAExD,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;GAInC,CAAA;AACH,CAAC;AAED,SAAS,qCAAqC,CAAC,QAAkB,EAAE,QAAoC;IACrG,MAAM,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,mBAAmB,CAAA;IAEhD,IAAI,QAAQ,CAAC,cAAc,EAAE;QAC3B,OAAO;aACE,IAAI;mCACkB,QAAQ,CAAC,IAAI;;;;cAIlC,IAAI;;;;;QAKV,CAAA;KACL;SAAM;QACL,OAAO;aACE,IAAI;;iCAEgB,IAAI;;KAEhC,CAAA;KACF;AACH,CAAC;AAED,SAAS,iCAAiC,CAAC,QAAkB,EAAE,QAAoC;IACjG,IAAI,CAAC,QAAQ,CAAC,cAAc;QAAE,OAAO,EAAE,CAAA;IAEvC,MAAM,iBAAiB,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CACnD,CAAC,OAAO,EAAE,EAAE,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,SAAS,aAAa,CACrE,CAAA;IACD,OAAO;qBACY,QAAQ,CAAC,IAAI;MAC5B,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;KAC7B,CAAA;AACL,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,kBAAkB,CACzB,GAAQ,EACR,sBAA+B,EAC/B,SAAmD;IAEnD,yDAAyD;IACzD,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;QAClB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAA;KAC5C;IAED,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,sBAAsB,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,iBAAQ,CAAC,CAAA;AACvG,CAAC"}