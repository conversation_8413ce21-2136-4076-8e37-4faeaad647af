{"_format": "hh-sol-cache-2", "files": {"D:\\nexus\\contracts\\contracts\\TokenA.sol": {"lastModificationDate": 1753344619450, "contentHash": "c83a9100cbaee1e597e54dd979320ebf", "sourceName": "contracts/TokenA.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["TokenA"]}, "D:\\nexus\\contracts\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol": {"lastModificationDate": 1753345175069, "contentHash": "59dfce11284f2636db261df9b6a18f81", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "D:\\nexus\\contracts\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol": {"lastModificationDate": 1753345175236, "contentHash": "9261adf6457863de3e9892f51317ec89", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.16"], "artifacts": ["IERC20"]}, "D:\\nexus\\contracts\\node_modules\\@openzeppelin\\contracts\\utils\\Context.sol": {"lastModificationDate": 1753345174948, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "D:\\nexus\\contracts\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol": {"lastModificationDate": 1753345175238, "contentHash": "513778b30d2750f5d2b9b19bbcf748a5", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC20Metadata"]}, "D:\\nexus\\contracts\\node_modules\\@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol": {"lastModificationDate": 1753345175018, "contentHash": "5041977bbe908de2e6ed0270447f79ad", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.8.4"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "D:\\nexus\\contracts\\contracts\\TokenSwap.sol": {"lastModificationDate": 1753344640077, "contentHash": "4a936d1216d741d76b3e524d85a13d14", "sourceName": "contracts/TokenSwap.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["TokenSwap"]}, "D:\\nexus\\contracts\\contracts\\TokenB.sol": {"lastModificationDate": 1753344626902, "contentHash": "2030d479611474484070bd538f2897f2", "sourceName": "contracts/TokenB.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["TokenB"]}, "D:\\nexus\\contracts\\contracts\\SimpleLiquidityPool.sol": {"lastModificationDate": 1753348498190, "contentHash": "d87a805eaf4f49ff43d30b0a68337a28", "sourceName": "contracts/SimpleLiquidityPool.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SimpleLiquidityPool"]}, "D:\\nexus\\contracts\\node_modules\\@openzeppelin\\contracts\\utils\\ReentrancyGuard.sol": {"lastModificationDate": 1753345175446, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "D:\\nexus\\contracts\\contracts\\SimpleStaking.sol": {"lastModificationDate": 1753348571637, "contentHash": "477532e3d3c5c43dedf5900b709efa3f", "sourceName": "contracts/SimpleStaking.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SimpleStaking"]}, "D:\\nexus\\contracts\\node_modules\\@openzeppelin\\contracts\\access\\Ownable.sol": {"lastModificationDate": 1753345175286, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}}}