{"name": "@typechain/hardhat", "description": "Zero-config TypeChain support for Hardhat", "keywords": ["TypeScript", "hardhat", "plugin", "typechain", "ethereum", "bindings", "smartcontract", "blockchain"], "version": "9.1.0", "license": "MIT", "repository": "https://github.com/ethereum-ts/Typechain", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/**/*"], "dependencies": {"fs-extra": "^9.1.0"}, "devDependencies": {"@nomicfoundation/hardhat-ethers": "^3.0.0", "@typechain/ethers-v6": "^0.5.1", "@types/fs-extra": "^9.0.7", "@types/rimraf": "^3.0.0", "ethers": "^6.1.0", "hardhat": "^2.9.9", "rimraf": "^3.0.2", "typechain": "^8.3.2", "typescript": "^4"}, "peerDependencies": {"@typechain/ethers-v6": "^0.5.1", "ethers": "^6.1.0", "hardhat": "^2.9.9", "typechain": "^8.3.2"}, "scripts": {"format": "prettier --config ../../.prettierrc --ignore-path ../../.prettierignore --check \"./**/*.ts\" README.md", "format:fix": "prettier --config ../../.prettierrc --ignore-path ../../.prettierignore --write \"./**/*.ts\"  README.md", "lint": "eslint --ext .ts src test", "lint:fix": "pnpm lint --fix", "typecheck": "tsc --noEmit --incremental false --composite false", "clean": "rm -rf dist && rm -f tsconfig.build.tsbuildinfo", "test": "mocha --config ../../.mocharc.js", "test:fix": "pnpm lint:fix && pnpm format:fix && pnpm test && pnpm typecheck"}}