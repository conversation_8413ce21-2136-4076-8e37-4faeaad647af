{"name": "@scure/bip32", "version": "1.1.5", "description": "Secure, audited & minimal implementation of BIP32 hierarchical deterministic (HD) wallets", "files": ["index.ts", "lib/index.js", "lib/index.d.ts", "lib/index.js.map", "lib/esm/package.json", "lib/esm/index.js", "lib/esm/index.js.map"], "main": "lib/index.js", "module": "lib/esm/index.js", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/esm/index.js", "default": "./lib/index.js"}}, "dependencies": {"@noble/hashes": "~1.2.0", "@noble/secp256k1": "~1.7.0", "@scure/base": "~1.1.0"}, "devDependencies": {"micro-should": "0.4.0", "prettier": "2.6.2", "typescript": "4.7.3"}, "author": "<PERSON> (https://paulmillr.com)", "homepage": "https://paulmillr.com/", "repository": {"type": "git", "url": "https://github.com/paulmillr/scure-bip32.git"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "scripts": {"build": "tsc -d && tsc -p tsconfig.esm.json", "lint": "prettier --check 'index.ts' 'test/*.test.ts'", "format": "prettier --write 'index.ts' 'test/*.test.ts'", "test": "cd test && tsc && node hdkey.test.js"}, "keywords": ["bip32", "hierarchical", "deterministic", "hd key", "bip0032", "bip-32", "bip39", "micro", "scure", "mnemonic", "phrase", "code"], "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}]}