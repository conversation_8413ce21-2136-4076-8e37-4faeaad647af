{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/resolve-rewrites.ts"], "names": ["getPathMatch", "matchHas", "prepareDestination", "removeTrailingSlash", "normalizeLocalePath", "removeBasePath", "parseRelativeUrl", "resolveRewrites", "<PERSON><PERSON><PERSON>", "pages", "rewrites", "query", "resolveHref", "locales", "matchedPage", "externalDest", "parsedAs", "fsPathname", "pathname", "resolvedHref", "handleRewrite", "rewrite", "matcher", "source", "process", "env", "__NEXT_TRAILING_SLASH", "removeUnnamedP<PERSON>ms", "strict", "params", "has", "missing", "hasParams", "headers", "host", "document", "location", "hostname", "navigator", "userAgent", "cookies", "cookie", "split", "reduce", "acc", "item", "key", "value", "join", "Object", "assign", "destination", "destRes", "appendParamsToQuery", "parsedDestination", "newUrl", "includes", "finished", "i", "beforeFiles", "length", "afterFiles", "fallback"], "mappings": "AAEA,SAASA,YAAY,QAAQ,eAAc;AAC3C,SAASC,QAAQ,EAAEC,kBAAkB,QAAQ,wBAAuB;AACpE,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,mBAAmB,QAAQ,mCAAkC;AACtE,SAASC,cAAc,QAAQ,sCAAqC;AACpE,SAASC,gBAAgB,QAAQ,uBAAsB;AAEvD,eAAe,SAASC,gBACtBC,MAAc,EACdC,KAAe,EACfC,QAIC,EACDC,KAAqB,EACrBC,WAAqC,EACrCC,OAAkB;IAQlB,IAAIC,cAAc;IAClB,IAAIC,eAAe;IACnB,IAAIC,WAAWV,iBAAiBE;IAChC,IAAIS,aAAad,oBACfC,oBAAoBC,eAAeW,SAASE,QAAQ,GAAGL,SAASK,QAAQ;IAE1E,IAAIC;IAEJ,MAAMC,gBAAgB,CAACC;QACrB,MAAMC,UAAUtB,aACdqB,QAAQE,MAAM,GAAIC,CAAAA,QAAQC,GAAG,CAACC,qBAAqB,GAAG,SAAS,EAAC,GAChE;YACEC,qBAAqB;YACrBC,QAAQ;QACV;QAGF,IAAIC,SAASP,QAAQN,SAASE,QAAQ;QAEtC,IAAI,AAACG,CAAAA,QAAQS,GAAG,IAAIT,QAAQU,OAAO,AAAD,KAAMF,QAAQ;YAC9C,MAAMG,YAAY/B,SAChB;gBACEgC,SAAS;oBACPC,MAAMC,SAASC,QAAQ,CAACC,QAAQ;oBAChC,cAAcC,UAAUC,SAAS;gBACnC;gBACAC,SAASL,SAASM,MAAM,CACrBC,KAAK,CAAC,MACNC,MAAM,CAAyB,CAACC,KAAKC;oBACpC,MAAM,CAACC,KAAK,GAAGC,MAAM,GAAGF,KAAKH,KAAK,CAAC;oBACnCE,GAAG,CAACE,IAAI,GAAGC,MAAMC,IAAI,CAAC;oBACtB,OAAOJ;gBACT,GAAG,CAAC;YACR,GACA5B,SAASL,KAAK,EACdU,QAAQS,GAAG,EACXT,QAAQU,OAAO;YAGjB,IAAIC,WAAW;gBACbiB,OAAOC,MAAM,CAACrB,QAAQG;YACxB,OAAO;gBACLH,SAAS;YACX;QACF;QAEA,IAAIA,QAAQ;YACV,IAAI,CAACR,QAAQ8B,WAAW,EAAE;gBACxB,8DAA8D;gBAC9DpC,eAAe;gBACf,OAAO;YACT;YACA,MAAMqC,UAAUlD,mBAAmB;gBACjCmD,qBAAqB;gBACrBF,aAAa9B,QAAQ8B,WAAW;gBAChCtB,QAAQA;gBACRlB,OAAOA;YACT;YACAK,WAAWoC,QAAQE,iBAAiB;YACpC9C,SAAS4C,QAAQG,MAAM;YACvBN,OAAOC,MAAM,CAACvC,OAAOyC,QAAQE,iBAAiB,CAAC3C,KAAK;YAEpDM,aAAad,oBACXC,oBAAoBC,eAAeG,SAASK,SAASK,QAAQ;YAG/D,IAAIT,MAAM+C,QAAQ,CAACvC,aAAa;gBAC9B,yDAAyD;gBACzD,yBAAyB;gBACzBH,cAAc;gBACdK,eAAeF;gBACf,OAAO;YACT;YAEA,uEAAuE;YACvEE,eAAeP,YAAYK;YAE3B,IAAIE,iBAAiBX,UAAUC,MAAM+C,QAAQ,CAACrC,eAAe;gBAC3DL,cAAc;gBACd,OAAO;YACT;QACF;IACF;IACA,IAAI2C,WAAW;IAEf,IAAK,IAAIC,IAAI,GAAGA,IAAIhD,SAASiD,WAAW,CAACC,MAAM,EAAEF,IAAK;QACpD,mDAAmD;QACnD,8CAA8C;QAC9CtC,cAAcV,SAASiD,WAAW,CAACD,EAAE;IACvC;IACA5C,cAAcL,MAAM+C,QAAQ,CAACvC;IAE7B,IAAI,CAACH,aAAa;QAChB,IAAI,CAAC2C,UAAU;YACb,IAAK,IAAIC,IAAI,GAAGA,IAAIhD,SAASmD,UAAU,CAACD,MAAM,EAAEF,IAAK;gBACnD,IAAItC,cAAcV,SAASmD,UAAU,CAACH,EAAE,GAAG;oBACzCD,WAAW;oBACX;gBACF;YACF;QACF;QAEA,0DAA0D;QAC1D,IAAI,CAACA,UAAU;YACbtC,eAAeP,YAAYK;YAC3BH,cAAcL,MAAM+C,QAAQ,CAACrC;YAC7BsC,WAAW3C;QACb;QAEA,IAAI,CAAC2C,UAAU;YACb,IAAK,IAAIC,IAAI,GAAGA,IAAIhD,SAASoD,QAAQ,CAACF,MAAM,EAAEF,IAAK;gBACjD,IAAItC,cAAcV,SAASoD,QAAQ,CAACJ,EAAE,GAAG;oBACvCD,WAAW;oBACX;gBACF;YACF;QACF;IACF;IAEA,OAAO;QACLjD;QACAQ;QACAF;QACAK;QACAJ;IACF;AACF"}