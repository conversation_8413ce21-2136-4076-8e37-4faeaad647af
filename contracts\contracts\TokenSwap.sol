// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title TokenSwap
 * @dev Contract that enables users to swap Token A for Token B at a fixed 1:1 ratio
 * The contract must be preloaded with Token B liquidity before users can perform swaps
 */
contract TokenSwap {
    IERC20 public tokenA;
    IERC20 public tokenB;

    event Swap(address indexed user, uint256 amount);

    /**
     * @dev Constructor that sets the addresses of Token A and Token B
     * @param _tokenA Address of the Token A contract
     * @param _tokenB Address of the Token B contract
     */
    constructor(address _tokenA, address _tokenB) {
        require(_tokenA != address(0), "TokenA address cannot be zero");
        require(_tokenB != address(0), "TokenB address cannot be zero");
        tokenA = IERC20(_tokenA);
        tokenB = IERC20(_tokenB);
    }

    /**
     * @dev Swaps Token A for Token B at a 1:1 ratio
     * @param amount The amount of Token A to swap for Token B
     * @return success True if the swap was successful
     * 
     * Requirements:
     * - User must have approved this contract to spend their Token A
     * - Contract must have sufficient Token B balance
     * - Amount must be greater than 0
     */
    function swap(uint256 amount) external returns (bool success) {
        require(amount > 0, "Amount must be greater than 0");
        require(tokenB.balanceOf(address(this)) >= amount, "Insufficient Token B liquidity");
        
        // Transfer Token A from user to this contract
        require(tokenA.transferFrom(msg.sender, address(this), amount), "Transfer Token A failed");
        
        // Transfer Token B from this contract to user
        require(tokenB.transfer(msg.sender, amount), "Transfer Token B failed");
        
        emit Swap(msg.sender, amount);
        return true;
    }

    /**
     * @dev Returns the Token B balance of this contract (available liquidity)
     * @return The amount of Token B available for swapping
     */
    function getTokenBLiquidity() external view returns (uint256) {
        return tokenB.balanceOf(address(this));
    }

    /**
     * @dev Returns the Token A balance of this contract (accumulated from swaps)
     * @return The amount of Token A held by this contract
     */
    function getTokenABalance() external view returns (uint256) {
        return tokenA.balanceOf(address(this));
    }
}
