@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-slate-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-slate-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-slate-500;
}

/* Custom focus styles */
input:focus,
button:focus {
  outline: none;
}

/* Animation for loading states */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Custom button hover effects */
.btn-primary {
  @apply bg-nexus-600 hover:bg-nexus-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  @apply bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
}

/* Input styles */
.input-primary {
  @apply bg-slate-700/50 border border-slate-600/50 text-white placeholder-slate-400 rounded-lg px-3 py-2 focus:ring-2 focus:ring-nexus-500 focus:border-transparent transition-all;
}

/* Card styles */
.card {
  @apply bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6;
}

/* Number input specific styles */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* Status indicator animations */
.status-online {
  @apply w-2 h-2 bg-green-400 rounded-full;
  animation: pulse 2s infinite;
}

.status-offline {
  @apply w-2 h-2 bg-red-400 rounded-full;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Loading spinner */
.spinner {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 2px solid #ffffff;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced responsive design utilities */
@media (max-width: 640px) {
  .mobile-padding {
    @apply px-4;
  }
  
  .mobile-text {
    @apply text-sm;
  }
  
  .mobile-grid {
    @apply grid-cols-1;
  }
  
  .mobile-hidden {
    @apply hidden;
  }
  
  .mobile-full {
    @apply w-full;
  }
  
  .mobile-spacing {
    @apply space-y-3;
  }
}

@media (max-width: 768px) {
  .tablet-grid {
    @apply grid-cols-2;
  }
  
  .tablet-hidden {
    @apply hidden;
  }
  
  .tablet-spacing {
    @apply space-y-4;
  }
}

/* Touch-friendly button sizes for mobile */
@media (max-width: 640px) {
  .btn-primary,
  .btn-secondary {
    @apply py-3 px-6 text-base;
    min-height: 44px; /* iOS recommended touch target */
  }
  
  input, select, textarea {
    @apply py-3 px-4 text-base;
    min-height: 44px;
  }
}

/* Safe area support for iOS */
@supports (padding: max(0px)) {
  .safe-area-inset {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
    padding-top: max(1rem, env(safe-area-inset-top));
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* Dark theme specific adjustments */
.dark-theme {
  color-scheme: dark;
}

/* Custom transitions */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effects for interactive elements */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}

/* Text selection */
::selection {
  background: rgba(59, 130, 246, 0.3);
  color: white;
}

/* Custom border gradients */
.border-gradient {
  border: 1px solid;
  border-image: linear-gradient(135deg, #3b82f6, #06b6d4) 1;
}

/* Notification styles */
.notification-enter {
  opacity: 0;
  transform: translateY(-20px);
}

.notification-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.notification-exit {
  opacity: 1;
}

.notification-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}