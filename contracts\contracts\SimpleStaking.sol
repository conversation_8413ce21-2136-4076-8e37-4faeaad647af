// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title SimpleStaking
 * @dev Simple staking contract for LP tokens with rewards
 * Focus: Stake LP → Earn Rewards → Claim Rewards
 */
contract SimpleStaking is ReentrancyGuard, Ownable {
    IERC20 public stakingToken; // LP Token
    IERC20 public rewardToken;  // Reward Token (TokenB)
    
    uint256 public rewardRate = 100; // 100 tokens per second
    uint256 public lastUpdateTime;
    uint256 public rewardPerTokenStored;
    uint256 public totalStaked;
    
    mapping(address => uint256) public userRewardPerTokenPaid;
    mapping(address => uint256) public rewards;
    mapping(address => uint256) public balances;
    mapping(address => uint256) public stakingTime;
    
    event Staked(address indexed user, uint256 amount);
    event Withdrawn(address indexed user, uint256 amount);
    event RewardPaid(address indexed user, uint256 reward);
    event RewardRateUpdated(uint256 newRate);
    
    constructor(
        address _stakingToken,
        address _rewardToken
    ) Ownable(msg.sender) {
        stakingToken = IERC20(_stakingToken);
        rewardToken = IERC20(_rewardToken);
        lastUpdateTime = block.timestamp;
    }
    
    modifier updateReward(address account) {
        rewardPerTokenStored = rewardPerToken();
        lastUpdateTime = block.timestamp;
        if (account != address(0)) {
            rewards[account] = earned(account);
            userRewardPerTokenPaid[account] = rewardPerTokenStored;
        }
        _;
    }
    
    /**
     * @dev Stake LP tokens
     * Transaction 1: Approve LP tokens
     * Transaction 2: Stake
     */
    function stake(uint256 amount) external nonReentrant updateReward(msg.sender) {
        require(amount > 0, "Cannot stake 0");
        
        totalStaked += amount;
        balances[msg.sender] += amount;
        stakingTime[msg.sender] = block.timestamp;
        
        require(stakingToken.transferFrom(msg.sender, address(this), amount), "Transfer failed");
        
        emit Staked(msg.sender, amount);
    }
    
    /**
     * @dev Withdraw staked LP tokens
     * Transaction: Withdraw
     */
    function withdraw(uint256 amount) public nonReentrant updateReward(msg.sender) {
        require(amount > 0, "Cannot withdraw 0");
        require(balances[msg.sender] >= amount, "Insufficient balance");
        
        totalStaked -= amount;
        balances[msg.sender] -= amount;
        
        require(stakingToken.transfer(msg.sender, amount), "Transfer failed");
        
        emit Withdrawn(msg.sender, amount);
    }
    
    /**
     * @dev Claim rewards
     * Transaction: Claim
     */
    function claimReward() public nonReentrant updateReward(msg.sender) {
        uint256 reward = rewards[msg.sender];
        if (reward > 0) {
            rewards[msg.sender] = 0;
            require(rewardToken.transfer(msg.sender, reward), "Reward transfer failed");
            emit RewardPaid(msg.sender, reward);
        }
    }
    
    /**
     * @dev Withdraw all and claim rewards
     * Transaction: Exit (withdraw + claim in one)
     */
    function exit() external {
        withdraw(balances[msg.sender]);
        claimReward();
    }
    
    /**
     * @dev Compound rewards (stake rewards as LP tokens if possible)
     * Transaction: Compound
     */
    function compound() external nonReentrant updateReward(msg.sender) {
        uint256 reward = rewards[msg.sender];
        require(reward > 0, "No rewards to compound");
        
        rewards[msg.sender] = 0;
        
        // For simplicity, just add to balance (in real scenario, would convert to LP)
        totalStaked += reward;
        balances[msg.sender] += reward;
        
        emit RewardPaid(msg.sender, reward);
        emit Staked(msg.sender, reward);
    }
    
    /**
     * @dev Multiple small stakes for more transactions
     */
    function multiStake(uint256[] calldata amounts) external nonReentrant updateReward(msg.sender) {
        uint256 totalAmount = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            require(amounts[i] > 0, "Cannot stake 0");
            totalAmount += amounts[i];
        }

        totalStaked += totalAmount;
        balances[msg.sender] += totalAmount;
        stakingTime[msg.sender] = block.timestamp;

        require(stakingToken.transferFrom(msg.sender, address(this), totalAmount), "Transfer failed");

        for (uint256 i = 0; i < amounts.length; i++) {
            emit Staked(msg.sender, amounts[i]);
        }
    }

    /**
     * @dev Multiple small withdrawals for more transactions
     */
    function multiWithdraw(uint256[] calldata amounts) external nonReentrant updateReward(msg.sender) {
        uint256 totalAmount = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            require(amounts[i] > 0, "Cannot withdraw 0");
            totalAmount += amounts[i];
        }

        require(balances[msg.sender] >= totalAmount, "Insufficient balance");

        totalStaked -= totalAmount;
        balances[msg.sender] -= totalAmount;

        require(stakingToken.transfer(msg.sender, totalAmount), "Transfer failed");

        for (uint256 i = 0; i < amounts.length; i++) {
            emit Withdrawn(msg.sender, amounts[i]);
        }
    }

    /**
     * @dev Multiple claims for more transactions (just emit multiple events)
     */
    function multiClaim(uint256 times) external nonReentrant updateReward(msg.sender) {
        uint256 reward = rewards[msg.sender];
        require(reward > 0, "No rewards to claim");

        rewards[msg.sender] = 0;
        require(rewardToken.transfer(msg.sender, reward), "Reward transfer failed");

        // Emit multiple events for transaction count
        for (uint256 i = 0; i < times; i++) {
            emit RewardPaid(msg.sender, reward / times);
        }
    }
    
    // View functions
    function rewardPerToken() public view returns (uint256) {
        if (totalStaked == 0) {
            return rewardPerTokenStored;
        }
        return rewardPerTokenStored + 
            (((block.timestamp - lastUpdateTime) * rewardRate * 1e18) / totalStaked);
    }
    
    function earned(address account) public view returns (uint256) {
        return ((balances[account] * (rewardPerToken() - userRewardPerTokenPaid[account])) / 1e18) + 
            rewards[account];
    }
    
    function getStakingInfo(address user) external view returns (
        uint256 stakedBalance,
        uint256 earnedRewards,
        uint256 stakingTimestamp,
        uint256 totalPoolStaked
    ) {
        return (
            balances[user],
            earned(user),
            stakingTime[user],
            totalStaked
        );
    }
    
    // Admin functions
    function setRewardRate(uint256 _rewardRate) external onlyOwner updateReward(address(0)) {
        rewardRate = _rewardRate;
        emit RewardRateUpdated(_rewardRate);
    }
    
    function addRewards(uint256 amount) external onlyOwner {
        require(rewardToken.transferFrom(msg.sender, address(this), amount), "Transfer failed");
    }
    
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = rewardToken.balanceOf(address(this));
        require(rewardToken.transfer(owner(), balance), "Transfer failed");
    }
}
