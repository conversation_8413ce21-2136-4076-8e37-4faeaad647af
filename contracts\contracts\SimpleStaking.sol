// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

// Interface for TokenB reward minting
interface IRewardToken {
    function mintRewards(address to, uint256 amount) external;
    function isRewardMinter(address minter) external view returns (bool);
}

/**
 * @title NexusStaking
 * @dev Advanced staking contract for LP tokens on Nexus Layer 1
 * Features:
 * - Time-based reward calculations
 * - Multiple reward tiers based on staking duration
 * - Emergency pause functionality
 * - Reward minting integration with TokenB
 * - Compound staking mechanism
 * - Batch operations for gas optimization
 * - Anti-whale mechanisms
 */
contract NexusStaking is ReentrancyGuard, Ownable, Pausable {
    IERC20 public immutable stakingToken; // LP Token
    IRewardToken public immutable rewardToken;  // Reward Token (TokenB) with minting capability

    // Reward configuration
    uint256 public baseRewardRate = 100; // Base: 100 tokens per second
    uint256 public lastUpdateTime;
    uint256 public rewardPerTokenStored;
    uint256 public totalStaked;

    // Staking tiers (bonus multipliers based on staking duration)
    uint256 public constant TIER_1_DURATION = 7 days;   // 1.2x multiplier
    uint256 public constant TIER_2_DURATION = 30 days;  // 1.5x multiplier
    uint256 public constant TIER_3_DURATION = 90 days;  // 2.0x multiplier

    uint256 public constant TIER_1_MULTIPLIER = 120; // 1.2x = 120/100
    uint256 public constant TIER_2_MULTIPLIER = 150; // 1.5x = 150/100
    uint256 public constant TIER_3_MULTIPLIER = 200; // 2.0x = 200/100
    uint256 public constant MULTIPLIER_BASE = 100;

    // Anti-whale mechanism
    uint256 public maxStakePerUser = 10000 * 10**18; // 10k LP tokens max per user
    uint256 public maxTotalStake = 1000000 * 10**18;  // 1M LP tokens total pool cap

    // User data
    mapping(address => uint256) public userRewardPerTokenPaid;
    mapping(address => uint256) public rewards;
    mapping(address => uint256) public balances;
    mapping(address => uint256) public stakingTime;
    mapping(address => uint256) public lastClaimTime;

    // Statistics
    uint256 public totalRewardsDistributed;
    uint256 public totalStakers;
    mapping(address => bool) public hasStaked;
    
    event Staked(address indexed user, uint256 amount, uint256 newBalance);
    event Withdrawn(address indexed user, uint256 amount, uint256 newBalance);
    event RewardPaid(address indexed user, uint256 reward, uint256 tier);
    event RewardRateUpdated(uint256 oldRate, uint256 newRate);
    event MaxStakeUpdated(uint256 oldMax, uint256 newMax);
    event EmergencyPaused(address indexed by);
    event EmergencyUnpaused(address indexed by);

    constructor(
        address _stakingToken,
        address _rewardToken
    ) Ownable(msg.sender) {
        require(_stakingToken != address(0), "Staking token cannot be zero address");
        require(_rewardToken != address(0), "Reward token cannot be zero address");

        stakingToken = IERC20(_stakingToken);
        rewardToken = IRewardToken(_rewardToken);
        lastUpdateTime = block.timestamp;

        // Note: Authorization will be set after deployment
    }
    
    modifier updateReward(address account) {
        rewardPerTokenStored = rewardPerToken();
        lastUpdateTime = block.timestamp;
        if (account != address(0)) {
            rewards[account] = earned(account);
            userRewardPerTokenPaid[account] = rewardPerTokenStored;
        }
        _;
    }
    
    /**
     * @dev Stake LP tokens with anti-whale protection
     * Transaction 1: Approve LP tokens
     * Transaction 2: Stake
     */
    function stake(uint256 amount) external nonReentrant updateReward(msg.sender) whenNotPaused {
        require(amount > 0, "Cannot stake 0");
        require(balances[msg.sender] + amount <= maxStakePerUser, "Exceeds max stake per user");
        require(totalStaked + amount <= maxTotalStake, "Exceeds total pool cap");

        // Track new stakers
        if (!hasStaked[msg.sender]) {
            hasStaked[msg.sender] = true;
            totalStakers++;
        }

        totalStaked += amount;
        balances[msg.sender] += amount;

        // Set staking time for new stakes (for tier calculation)
        if (stakingTime[msg.sender] == 0) {
            stakingTime[msg.sender] = block.timestamp;
        }

        require(stakingToken.transferFrom(msg.sender, address(this), amount), "Transfer failed");

        emit Staked(msg.sender, amount, balances[msg.sender]);
    }
    
    /**
     * @dev Withdraw staked LP tokens
     * Transaction: Withdraw
     */
    function withdraw(uint256 amount) public nonReentrant updateReward(msg.sender) whenNotPaused {
        require(amount > 0, "Cannot withdraw 0");
        require(balances[msg.sender] >= amount, "Insufficient balance");
        
        totalStaked -= amount;
        balances[msg.sender] -= amount;
        
        require(stakingToken.transfer(msg.sender, amount), "Transfer failed");

        emit Withdrawn(msg.sender, amount, balances[msg.sender]);
    }
    
    /**
     * @dev Claim rewards with tier bonus
     * Transaction: Claim
     */
    function claimReward() public nonReentrant updateReward(msg.sender) whenNotPaused {
        uint256 baseReward = rewards[msg.sender];
        require(baseReward > 0, "No rewards to claim");

        // Calculate tier bonus
        uint256 stakingDuration = block.timestamp - stakingTime[msg.sender];
        uint256 tierMultiplier = getTierMultiplier(stakingDuration);
        uint256 finalReward = (baseReward * tierMultiplier) / MULTIPLIER_BASE;

        rewards[msg.sender] = 0;
        lastClaimTime[msg.sender] = block.timestamp;
        totalRewardsDistributed += finalReward;

        // Mint rewards directly (no need for pre-funding)
        rewardToken.mintRewards(msg.sender, finalReward);

        emit RewardPaid(msg.sender, finalReward, tierMultiplier);
    }
    
    /**
     * @dev Withdraw all and claim rewards
     * Transaction: Exit (withdraw + claim in one)
     */
    function exit() external {
        withdraw(balances[msg.sender]);
        claimReward();
    }
    
    /**
     * @dev Compound rewards (stake rewards as LP tokens if possible)
     * Transaction: Compound
     */
    function compound() external nonReentrant updateReward(msg.sender) {
        uint256 reward = rewards[msg.sender];
        require(reward > 0, "No rewards to compound");
        
        rewards[msg.sender] = 0;
        
        // For simplicity, just add to balance (in real scenario, would convert to LP)
        totalStaked += reward;
        balances[msg.sender] += reward;
        
        uint256 tierMultiplier = getTierMultiplier(block.timestamp - stakingTime[msg.sender]);
        emit RewardPaid(msg.sender, reward, tierMultiplier);
        emit Staked(msg.sender, reward, balances[msg.sender]);
    }
    
    /**
     * @dev Multiple small stakes for more transactions
     */
    function multiStake(uint256[] calldata amounts) external nonReentrant updateReward(msg.sender) {
        uint256 totalAmount = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            require(amounts[i] > 0, "Cannot stake 0");
            totalAmount += amounts[i];
        }

        totalStaked += totalAmount;
        balances[msg.sender] += totalAmount;
        stakingTime[msg.sender] = block.timestamp;

        require(stakingToken.transferFrom(msg.sender, address(this), totalAmount), "Transfer failed");

        for (uint256 i = 0; i < amounts.length; i++) {
            emit Staked(msg.sender, amounts[i], balances[msg.sender]);
        }
    }

    /**
     * @dev Multiple small withdrawals for more transactions
     */
    function multiWithdraw(uint256[] calldata amounts) external nonReentrant updateReward(msg.sender) {
        uint256 totalAmount = 0;
        for (uint256 i = 0; i < amounts.length; i++) {
            require(amounts[i] > 0, "Cannot withdraw 0");
            totalAmount += amounts[i];
        }

        require(balances[msg.sender] >= totalAmount, "Insufficient balance");

        totalStaked -= totalAmount;
        balances[msg.sender] -= totalAmount;

        require(stakingToken.transfer(msg.sender, totalAmount), "Transfer failed");

        for (uint256 i = 0; i < amounts.length; i++) {
            emit Withdrawn(msg.sender, amounts[i], balances[msg.sender]);
        }
    }

    /**
     * @dev Multiple claims for more transactions (just emit multiple events)
     */
    function multiClaim(uint256 times) external nonReentrant updateReward(msg.sender) {
        uint256 reward = rewards[msg.sender];
        require(reward > 0, "No rewards to claim");

        rewards[msg.sender] = 0;
        rewardToken.mintRewards(msg.sender, reward);

        // Emit multiple events for transaction count
        uint256 tierMultiplier = getTierMultiplier(block.timestamp - stakingTime[msg.sender]);
        for (uint256 i = 0; i < times; i++) {
            emit RewardPaid(msg.sender, reward / times, tierMultiplier);
        }
    }
    
    // View functions
    function rewardPerToken() public view returns (uint256) {
        if (totalStaked == 0) {
            return rewardPerTokenStored;
        }
        return rewardPerTokenStored +
            (((block.timestamp - lastUpdateTime) * baseRewardRate * 1e18) / totalStaked);
    }

    function earned(address account) public view returns (uint256) {
        return ((balances[account] * (rewardPerToken() - userRewardPerTokenPaid[account])) / 1e18) +
            rewards[account];
    }

    /**
     * @dev Get tier multiplier based on staking duration
     */
    function getTierMultiplier(uint256 stakingDuration) public pure returns (uint256) {
        if (stakingDuration >= TIER_3_DURATION) {
            return TIER_3_MULTIPLIER; // 2.0x
        } else if (stakingDuration >= TIER_2_DURATION) {
            return TIER_2_MULTIPLIER; // 1.5x
        } else if (stakingDuration >= TIER_1_DURATION) {
            return TIER_1_MULTIPLIER; // 1.2x
        } else {
            return MULTIPLIER_BASE; // 1.0x
        }
    }

    /**
     * @dev Get user's current tier info
     */
    function getUserTierInfo(address user) external view returns (
        uint256 currentTier,
        uint256 multiplier,
        uint256 stakingDuration,
        uint256 nextTierIn
    ) {
        stakingDuration = stakingTime[user] > 0 ? block.timestamp - stakingTime[user] : 0;
        multiplier = getTierMultiplier(stakingDuration);

        if (stakingDuration >= TIER_3_DURATION) {
            currentTier = 3;
            nextTierIn = 0; // Max tier reached
        } else if (stakingDuration >= TIER_2_DURATION) {
            currentTier = 2;
            nextTierIn = TIER_3_DURATION - stakingDuration;
        } else if (stakingDuration >= TIER_1_DURATION) {
            currentTier = 1;
            nextTierIn = TIER_2_DURATION - stakingDuration;
        } else {
            currentTier = 0;
            nextTierIn = TIER_1_DURATION - stakingDuration;
        }
    }
    
    function getStakingInfo(address user) external view returns (
        uint256 stakedBalance,
        uint256 earnedRewards,
        uint256 stakingTimestamp,
        uint256 totalPoolStaked,
        uint256 currentTier,
        uint256 tierMultiplier,
        uint256 projectedReward
    ) {
        uint256 stakingDuration = stakingTime[user] > 0 ? block.timestamp - stakingTime[user] : 0;
        tierMultiplier = getTierMultiplier(stakingDuration);
        uint256 baseReward = earned(user);
        projectedReward = (baseReward * tierMultiplier) / MULTIPLIER_BASE;

        // Determine current tier
        if (stakingDuration >= TIER_3_DURATION) currentTier = 3;
        else if (stakingDuration >= TIER_2_DURATION) currentTier = 2;
        else if (stakingDuration >= TIER_1_DURATION) currentTier = 1;
        else currentTier = 0;

        return (
            balances[user],
            baseReward,
            stakingTime[user],
            totalStaked,
            currentTier,
            tierMultiplier,
            projectedReward
        );
    }

    /**
     * @dev Get pool statistics
     */
    function getPoolStats() external view returns (
        uint256 totalStakedAmount,
        uint256 totalStakersCount,
        uint256 totalRewardsGiven,
        uint256 currentRewardRate,
        uint256 poolCapacity,
        uint256 maxUserStake
    ) {
        return (
            totalStaked,
            totalStakers,
            totalRewardsDistributed,
            baseRewardRate,
            maxTotalStake,
            maxStakePerUser
        );
    }
    
    // Admin functions
    /**
     * @dev Update base reward rate (only owner)
     */
    function setRewardRate(uint256 _rewardRate) external onlyOwner updateReward(address(0)) {
        uint256 oldRate = baseRewardRate;
        baseRewardRate = _rewardRate;
        emit RewardRateUpdated(oldRate, _rewardRate);
    }

    /**
     * @dev Update max stake per user (only owner)
     */
    function setMaxStakePerUser(uint256 _maxStake) external onlyOwner {
        uint256 oldMax = maxStakePerUser;
        maxStakePerUser = _maxStake;
        emit MaxStakeUpdated(oldMax, _maxStake);
    }

    /**
     * @dev Update max total stake (only owner)
     */
    function setMaxTotalStake(uint256 _maxTotalStake) external onlyOwner {
        require(_maxTotalStake >= totalStaked, "Cannot set below current total");
        maxTotalStake = _maxTotalStake;
    }

    /**
     * @dev Pause the contract (emergency function)
     */
    function pause() external onlyOwner {
        _pause();
        emit EmergencyPaused(msg.sender);
    }

    /**
     * @dev Unpause the contract
     */
    function unpause() external onlyOwner {
        _unpause();
        emit EmergencyUnpaused(msg.sender);
    }

    /**
     * @dev Emergency withdraw staking tokens (only when paused)
     */
    function emergencyWithdrawStaking() external onlyOwner whenPaused {
        uint256 balance = stakingToken.balanceOf(address(this));
        require(stakingToken.transfer(owner(), balance), "Transfer failed");
    }
}
