'use client';

import { useState, useEffect } from 'react';

interface StakingInterfaceProps {
  account: string;
  isConnected: boolean;
  isCorrectNetwork: boolean;
  nexBalance: string;
  tokenABalance: string;
  tokenBBalance: string;
  lpTokenBalance: string;
  stakedBalance: string;
  earnedRewards: string;
  onStake: (amount: string) => Promise<void>;
  onWithdraw: (amount: string) => Promise<void>;
  onClaim: () => Promise<void>;
  loading: boolean;
}

export default function StakingInterface({
  account,
  isConnected,
  isCorrectNetwork,
  nexBalance,
  tokenABalance,
  tokenBBalance,
  lpTokenBalance,
  stakedBalance,
  earnedRewards,
  onStake,
  onWithdraw,
  onClaim,
  loading
}: StakingInterfaceProps) {
  const [activeTab, setActiveTab] = useState('stake');
  const [stakeAmount, setStakeAmount] = useState('');
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [showStakeTokenSelector, setShowStakeTokenSelector] = useState(false);
  const [selectedStakeToken, setSelectedStakeToken] = useState({
    symbol: 'NLP',
    name: 'Nexus LP Token',
    address: 'lp',
    balance: lpTokenBalance,
    decimals: 18
  });

  const stakeTokens = [
    {
      symbol: 'NEX',
      name: 'Nexus',
      address: 'native',
      balance: nexBalance,
      decimals: 18
    },
    {
      symbol: 'TKNA',
      name: 'Token A',
      address: 'tokenA',
      balance: tokenABalance,
      decimals: 18
    },
    {
      symbol: 'TKNB',
      name: 'Token B',
      address: 'tokenB',
      balance: tokenBBalance,
      decimals: 18
    },
    {
      symbol: 'NLP',
      name: 'Nexus LP Token',
      address: 'lp',
      balance: lpTokenBalance,
      decimals: 18
    }
  ];

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance);
    if (num === 0) return '0.00';
    if (num < 0.01) return '<0.01';
    return num.toFixed(4);
  };

  const handleSelectStakeToken = (token: any) => {
    setSelectedStakeToken(token);
    setStakeAmount('');
    setShowStakeTokenSelector(false);
  };

  // Update token balances when props change
  useEffect(() => {
    setSelectedStakeToken(prev => ({
      ...prev,
      balance: prev.symbol === 'NEX' ? nexBalance : 
               prev.symbol === 'TKNA' ? tokenABalance : 
               prev.symbol === 'TKNB' ? tokenBBalance : lpTokenBalance
    }));
  }, [nexBalance, tokenABalance, tokenBBalance, lpTokenBalance]);

  const canStake = isConnected && isCorrectNetwork && stakeAmount && 
    parseFloat(stakeAmount) > 0 && parseFloat(stakeAmount) <= parseFloat(selectedStakeToken.balance);

  const canWithdraw = isConnected && isCorrectNetwork && withdrawAmount && 
    parseFloat(withdrawAmount) > 0 && parseFloat(withdrawAmount) <= parseFloat(stakedBalance);

  const canClaim = isConnected && isCorrectNetwork && parseFloat(earnedRewards) > 0;

  const handleStake = async () => {
    if (!canStake) return;
    try {
      await onStake(stakeAmount);
      setStakeAmount('');
    } catch (error) {
      console.error('Stake failed:', error);
    }
  };

  const handleWithdraw = async () => {
    if (!canWithdraw) return;
    try {
      await onWithdraw(withdrawAmount);
      setWithdrawAmount('');
    } catch (error) {
      console.error('Withdraw failed:', error);
    }
  };

  const handleClaim = async () => {
    if (!canClaim) return;
    try {
      await onClaim();
    } catch (error) {
      console.error('Claim failed:', error);
    }
  };

  return (
    <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white">Staking</h2>
        <div className="flex items-center space-x-2 bg-slate-700/50 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('stake')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'stake'
                ? 'bg-nexus-600 text-white'
                : 'text-slate-400 hover:text-white'
            }`}
          >
            Stake
          </button>
          <button
            onClick={() => setActiveTab('withdraw')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'withdraw'
                ? 'bg-nexus-600 text-white'
                : 'text-slate-400 hover:text-white'
            }`}
          >
            Withdraw
          </button>
        </div>
      </div>

      {/* Staking Stats */}
      <div className="grid grid-cols-3 gap-4 mb-6 p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
        <div className="text-center">
          <p className="text-xs text-slate-400">Available</p>
          <p className="text-sm text-white font-mono">{formatBalance(lpTokenBalance)} NLP</p>
        </div>
        <div className="text-center">
          <p className="text-xs text-slate-400">Staked</p>
          <p className="text-sm text-white font-mono">{formatBalance(stakedBalance)} NLP</p>
        </div>
        <div className="text-center">
          <p className="text-xs text-slate-400">Rewards</p>
          <p className="text-sm text-nexus-400 font-mono">{formatBalance(earnedRewards)} TKNB</p>
        </div>
      </div>

      {/* Rewards Section */}
      {parseFloat(earnedRewards) > 0 && (
        <div className="mb-6 p-4 bg-gradient-to-r from-nexus-900/50 to-nexus-800/50 rounded-lg border border-nexus-600/50">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-400">Pending Rewards</p>
              <p className="text-2xl font-bold text-nexus-400">{formatBalance(earnedRewards)} TKNB</p>
            </div>
            <button
              onClick={handleClaim}
              disabled={!canClaim || loading}
              className="bg-nexus-600 hover:bg-nexus-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:bg-slate-600 disabled:cursor-not-allowed"
            >
              {loading ? 'Claiming...' : 'Claim'}
            </button>
          </div>
        </div>
      )}

      {activeTab === 'stake' ? (
        <div className="space-y-6">
          {/* Stake Input */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-slate-400">Stake Amount</span>
              <span className="text-sm text-slate-400">
                Available: {formatBalance(selectedStakeToken.balance)} {selectedStakeToken.symbol}
              </span>
            </div>
            <div className="bg-slate-700/50 rounded-xl p-4 border border-slate-600/50">
              <div className="flex items-center justify-between">
                <input
                  type="number"
                  value={stakeAmount}
                  onChange={(e) => setStakeAmount(e.target.value)}
                  placeholder="0.00"
                  className="bg-transparent text-white text-xl font-medium placeholder-slate-500 border-none outline-none flex-1"
                />
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setStakeAmount(selectedStakeToken.balance)}
                    className="px-2 py-1 bg-nexus-600/20 text-nexus-400 text-xs rounded hover:bg-nexus-600/30"
                  >
                    MAX
                  </button>
                  <div className="relative token-selector">
                    <button
                      onClick={() => setShowStakeTokenSelector(!showStakeTokenSelector)}
                      className="flex items-center space-x-2 bg-slate-600/50 rounded-lg px-3 py-2 hover:bg-slate-600/70 transition-colors"
                    >
                      <div className="w-6 h-6 bg-gradient-to-r from-nexus-400 to-nexus-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">{selectedStakeToken.symbol[0]}</span>
                      </div>
                      <span className="text-white font-medium">{selectedStakeToken.symbol}</span>
                      <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                    
                    {/* Stake Token Dropdown */}
                    {showStakeTokenSelector && (
                      <div className="absolute right-0 top-full mt-2 w-64 bg-slate-700 border border-slate-600 rounded-lg shadow-xl z-50">
                        <div className="p-3 border-b border-slate-600">
                          <h3 className="text-sm font-medium text-white">Select Token to Stake</h3>
                        </div>
                        <div className="max-h-60 overflow-y-auto">
                          {stakeTokens.map((token) => (
                            <button
                              key={token.address}
                              onClick={() => handleSelectStakeToken(token)}
                              className="w-full flex items-center justify-between p-3 hover:bg-slate-600/50 transition-colors"
                            >
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-gradient-to-r from-nexus-400 to-nexus-600 rounded-full flex items-center justify-center">
                                  <span className="text-white text-sm font-bold">{token.symbol[0]}</span>
                                </div>
                                <div className="text-left">
                                  <p className="text-white font-medium">{token.symbol}</p>
                                  <p className="text-slate-400 text-xs">{token.name}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="text-white text-sm">{formatBalance(token.balance)}</p>
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* APY Info */}
          <div className="p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Estimated APY</span>
              <span className="text-sm text-green-400 font-medium">~50%</span>
            </div>
          </div>

          {/* Stake Button */}
          <button
            onClick={handleStake}
            disabled={!canStake || loading}
            className={`w-full py-4 rounded-xl font-medium text-lg transition-all ${
              !canStake
                ? 'bg-slate-700/50 text-slate-500 cursor-not-allowed'
                : loading
                ? 'bg-nexus-600/50 text-white cursor-wait'
                : 'bg-nexus-600 hover:bg-nexus-700 text-white hover:shadow-lg hover:shadow-nexus-500/25'
            }`}
          >
            {loading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span>Staking...</span>
              </div>
            ) : !isConnected ? (
              'Connect Wallet'
            ) : !isCorrectNetwork ? (
              'Switch to Nexus Network'
            ) : (
              'Stake LP Tokens'
            )}
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Withdraw Input */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-slate-400">Withdraw Amount</span>
              <span className="text-sm text-slate-400">
                Staked: {formatBalance(stakedBalance)} NLP
              </span>
            </div>
            <div className="bg-slate-700/50 rounded-xl p-4 border border-slate-600/50">
              <div className="flex items-center justify-between">
                <input
                  type="number"
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                  placeholder="0.00"
                  className="bg-transparent text-white text-xl font-medium placeholder-slate-500 border-none outline-none flex-1"
                />
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setWithdrawAmount(stakedBalance)}
                    className="px-2 py-1 bg-red-600/20 text-red-400 text-xs rounded hover:bg-red-600/30"
                  >
                    MAX
                  </button>
                  <div className="flex items-center space-x-2 bg-slate-600/50 rounded-lg px-3 py-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-red-400 to-red-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">LP</span>
                    </div>
                    <span className="text-white font-medium">NLP</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Percentage Buttons */}
          <div className="grid grid-cols-4 gap-2">
            {['25%', '50%', '75%', '100%'].map((percentage) => (
              <button
                key={percentage}
                onClick={() => {
                  const percent = parseFloat(percentage) / 100;
                  setWithdrawAmount((parseFloat(stakedBalance) * percent).toString());
                }}
                className="py-2 bg-slate-700 hover:bg-slate-600 text-white text-sm rounded-lg transition-colors"
              >
                {percentage}
              </button>
            ))}
          </div>

          {/* Warning */}
          <div className="p-3 bg-yellow-900/30 border border-yellow-600/50 rounded-lg">
            <p className="text-xs text-yellow-400">
              ⚠️ Withdrawing will claim your pending rewards automatically
            </p>
          </div>

          {/* Withdraw Button */}
          <button
            onClick={handleWithdraw}
            disabled={!canWithdraw || loading}
            className={`w-full py-4 rounded-xl font-medium text-lg transition-all ${
              !canWithdraw
                ? 'bg-slate-700/50 text-slate-500 cursor-not-allowed'
                : loading
                ? 'bg-red-600/50 text-white cursor-wait'
                : 'bg-red-600 hover:bg-red-700 text-white hover:shadow-lg hover:shadow-red-500/25'
            }`}
          >
            {loading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span>Withdrawing...</span>
              </div>
            ) : !isConnected ? (
              'Connect Wallet'
            ) : !isCorrectNetwork ? (
              'Switch to Nexus Network'
            ) : (
              'Withdraw Staked Tokens'
            )}
          </button>
        </div>
      )}
    </div>
  );
}