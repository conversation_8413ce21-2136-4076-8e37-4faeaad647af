{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/find-head-in-cache.ts"], "names": ["findHeadInCache", "cache", "parallelRoutes", "isLastItem", "Object", "keys", "length", "head", "key", "segment", "childPara<PERSON>l<PERSON><PERSON><PERSON>", "childSegmentMap", "get", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "cacheNode", "item", "undefined"], "mappings": ";;;;+BAIgBA;;;eAAAA;;;sCAFqB;AAE9B,SAASA,gBACdC,KAAgB,EAChBC,cAAoC;IAEpC,MAAMC,aAAaC,OAAOC,IAAI,CAACH,gBAAgBI,MAAM,KAAK;IAC1D,IAAIH,YAAY;QACd,OAAOF,MAAMM,IAAI;IACnB;IACA,IAAK,MAAMC,OAAON,eAAgB;QAChC,MAAM,CAACO,SAASC,oBAAoB,GAAGR,cAAc,CAACM,IAAI;QAC1D,MAAMG,kBAAkBV,MAAMC,cAAc,CAACU,GAAG,CAACJ;QACjD,IAAI,CAACG,iBAAiB;YACpB;QACF;QAEA,MAAME,WAAWC,IAAAA,0CAAoB,EAACL;QAEtC,MAAMM,YAAYJ,gBAAgBC,GAAG,CAACC;QACtC,IAAI,CAACE,WAAW;YACd;QACF;QAEA,MAAMC,OAAOhB,gBAAgBe,WAAWL;QACxC,IAAIM,MAAM;YACR,OAAOA;QACT;IACF;IAEA,OAAOC;AACT"}