# 🚀 Deployment Guide - Nexus Token Swap DeFi Application

## Prerequisites

### 1. Install Required Tools
```bash
# Install Node.js (v18 or higher)
# Download from: https://nodejs.org/

# Verify installation
node --version
npm --version
```

### 2. Get NEX Tokens for Deployment
You need NEX tokens to pay for gas fees when deploying contracts:

**Option 1: Nexus Faucet (Recommended for testing)**
- Visit: https://faucet.nexus.xyz
- Connect your wallet
- Request test NEX tokens

**Option 2: Earn NEX through Proving**
- Visit: https://app.nexus.xyz
- Submit proofs to earn NEX in real-time
- More sustainable for extensive testing

### 3. Get Your Private Key
1. Go to https://app.nexus.xyz
2. Sign in to your account
3. Click "Settings" tab
4. Navigate to "Account & Security"
5. Click on "Private Key"
6. Click "Reveal" to view your private key
7. **IMPORTANT**: Copy without the "0x" prefix

## 🛠️ Setup Instructions

### Step 1: Clone and Install Dependencies
```bash
# Clone the repository
git clone <your-repo-url>
cd nexus-swap-defi-app

# Install all dependencies
npm run install:all
```

### Step 2: Environment Configuration
```bash
# Copy environment templates
cp .env.example .env
cp contracts/.env.example contracts/.env

# Edit .env files and add your private key
# PRIVATE_KEY=your_private_key_here_without_0x_prefix
```

### Step 3: Compile Smart Contracts
```bash
# Compile contracts
npm run contracts:compile
```

### Step 4: Run Tests (Optional but Recommended)
```bash
# Run contract tests
npm run contracts:test
```

## 🚀 Deployment Process

### Step 1: Deploy Smart Contracts
```bash
# Deploy to Nexus Testnet III
npm run contracts:deploy
```

**Expected Output:**
```
🚀 Starting deployment to Nexus network...
Network: nexus
Chain ID: 3940
Deploying contracts with account: 0x...
Account balance: 1.0 NEX

📄 Deploying TokenA...
✅ TokenA deployed to: 0x...

📄 Deploying TokenB...
✅ TokenB deployed to: 0x...

📄 Deploying TokenSwap...
✅ TokenSwap deployed to: 0x...

💧 Adding liquidity to TokenSwap contract...
✅ Transferred 500000.0 TokenB to swap contract

🎉 Deployment completed successfully!
```

### Step 2: Verify Contracts (Optional)
```bash
# Verify contracts on Nexus Explorer
npm run contracts:verify
```

### Step 3: Start Frontend
```bash
# Start development server
npm run frontend:dev
```

The application will be available at: http://localhost:3000

## 📋 Post-Deployment Checklist

### ✅ Verify Deployment
1. **Check Contract Addresses**: Ensure `lib/deployedAddresses.json` is updated
2. **Verify on Explorer**: Visit Nexus Explorer links provided in deployment output
3. **Test Liquidity**: Confirm TokenSwap contract has Token B liquidity

### ✅ Test Frontend
1. **Connect Wallet**: Ensure MetaMask connects to Nexus Testnet III
2. **Check Balances**: Verify Token A and Token B balances display correctly
3. **Test Swap**: Perform a small test swap to ensure functionality

### ✅ Network Configuration
Ensure MetaMask is configured for Nexus Testnet III:
- **Network Name**: Nexus Testnet III
- **RPC URL**: https://testnet3.rpc.nexus.xyz
- **Chain ID**: 3940
- **Currency Symbol**: NEX
- **Block Explorer**: https://explorer.nexus.xyz

## 🔧 Troubleshooting

### Common Issues

**1. "Deployer account has no NEX tokens"**
- Solution: Get NEX from faucet or through proving
- Check: https://faucet.nexus.xyz

**2. "Wrong Network" in frontend**
- Solution: Switch MetaMask to Nexus Testnet III
- Add network manually if not present

**3. "Contract addresses not found"**
- Solution: Ensure deployment completed successfully
- Check: `lib/deployedAddresses.json` exists and has valid addresses

**4. "Insufficient Token B liquidity"**
- Solution: Deployment script automatically adds liquidity
- Check: Contract deployment logs for liquidity transfer

**5. "Transaction failed"**
- Solution: Ensure sufficient NEX for gas fees
- Check: Network congestion and try again

### Debug Commands
```bash
# Check contract compilation
npm run contracts:compile

# Run tests to verify contract logic
npm run contracts:test

# Clean and rebuild
npm run clean:cache
npm run contracts:compile

# Check deployment addresses
cat lib/deployedAddresses.json
```

## 🌐 Production Deployment

### Frontend Deployment (Vercel)
```bash
# Build for production
npm run frontend:build

# Deploy to Vercel
npx vercel --prod
```

### Environment Variables for Production
Set these in your deployment platform:
```
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production
```

## 📚 Additional Resources

- **Nexus Documentation**: https://docs.nexus.xyz
- **Nexus Explorer**: https://explorer.nexus.xyz
- **Nexus Faucet**: https://faucet.nexus.xyz
- **MetaMask Setup**: https://metamask.io/

## 🔒 Security Reminders

- ✅ Never share your private key
- ✅ Never commit `.env` files to version control
- ✅ Use test accounts for development
- ✅ Keep private keys secure and backed up
- ✅ Verify contract addresses before interacting

## 🆘 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Review contract deployment logs
3. Verify network configuration
4. Test with small amounts first
