{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "names": ["buildAppStaticPaths", "buildStaticPaths", "collectGenerateParams", "loadComponents", "setHttpClientAndAgentOptions", "isAppRouteRouteModule", "loadStaticPaths", "dir", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "incremental<PERSON>ache<PERSON>andlerPath", "ppr", "require", "setConfig", "components", "getStaticPaths", "Error", "routeModule", "generateParams", "revalidate", "userland", "dynamic", "dynamicParams", "generateStaticParams", "segmentPath", "ComponentMod", "tree", "configFileName"], "mappings": "AAEA,OAAO,kBAAiB;AACxB,OAAO,sBAAqB;AAE5B,SACEA,mBAAmB,EACnBC,gBAAgB,EAChBC,qBAAqB,QAChB,oBAAmB;AAE1B,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,4BAA4B,QAAQ,0BAAyB;AAEtE,SAASC,qBAAqB,QAAQ,iCAAgC;AAQtE,yDAAyD;AACzD,uDAAuD;AACvD,4BAA4B;AAC5B,OAAO,eAAeC,gBAAgB,EACpCC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,2BAA2B,EAC3BC,GAAG,EAiBJ;IAKC,oCAAoC;IACpCC,QAAQ,4CAA4CC,SAAS,CAACb;IAC9DN,6BAA6B;QAC3BO;IACF;IAEA,MAAMa,aAAa,MAAMrB,eAAe;QACtCK;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;IACF;IAEA,IAAI,CAACU,WAAWC,cAAc,IAAI,CAACX,WAAW;QAC5C,yDAAyD;QACzD,mDAAmD;QACnD,MAAM,IAAIY,MACR,CAAC,uDAAuD,EAAEjB,SAAS,CAAC;IAExE;IAEA,IAAIK,WAAW;QACb,MAAM,EAAEa,WAAW,EAAE,GAAGH;QACxB,MAAMI,iBACJD,eAAetB,sBAAsBsB,eACjC;YACE;gBACEjB,QAAQ;oBACNmB,YAAYF,YAAYG,QAAQ,CAACD,UAAU;oBAC3CE,SAASJ,YAAYG,QAAQ,CAACC,OAAO;oBACrCC,eAAeL,YAAYG,QAAQ,CAACE,aAAa;gBACnD;gBACAC,sBAAsBN,YAAYG,QAAQ,CAACG,oBAAoB;gBAC/DC,aAAazB;YACf;SACD,GACD,MAAMP,sBAAsBsB,WAAWW,YAAY,CAACC,IAAI;QAE9D,OAAO,MAAMpC,oBAAoB;YAC/BO;YACAQ,MAAMN;YACNmB;YACAS,gBAAgB3B,OAAO2B,cAAc;YACrC7B;YACAW;YACAC;YACAJ;YACAC;YACAC;YACAG;YACAc,cAAcX,WAAWW,YAAY;QACvC;IACF;IAEA,OAAO,MAAMlC,iBAAiB;QAC5Bc,MAAMN;QACNgB,gBAAgBD,WAAWC,cAAc;QACzCY,gBAAgB3B,OAAO2B,cAAc;QACrCzB;QACAC;IACF;AACF"}