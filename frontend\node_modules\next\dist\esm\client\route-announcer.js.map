{"version": 3, "sources": ["../../src/client/route-announcer.tsx"], "names": ["React", "useRouter", "nextjsRouteAnnouncerStyles", "border", "clip", "height", "margin", "overflow", "padding", "position", "top", "width", "whiteSpace", "wordWrap", "RouteAnnouncer", "<PERSON><PERSON><PERSON>", "routeAnnouncement", "setRouteAnnouncement", "useState", "previouslyLoaded<PERSON><PERSON>", "useRef", "useEffect", "current", "document", "title", "pageHeader", "querySelector", "content", "innerText", "textContent", "p", "aria-live", "id", "role", "style"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,SAAS,QAAQ,WAAU;AAEpC,MAAMC,6BAAkD;IACtDC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC,KAAK;IACLC,OAAO;IAEP,wFAAwF;IACxFC,YAAY;IACZC,UAAU;AACZ;AAEA,OAAO,MAAMC,iBAAiB;IAC5B,MAAM,EAAEC,MAAM,EAAE,GAAGd;IACnB,MAAM,CAACe,mBAAmBC,qBAAqB,GAAGjB,MAAMkB,QAAQ,CAAC;IAEjE,2EAA2E;IAC3E,qCAAqC;IACrC,MAAMC,uBAAuBnB,MAAMoB,MAAM,CAACL;IAE1C,4EAA4E;IAC5E,6EAA6E;IAC7E,8EAA8E;IAC9E,0EAA0E;IAC1E,iCAAiC;IACjC,mFAAmF;IACnFf,MAAMqB,SAAS,CACb;QACE,4CAA4C;QAC5C,IAAIF,qBAAqBG,OAAO,KAAKP,QAAQ;QAC7CI,qBAAqBG,OAAO,GAAGP;QAE/B,IAAIQ,SAASC,KAAK,EAAE;YAClBP,qBAAqBM,SAASC,KAAK;QACrC,OAAO;YACL,MAAMC,aAAaF,SAASG,aAAa,CAAC;gBAC1BD;YAAhB,MAAME,UAAUF,CAAAA,wBAAAA,8BAAAA,WAAYG,SAAS,YAArBH,wBAAyBA,8BAAAA,WAAYI,WAAW;YAEhEZ,qBAAqBU,WAAWZ;QAClC;IACF,GACA,wEAAwE;IACxE;QAACA;KAAO;IAGV,qBACE,oBAACe;QACCC,aAAU,YAAY,qCAAqC;;QAC3DC,IAAG;QACHC,MAAK;QACLC,OAAOhC;OAENc;AAGP,EAAC;AAED,eAAeF,eAAc"}