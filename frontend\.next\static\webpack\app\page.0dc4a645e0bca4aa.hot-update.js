"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Contract ABIs - Updated with complete function signatures\nconst TokenA_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) external returns (bool)\",\n    \"function allowance(address owner, address spender) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) external returns (bool)\",\n    \"function transferFrom(address from, address to, uint256 amount) external returns (bool)\"\n];\nconst TokenB_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\"\n];\nconst TokenSwap_ABI = [\n    \"function swap(uint256 amount) external returns (bool)\",\n    \"function getTokenBLiquidity() external view returns (uint256)\",\n    \"function getTokenABalance() external view returns (uint256)\",\n    \"function tokenA() external view returns (address)\",\n    \"function tokenB() external view returns (address)\",\n    \"event Swap(address indexed user, uint256 amount)\"\n];\n// Nexus network configuration\nconst NEXUS_NETWORK = {\n    chainId: \"0xF64\",\n    chainName: \"Nexus Testnet III\",\n    nativeCurrency: {\n        name: \"NEX\",\n        symbol: \"NEX\",\n        decimals: 18\n    },\n    rpcUrls: [\n        \"https://testnet3.rpc.nexus.xyz\"\n    ],\n    blockExplorerUrls: [\n        \"https://explorer.nexus.xyz\"\n    ]\n};\n// Default contract addresses (will be replaced with deployed addresses)\nconst DEFAULT_ADDRESSES = {\n    TokenA: \"0x0000000000000000000000000000000000000000\",\n    TokenB: \"0x0000000000000000000000000000000000000000\",\n    TokenSwap: \"0x0000000000000000000000000000000000000000\"\n};\nfunction Home() {\n    _s();\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenABalance, setTokenABalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [tokenBBalance, setTokenBBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [swapAmount, setSwapAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_ADDRESSES);\n    const [liquidity, setLiquidity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [isCorrectNetwork, setIsCorrectNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkIfWalletIsConnected();\n        loadContractAddresses();\n        setupEventListeners();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (account && isCorrectNetwork) {\n            updateBalances();\n            updateLiquidity();\n        }\n    }, [\n        account,\n        contractAddresses,\n        isCorrectNetwork\n    ]);\n    function setupEventListeners() {\n        const { ethereum } = window;\n        if (!ethereum) return;\n        // Listen for account changes\n        ethereum.on(\"accountsChanged\", (accounts)=>{\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                checkNetwork();\n            } else {\n                setAccount(\"\");\n                setIsCorrectNetwork(false);\n            }\n        });\n        // Listen for network changes\n        ethereum.on(\"chainChanged\", (chainId)=>{\n            console.log(\"Network changed to:\", chainId);\n            checkNetwork();\n            // Reload page to reset state\n            window.location.reload();\n        });\n        // Cleanup function\n        return ()=>{\n            if (ethereum.removeListener) {\n                ethereum.removeListener(\"accountsChanged\", ()=>{});\n                ethereum.removeListener(\"chainChanged\", ()=>{});\n            }\n        };\n    }\n    async function loadContractAddresses() {\n        try {\n            // Try to load deployed addresses from public folder\n            const response = await fetch(\"/deployedAddresses.json\");\n            if (response.ok) {\n                const addresses = await response.json();\n                setContractAddresses(addresses);\n                console.log(\"Loaded contract addresses:\", addresses);\n            } else {\n                console.warn(\"Could not load deployed addresses, using defaults\");\n            }\n        } catch (error) {\n            console.warn(\"Could not load deployed addresses:\", error);\n        }\n    }\n    async function checkIfWalletIsConnected() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_accounts\"\n            });\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                await checkNetwork();\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setError(\"Error saat mengecek koneksi wallet\");\n        }\n    }\n    async function checkNetwork() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setIsCorrectNetwork(false);\n                return;\n            }\n            const chainId = await ethereum.request({\n                method: \"eth_chainId\"\n            });\n            console.log(\"Current chainId:\", chainId, \"Expected:\", NEXUS_NETWORK.chainId);\n            // Convert both to same format for comparison\n            const currentChainId = parseInt(chainId, 16);\n            const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);\n            if (currentChainId === expectedChainId) {\n                setIsCorrectNetwork(true);\n                setError(\"\");\n                console.log(\"✅ Connected to correct network\");\n            } else {\n                setIsCorrectNetwork(false);\n                setError(\"Wrong network. Current: \".concat(currentChainId, \", Expected: \").concat(expectedChainId, \" (Nexus Testnet III)\"));\n                console.log(\"❌ Wrong network detected\");\n            }\n        } catch (error) {\n            console.error(\"Error checking network:\", error);\n            setIsCorrectNetwork(false);\n            setError(\"Error checking network\");\n        }\n    }\n    async function switchToNexusNetwork() {\n        try {\n            const { ethereum } = window;\n            setError(\"\");\n            setSuccess(\"Switching to Nexus network...\");\n            try {\n                await ethereum.request({\n                    method: \"wallet_switchEthereumChain\",\n                    params: [\n                        {\n                            chainId: NEXUS_NETWORK.chainId\n                        }\n                    ]\n                });\n                console.log(\"✅ Network switch requested\");\n            } catch (switchError) {\n                console.log(\"Switch error code:\", switchError.code);\n                // Network belum ditambahkan, tambahkan dulu\n                if (switchError.code === 4902) {\n                    console.log(\"Adding Nexus network...\");\n                    await ethereum.request({\n                        method: \"wallet_addEthereumChain\",\n                        params: [\n                            NEXUS_NETWORK\n                        ]\n                    });\n                    console.log(\"✅ Network added\");\n                } else {\n                    throw switchError;\n                }\n            }\n            // Wait a bit for network to switch\n            setTimeout(async ()=>{\n                await checkNetwork();\n                setSuccess(\"\");\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error switching network:\", error);\n            setError(\"Gagal switch ke Nexus network: \" + (error.message || \"Unknown error\"));\n            setSuccess(\"\");\n        }\n    }\n    async function connectWallet() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            setAccount(accounts[0]);\n            await checkNetwork();\n            setError(\"\");\n            setSuccess(\"Wallet berhasil terhubung!\");\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n            setError(\"Gagal menghubungkan wallet\");\n        }\n    }\n    async function updateBalances() {\n        if (!account || !isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {\n            console.log(\"Contract addresses not loaded yet, skipping balance update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating balances for:\", account);\n            console.log(\"TokenA address:\", contractAddresses.TokenA);\n            console.log(\"TokenB address:\", contractAddresses.TokenB);\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, provider);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, provider);\n            // Test if contracts exist\n            try {\n                const [balanceA, balanceB] = await Promise.all([\n                    tokenAContract.balanceOf(account),\n                    tokenBContract.balanceOf(account)\n                ]);\n                console.log(\"Raw balanceA:\", balanceA.toString());\n                console.log(\"Raw balanceB:\", balanceB.toString());\n                setTokenABalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceA));\n                setTokenBBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceB));\n                console.log(\"Balances updated successfully\");\n            } catch (contractError) {\n                console.error(\"Contract call error:\", contractError);\n                setError(\"Error reading token balances. Contracts may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating balances:\", error);\n            setError(\"Error connecting to contracts\");\n        }\n    }\n    async function updateLiquidity() {\n        if (!isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {\n            console.log(\"TokenSwap address not loaded yet, skipping liquidity update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating liquidity for TokenSwap:\", contractAddresses.TokenSwap);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);\n            try {\n                const liquidityAmount = await swapContract.getTokenBLiquidity();\n                console.log(\"Raw liquidity:\", liquidityAmount.toString());\n                setLiquidity(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(liquidityAmount));\n                console.log(\"Liquidity updated successfully\");\n            } catch (contractError) {\n                console.error(\"TokenSwap contract call error:\", contractError);\n                setError(\"Error reading liquidity. TokenSwap contract may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating liquidity:\", error);\n            setError(\"Error connecting to TokenSwap contract\");\n        }\n    }\n    async function handleSwap() {\n        if (!swapAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(swapAmount);\n            // Check balance\n            const balance = await tokenAContract.balanceOf(account);\n            if (balance < amount) {\n                setError(\"Saldo Token A tidak mencukupi\");\n                return;\n            }\n            // Check liquidity\n            const availableLiquidity = await swapContract.getTokenBLiquidity();\n            if (availableLiquidity < amount) {\n                setError(\"Likuiditas Token B tidak mencukupi\");\n                return;\n            }\n            // Check allowance\n            const allowance = await tokenAContract.allowance(account, contractAddresses.TokenSwap);\n            if (allowance < amount) {\n                setSuccess(\"Menyetujui penggunaan Token A...\");\n                const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, amount);\n                await approveTx.wait();\n                setSuccess(\"Approval berhasil! Melakukan swap...\");\n            } else {\n                setSuccess(\"Melakukan swap...\");\n            }\n            // Perform swap\n            const swapTx = await swapContract.swap(amount);\n            await swapTx.wait();\n            setSuccess(\"Swap berhasil! \\uD83C\\uDF89\");\n            setSwapAmount(\"\");\n            // Update balances and liquidity\n            await updateBalances();\n            await updateLiquidity();\n        } catch (error) {\n            console.error(\"Error during swap:\", error);\n            if (error.code === \"ACTION_REJECTED\") {\n                setError(\"Transaksi dibatalkan oleh user\");\n            } else if (error.message.includes(\"insufficient funds\")) {\n                setError(\"Saldo NEX tidak mencukupi untuk gas fee\");\n            } else {\n                setError(\"Swap gagal: \" + (error.reason || error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    }\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const clearMessages = ()=>{\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const isDeployerAccount = account && contractAddresses.deployer && account.toLowerCase() === contractAddresses.deployer.toLowerCase();\n    async function requestTestTokens() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"Requesting test tokens...\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            // Transfer 1000 Token A to user for testing\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"1000\");\n            const transferTx = await tokenAContract.transfer(account, amount);\n            await transferTx.wait();\n            setSuccess(\"Test tokens received! \\uD83C\\uDF89\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error requesting test tokens:\", error);\n            setError(\"Failed to get test tokens: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-6 right-6 flex flex-col items-end space-y-2\",\n                children: [\n                    account && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Connected:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            formatAddress(account)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full \".concat(isCorrectNetwork ? \"bg-green-500\" : \"bg-red-500\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: isCorrectNetwork ? \"Nexus Testnet III\" : \"Wrong Network\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 max-w-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Expected Chain: 3940 (0xF64)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Contracts: \",\n                                    contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? \"✅\" : \"❌\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Deployer: \",\n                                    contractAddresses.deployer ? contractAddresses.deployer.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Your Address: \",\n                                    account ? account.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-screen px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"Nexus Swap\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Decentralized token exchange on Nexus blockchain. Swap Token A for Token B at a fixed 1:1 ratio.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this),\n                    (error || success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mb-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-red-500 hover:text-red-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-green-500 hover:text-green-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 11\n                    }, this),\n                    !account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: connectWallet,\n                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Connect your MetaMask wallet to start swapping tokens\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 11\n                    }, this) : !isCorrectNetwork ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: switchToNexusNetwork,\n                                        className: \"bg-orange-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                        children: \"Switch to Nexus Network\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: checkNetwork,\n                                            className: \"text-sm text-blue-600 hover:text-blue-800 underline\",\n                                            children: \"Already switched? Click to refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Please switch to Nexus Testnet III to continue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl shadow-xl p-8 border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-6 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-blue-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token A Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-2xl font-bold text-blue-600\",\n                                                        children: parseFloat(tokenABalance).toFixed(4)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-purple-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token B Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-2xl font-bold text-purple-600\",\n                                                        children: parseFloat(tokenBBalance).toFixed(4)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-6 p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Available Liquidity\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-mono text-lg font-semibold text-gray-800\",\n                                                children: [\n                                                    parseFloat(liquidity).toFixed(2),\n                                                    \" Token B\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Swap Amount (Token A → Token B)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: swapAmount,\n                                                        onChange: (e)=>setSwapAmount(e.target.value),\n                                                        placeholder: \"0.0\",\n                                                        className: \"w-full bg-gray-50 border border-gray-300 px-4 py-4 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-lg\",\n                                                        disabled: loading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium\",\n                                                        children: \"TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"Exchange rate: 1 TKNA = 1 TKNB\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleSwap,\n                                        disabled: loading || !swapAmount || parseFloat(swapAmount) <= 0,\n                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Processing...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 19\n                                        }, this) : \"Swap Tokens\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-6 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 text-center\",\n                                                children: \"Smart contracts deployed on Nexus Testnet III\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400 mt-2 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"TokenA: \",\n                                                            formatAddress(contractAddresses.TokenA)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"TokenB: \",\n                                                            formatAddress(contractAddresses.TokenB)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"Swap: \",\n                                                            formatAddress(contractAddresses.TokenSwap)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"How to use:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"1. Make sure you have Token A in your wallet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"2. Enter the amount you want to swap\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: '3. Click \"Swap Tokens\" and confirm the transaction'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"4. Wait for confirmation and see your new Token B balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"EjoYfBLQTh/yMLgch/KCk+Cuf6Y=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});