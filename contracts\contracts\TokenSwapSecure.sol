// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title TokenSwapSecure
 * @dev Secure version of TokenSwap with reentrancy protection
 */
contract TokenSwapSecure is ReentrancyGuard {
    IERC20 public tokenA;
    IERC20 public tokenB;

    event Swap(address indexed user, uint256 amount);

    constructor(address _tokenA, address _tokenB) {
        require(_tokenA != address(0), "TokenA address cannot be zero");
        require(_tokenB != address(0), "TokenB address cannot be zero");
        tokenA = IERC20(_tokenA);
        tokenB = IERC20(_tokenB);
    }

    /**
     * @dev Secure swap function with reentrancy protection
     * @param amount The amount of Token A to swap for Token B
     * @return success True if the swap was successful
     */
    function swap(uint256 amount) external nonReentrant returns (bool success) {
        require(amount > 0, "Amount must be greater than 0");
        require(tokenB.balanceOf(address(this)) >= amount, "Insufficient Token B liquidity");
        
        // Check-Effects-Interactions pattern
        // 1. Checks (already done above)
        
        // 2. Effects (state changes first)
        emit Swap(msg.sender, amount);
        
        // 3. Interactions (external calls last)
        require(tokenA.transferFrom(msg.sender, address(this), amount), "Transfer Token A failed");
        require(tokenB.transfer(msg.sender, amount), "Transfer Token B failed");
        
        return true;
    }

    function getTokenBLiquidity() external view returns (uint256) {
        return tokenB.balanceOf(address(this));
    }

    function getTokenABalance() external view returns (uint256) {
        return tokenA.balanceOf(address(this));
    }
}