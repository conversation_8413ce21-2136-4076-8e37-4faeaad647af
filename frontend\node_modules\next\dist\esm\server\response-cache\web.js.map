{"version": 3, "sources": ["../../../src/server/response-cache/web.ts"], "names": ["Detached<PERSON>romise", "WebResponseCache", "constructor", "minimalMode", "pendingResponses", "Map", "Object", "assign", "get", "key", "responseGenerator", "context", "pendingResponseKey", "isOnDemandRevalidate", "pendingResponse", "promise", "resolve", "resolver", "reject", "rejecter", "set", "resolved", "cacheEntry", "Promise", "previousCacheItem", "expiresAt", "Date", "now", "entry", "delete", "resolveValue", "isMiss", "revalidate", "undefined", "err", "console", "error"], "mappings": "AAAA,SAASA,eAAe,QAAQ,6BAA4B;AAG5D;;;CAGC,GACD,eAAe,MAAMC;IASnBC,YAAYC,WAAoB,CAAE;QAChC,IAAI,CAACC,gBAAgB,GAAG,IAAIC;QAC5B,4EAA4E;QAC5E,qEAAqE;QACrEC,OAAOC,MAAM,CAAC,IAAI,EAAE;YAAEJ;QAAY;IACpC;IAEOK,IACLC,GAAkB,EAClBC,iBAAoC,EACpCC,OAIC,EACmC;YA0ClC;QAzCF,4DAA4D;QAC5D,MAAMC,qBAAqBH,MACvB,CAAC,EAAEA,IAAI,CAAC,EAAEE,QAAQE,oBAAoB,GAAG,MAAM,IAAI,CAAC,GACpD;QAEJ,MAAMC,kBAAkBF,qBACpB,IAAI,CAACR,gBAAgB,CAACI,GAAG,CAACI,sBAC1B;QACJ,IAAIE,iBAAiB;YACnB,OAAOA;QACT;QAEA,MAAM,EACJC,OAAO,EACPC,SAASC,QAAQ,EACjBC,QAAQC,QAAQ,EACjB,GAAG,IAAInB;QACR,IAAIY,oBAAoB;YACtB,IAAI,CAACR,gBAAgB,CAACgB,GAAG,CAACR,oBAAoBG;QAChD;QAEA,IAAIM,WAAW;QACf,MAAML,UAAU,CAACM;YACf,IAAIV,oBAAoB;gBACtB,wDAAwD;gBACxD,IAAI,CAACR,gBAAgB,CAACgB,GAAG,CACvBR,oBACAW,QAAQP,OAAO,CAACM;YAEpB;YACA,IAAI,CAACD,UAAU;gBACbA,WAAW;gBACXJ,SAASK;YACX;QACF;QAEA,sDAAsD;QACtD,yDAAyD;QACzD,IACEV,sBACA,IAAI,CAACT,WAAW,IAChB,EAAA,0BAAA,IAAI,CAACqB,iBAAiB,qBAAtB,wBAAwBf,GAAG,MAAKG,sBAChC,IAAI,CAACY,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;YACAX,QAAQ,IAAI,CAACQ,iBAAiB,CAACI,KAAK;YACpC,IAAI,CAACxB,gBAAgB,CAACyB,MAAM,CAACjB;YAC7B,OAAOG;QACT;QAKE,CAAA;YACA,IAAI;gBACF,MAAMO,aAAa,MAAMZ,kBAAkBW;gBAC3C,MAAMS,eACJR,eAAe,OACX,OACA;oBACE,GAAGA,UAAU;oBACbS,QAAQ;gBACV;gBAEN,8DAA8D;gBAC9D,IAAI,CAACpB,QAAQE,oBAAoB,EAAE;oBACjCG,QAAQc;gBACV;gBAEA,IAAIrB,OAAOa,cAAc,OAAOA,WAAWU,UAAU,KAAK,aAAa;oBACrE,IAAI,CAACR,iBAAiB,GAAG;wBACvBf,KAAKG,sBAAsBH;wBAC3BmB,OAAON;wBACPG,WAAWC,KAAKC,GAAG,KAAK;oBAC1B;gBACF,OAAO;oBACL,IAAI,CAACH,iBAAiB,GAAGS;gBAC3B;gBAEA,IAAItB,QAAQE,oBAAoB,EAAE;oBAChCG,QAAQc;gBACV;YACF,EAAE,OAAOI,KAAK;gBACZ,0DAA0D;gBAC1D,4DAA4D;gBAC5D,IAAIb,UAAU;oBACZc,QAAQC,KAAK,CAACF;gBAChB,OAAO;oBACLf,SAASe;gBACX;YACF,SAAU;gBACR,IAAItB,oBAAoB;oBACtB,IAAI,CAACR,gBAAgB,CAACyB,MAAM,CAACjB;gBAC/B;YACF;QACF,CAAA;QACA,OAAOG;IACT;AACF"}