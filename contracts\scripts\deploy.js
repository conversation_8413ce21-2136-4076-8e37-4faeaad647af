const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🚀 Starting deployment to Nexus network...");
  console.log("Network:", hre.network.name);
  console.log("Chain ID:", hre.network.config.chainId);

  // Get the deployer account
  const [deployer] = await hre.ethers.getSigners();
  const deployerAddress = await deployer.getAddress();
  console.log("Deploying contracts with account:", deployerAddress);

  // Check deployer balance
  const balance = await hre.ethers.provider.getBalance(deployerAddress);
  console.log("Account balance:", hre.ethers.formatEther(balance), "NEX");

  if (balance === 0n) {
    console.error("❌ Deployer account has no NEX tokens!");
    console.log("Please get NEX from the faucet: https://faucet.nexus.xyz");
    process.exit(1);
  }

  // Define initial supply (1 million tokens with 18 decimals)
  const initialSupply = hre.ethers.parseEther("1000000");

  console.log("\n📄 Deploying TokenA...");
  const TokenA = await hre.ethers.getContractFactory("TokenA");
  const tokenA = await TokenA.deploy(initialSupply);
  await tokenA.waitForDeployment();
  const tokenAAddress = await tokenA.getAddress();
  console.log("✅ TokenA deployed to:", tokenAAddress);

  console.log("\n📄 Deploying TokenB...");
  const TokenB = await hre.ethers.getContractFactory("TokenB");
  const tokenB = await TokenB.deploy(initialSupply);
  await tokenB.waitForDeployment();
  const tokenBAddress = await tokenB.getAddress();
  console.log("✅ TokenB deployed to:", tokenBAddress);

  console.log("\n📄 Deploying TokenSwap...");
  const TokenSwap = await hre.ethers.getContractFactory("TokenSwap");
  const tokenSwap = await TokenSwap.deploy(tokenAAddress, tokenBAddress);
  await tokenSwap.waitForDeployment();
  const tokenSwapAddress = await tokenSwap.getAddress();
  console.log("✅ TokenSwap deployed to:", tokenSwapAddress);

  console.log("\n📄 Deploying SimpleLiquidityPool...");
  const SimpleLiquidityPool = await hre.ethers.getContractFactory("SimpleLiquidityPool");
  const liquidityPool = await SimpleLiquidityPool.deploy(tokenAAddress, tokenBAddress);
  await liquidityPool.waitForDeployment();
  const liquidityPoolAddress = await liquidityPool.getAddress();
  console.log("✅ SimpleLiquidityPool deployed to:", liquidityPoolAddress);

  console.log("\n📄 Deploying SimpleStaking...");
  const SimpleStaking = await hre.ethers.getContractFactory("SimpleStaking");
  const staking = await SimpleStaking.deploy(liquidityPoolAddress, tokenBAddress);
  await staking.waitForDeployment();
  const stakingAddress = await staking.getAddress();
  console.log("✅ SimpleStaking deployed to:", stakingAddress);



  // Setup initial liquidity and rewards
  console.log("\n💧 Setting up initial liquidity and rewards...");

  // Transfer TokenB to swap contract for basic swapping
  const swapLiquidityAmount = hre.ethers.parseEther("500000"); // 500k tokens
  const swapTransferTx = await tokenB.transfer(tokenSwapAddress, swapLiquidityAmount);
  await swapTransferTx.wait();
  console.log("✅ Transferred", hre.ethers.formatEther(swapLiquidityAmount), "TokenB to TokenSwap");

  // Transfer TokenB to staking contract for rewards
  const rewardAmount = hre.ethers.parseEther("100000"); // 100k tokens for rewards
  const rewardTransferTx = await tokenB.transfer(stakingAddress, rewardAmount);
  await rewardTransferTx.wait();
  console.log("✅ Transferred", hre.ethers.formatEther(rewardAmount), "TokenB to Staking for rewards");

  // Save deployment addresses
  const addresses = {
    network: hre.network.name,
    chainId: hre.network.config.chainId,
    deployer: deployerAddress,
    TokenA: tokenAAddress,
    TokenB: tokenBAddress,
    TokenSwap: tokenSwapAddress,
    LiquidityPool: liquidityPoolAddress,
    Staking: stakingAddress,
    deploymentBlock: await hre.ethers.provider.getBlockNumber(),
    timestamp: new Date().toISOString()
  };

  // Save to contracts directory
  const contractsFilePath = path.join(__dirname, "..", "deployedAddresses.json");
  fs.writeFileSync(contractsFilePath, JSON.stringify(addresses, null, 2));
  console.log("📁 Contract addresses saved to:", contractsFilePath);

  // Save to lib directory for frontend
  const libFilePath = path.join(__dirname, "..", "..", "lib", "deployedAddresses.json");
  fs.writeFileSync(libFilePath, JSON.stringify(addresses, null, 2));
  console.log("📁 Contract addresses saved to:", libFilePath);

  // Save to frontend public directory
  const publicFilePath = path.join(__dirname, "..", "..", "frontend", "public", "deployedAddresses.json");
  fs.writeFileSync(publicFilePath, JSON.stringify(addresses, null, 2));
  console.log("📁 Contract addresses saved to:", publicFilePath);

  console.log("\n🎉 Deployment completed successfully!");
  console.log("\n📋 Contract Summary:");
  console.log("├── TokenA:", tokenAAddress);
  console.log("├── TokenB:", tokenBAddress);
  console.log("├── TokenSwap:", tokenSwapAddress);
  console.log("├── LiquidityPool:", liquidityPoolAddress);
  console.log("└── Staking:", stakingAddress);

  console.log("\n🔗 View on Nexus Explorer:");
  console.log("├── TokenA: https://explorer.nexus.xyz/address/" + tokenAAddress);
  console.log("├── TokenB: https://explorer.nexus.xyz/address/" + tokenBAddress);
  console.log("├── TokenSwap: https://explorer.nexus.xyz/address/" + tokenSwapAddress);
  console.log("├── LiquidityPool: https://explorer.nexus.xyz/address/" + liquidityPoolAddress);
  console.log("└── Staking: https://explorer.nexus.xyz/address/" + stakingAddress);

  console.log("\n📝 DeFi Flow for Maximum Transactions:");
  console.log("1. Swap TokenA ⇄ TokenB (basic trading)");
  console.log("2. Add Liquidity → Get LP Tokens");
  console.log("3. Stake LP Tokens → Earn Rewards");
  console.log("4. Claim Rewards → More TokenB");
  console.log("5. Repeat for maximum Nexus testnet interaction!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exitCode = 1;
  });
