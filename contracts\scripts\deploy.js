const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🚀 Starting deployment to Nexus network...");
  console.log("Network:", hre.network.name);
  console.log("Chain ID:", hre.network.config.chainId);

  // Get the deployer account
  const [deployer] = await hre.ethers.getSigners();
  const deployerAddress = await deployer.getAddress();
  console.log("Deploying contracts with account:", deployerAddress);

  // Check deployer balance
  const balance = await hre.ethers.provider.getBalance(deployerAddress);
  console.log("Account balance:", hre.ethers.formatEther(balance), "NEX");

  if (balance === 0n) {
    console.error("❌ Deployer account has no NEX tokens!");
    console.log("Please get NEX from the faucet: https://faucet.nexus.xyz");
    process.exit(1);
  }

  // Enhanced tokens use built-in INITIAL_SUPPLY constants

  console.log("\n📄 Deploying Enhanced TokenA...");
  const TokenA = await hre.ethers.getContractFactory("TokenA");
  const tokenA = await TokenA.deploy(); // No constructor args - uses INITIAL_SUPPLY constant
  await tokenA.waitForDeployment();
  const tokenAAddress = await tokenA.getAddress();
  console.log("✅ Enhanced TokenA deployed to:", tokenAAddress);

  console.log("\n📄 Deploying Enhanced TokenB...");
  const TokenB = await hre.ethers.getContractFactory("TokenB");
  const tokenB = await TokenB.deploy(); // No constructor args - uses INITIAL_SUPPLY constant
  await tokenB.waitForDeployment();
  const tokenBAddress = await tokenB.getAddress();
  console.log("✅ Enhanced TokenB deployed to:", tokenBAddress);

  console.log("\n📄 Deploying TokenSwap...");
  const TokenSwap = await hre.ethers.getContractFactory("TokenSwap");
  const tokenSwap = await TokenSwap.deploy(tokenAAddress, tokenBAddress);
  await tokenSwap.waitForDeployment();
  const tokenSwapAddress = await tokenSwap.getAddress();
  console.log("✅ TokenSwap deployed to:", tokenSwapAddress);

  console.log("\n📄 Deploying NexusLiquidityPool...");
  const NexusLiquidityPool = await hre.ethers.getContractFactory("NexusLiquidityPool");
  const liquidityPool = await NexusLiquidityPool.deploy(tokenAAddress, tokenBAddress, deployerAddress); // Fee recipient = deployer
  await liquidityPool.waitForDeployment();
  const liquidityPoolAddress = await liquidityPool.getAddress();
  console.log("✅ NexusLiquidityPool deployed to:", liquidityPoolAddress);

  // Deploy staking contract first, then authorize
  console.log("\n📄 Deploying NexusStaking...");
  const NexusStaking = await hre.ethers.getContractFactory("NexusStaking");
  const staking = await NexusStaking.deploy(liquidityPoolAddress, tokenBAddress);
  await staking.waitForDeployment();
  const stakingAddress = await staking.getAddress();
  console.log("✅ NexusStaking deployed to:", stakingAddress);

  // Authorize staking contract to mint rewards
  console.log("\n🔐 Authorizing staking contract to mint TokenB rewards...");
  const authorizeTx = await tokenB.addRewardMinter(stakingAddress);
  await authorizeTx.wait();
  console.log("✅ Staking contract authorized to mint rewards");

  console.log("\n📄 Deploying BatchOperations...");
  const BatchOperations = await hre.ethers.getContractFactory("BatchOperations");
  const batchOps = await BatchOperations.deploy(
    tokenAAddress,
    tokenBAddress,
    liquidityPoolAddress, // LP token is the liquidity pool contract itself
    liquidityPoolAddress,
    stakingAddress,
    deployerAddress // Fee recipient = deployer
  );
  await batchOps.waitForDeployment();
  const batchOpsAddress = await batchOps.getAddress();
  console.log("✅ BatchOperations deployed to:", batchOpsAddress);



  // Setup initial liquidity
  console.log("\n💧 Setting up initial liquidity...");

  // Transfer TokenB to swap contract for basic swapping
  const swapLiquidityAmount = hre.ethers.parseEther("500000"); // 500k tokens
  const swapTransferTx = await tokenB.transfer(tokenSwapAddress, swapLiquidityAmount);
  await swapTransferTx.wait();
  console.log("✅ Transferred", hre.ethers.formatEther(swapLiquidityAmount), "TokenB to TokenSwap");

  // Note: Staking rewards are now minted on-demand, no need to pre-fund

  // Save deployment addresses
  const addresses = {
    network: hre.network.name,
    chainId: hre.network.config.chainId,
    deployer: deployerAddress,
    TokenA: tokenAAddress,
    TokenB: tokenBAddress,
    TokenSwap: tokenSwapAddress,
    LiquidityPool: liquidityPoolAddress,
    Staking: stakingAddress,
    BatchOperations: batchOpsAddress,
    deploymentBlock: await hre.ethers.provider.getBlockNumber(),
    timestamp: new Date().toISOString(),
    features: {
      enhancedTokens: true,
      advancedLiquidityPool: true,
      tierBasedStaking: true,
      batchOperations: true,
      rewardMinting: true
    }
  };

  // Save to contracts directory
  const contractsFilePath = path.join(__dirname, "..", "deployedAddresses.json");
  fs.writeFileSync(contractsFilePath, JSON.stringify(addresses, null, 2));
  console.log("📁 Contract addresses saved to:", contractsFilePath);

  // Save to lib directory for frontend
  const libFilePath = path.join(__dirname, "..", "..", "lib", "deployedAddresses.json");
  fs.writeFileSync(libFilePath, JSON.stringify(addresses, null, 2));
  console.log("📁 Contract addresses saved to:", libFilePath);

  // Save to frontend public directory
  const publicFilePath = path.join(__dirname, "..", "..", "frontend", "public", "deployedAddresses.json");
  fs.writeFileSync(publicFilePath, JSON.stringify(addresses, null, 2));
  console.log("📁 Contract addresses saved to:", publicFilePath);

  console.log("\n🎉 Enhanced DeFi Deployment completed successfully!");
  console.log("\n📋 Contract Summary:");
  console.log("├── Enhanced TokenA:", tokenAAddress);
  console.log("├── Enhanced TokenB:", tokenBAddress);
  console.log("├── TokenSwap:", tokenSwapAddress);
  console.log("├── NexusLiquidityPool:", liquidityPoolAddress);
  console.log("├── NexusStaking:", stakingAddress);
  console.log("└── BatchOperations:", batchOpsAddress);

  console.log("\n🔗 View on Nexus Explorer:");
  console.log("├── TokenA: https://testnet3.explorer.nexus.xyz/address/" + tokenAAddress);
  console.log("├── TokenB: https://testnet3.explorer.nexus.xyz/address/" + tokenBAddress);
  console.log("├── TokenSwap: https://testnet3.explorer.nexus.xyz/address/" + tokenSwapAddress);
  console.log("├── LiquidityPool: https://testnet3.explorer.nexus.xyz/address/" + liquidityPoolAddress);
  console.log("├── Staking: https://testnet3.explorer.nexus.xyz/address/" + stakingAddress);
  console.log("└── BatchOps: https://testnet3.explorer.nexus.xyz/address/" + batchOpsAddress);

  console.log("\n🚀 Enhanced DeFi Features:");
  console.log("✅ Burnable & Pausable Tokens");
  console.log("✅ Advanced AMM with Fee Collection");
  console.log("✅ Tier-based Staking Rewards (1.2x, 1.5x, 2.0x)");
  console.log("✅ On-demand Reward Minting");
  console.log("✅ Batch Operations for Gas Optimization");
  console.log("✅ Anti-whale Mechanisms");
  console.log("✅ Emergency Pause Functionality");

  console.log("\n📝 Enhanced DeFi Flow:");
  console.log("1. Swap TokenA ⇄ TokenB (with fees)");
  console.log("2. Add Liquidity → Get LP Tokens");
  console.log("3. Stake LP Tokens → Earn Tiered Rewards");
  console.log("4. Use Batch Operations for Gas Efficiency");
  console.log("5. Claim Rewards → Minted TokenB");
  console.log("6. Compound & Repeat for Maximum Nexus Interaction!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exitCode = 1;
  });
