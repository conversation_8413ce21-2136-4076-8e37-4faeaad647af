const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🚀 Starting deployment of NXI/NXY tokens with NEX integration to Nexus network...");
  console.log("Network:", hre.network.name);
  console.log("Chain ID:", hre.network.config.chainId);

  // Get the deployer account
  const [deployer] = await hre.ethers.getSigners();
  const deployerAddress = await deployer.getAddress();
  console.log("Deploying contracts with account:", deployerAddress);

  // Check deployer balance
  const balance = await hre.ethers.provider.getBalance(deployerAddress);
  console.log("Account balance:", hre.ethers.formatEther(balance), "NEX");

  if (balance === 0n) {
    console.error("❌ Deployer account has no NEX tokens!");
    console.log("Please get NEX from the faucet: https://faucet.nexus.xyz");
    process.exit(1);
  }

  console.log("\n📄 Deploying NXI Token (Nexus Index Token)...");
  const NXI = await hre.ethers.getContractFactory("NXI");
  const nxi = await NXI.deploy();
  await nxi.waitForDeployment();
  const nxiAddress = await nxi.getAddress();
  console.log("✅ NXI Token deployed to:", nxiAddress);

  console.log("\n📄 Deploying NXY Token (Nexus Yield Token)...");
  const NXY = await hre.ethers.getContractFactory("NXY");
  const nxy = await NXY.deploy();
  await nxy.waitForDeployment();
  const nxyAddress = await nxy.getAddress();
  console.log("✅ NXY Token deployed to:", nxyAddress);

  console.log("\n📄 Deploying NexusTokenSwap...");
  const NexusTokenSwap = await hre.ethers.getContractFactory("NexusTokenSwap");
  const nexusSwap = await NexusTokenSwap.deploy(nxiAddress, nxyAddress, deployerAddress);
  await nexusSwap.waitForDeployment();
  const nexusSwapAddress = await nexusSwap.getAddress();
  console.log("✅ NexusTokenSwap deployed to:", nexusSwapAddress);

  // Setup initial configuration
  console.log("\n⚙️ Setting up initial configuration...");

  // Whitelist the swap contract on both tokens
  console.log("🔐 Whitelisting swap contract on NXI token...");
  const whitelistNXITx = await nxi.setWhitelistAddress(nexusSwapAddress, true);
  await whitelistNXITx.wait();
  console.log("✅ NexusTokenSwap whitelisted on NXI");

  console.log("🔐 Whitelisting swap contract on NXY token...");
  const whitelistNXYTx = await nxy.setWhitelistAddress(nexusSwapAddress, true);
  await whitelistNXYTx.wait();
  console.log("✅ NexusTokenSwap whitelisted on NXY");

  // Transfer tokens to swap contract for liquidity
  console.log("\n💧 Setting up initial liquidity...");

  // Transfer NXI to swap contract
  const nxiLiquidityAmount = hre.ethers.parseEther("100000"); // 100k NXI
  const nxiTransferTx = await nxi.transfer(nexusSwapAddress, nxiLiquidityAmount);
  await nxiTransferTx.wait();
  console.log("✅ Transferred", hre.ethers.formatEther(nxiLiquidityAmount), "NXI to swap contract");

  // Transfer NXY to swap contract
  const nxyLiquidityAmount = hre.ethers.parseEther("120000"); // 120k NXY (more for 1.2 rate)
  const nxyTransferTx = await nxy.transfer(nexusSwapAddress, nxyLiquidityAmount);
  await nxyTransferTx.wait();
  console.log("✅ Transferred", hre.ethers.formatEther(nxyLiquidityAmount), "NXY to swap contract");

  // Fund NXY reward pool with some NEX
  console.log("💰 Funding NXY reward pool...");
  const rewardPoolAmount = hre.ethers.parseEther("10"); // 10 NEX
  const fundRewardPoolTx = await nxy.fundNEXRewardPool({ value: rewardPoolAmount });
  await fundRewardPoolTx.wait();
  console.log("✅ Funded NXY reward pool with", hre.ethers.formatEther(rewardPoolAmount), "NEX");

  // Test NEX to token swaps
  console.log("\n🧪 Testing NEX integration...");

  // Test NEX to NXI swap
  console.log("Testing NEX to NXI swap...");
  const nexToNxiAmount = hre.ethers.parseEther("1"); // 1 NEX
  const nxiSwapTx = await nxi.swapNEXForNXI({ value: nexToNxiAmount });
  await nxiSwapTx.wait();
  console.log("✅ Successfully swapped", hre.ethers.formatEther(nexToNxiAmount), "NEX for NXI");

  // Test NEX to NXY swap
  console.log("Testing NEX to NXY swap...");
  const nexToNxyAmount = hre.ethers.parseEther("1"); // 1 NEX
  const nxySwapTx = await nxy.swapNEXForNXY({ value: nexToNxyAmount });
  await nxySwapTx.wait();
  console.log("✅ Successfully swapped", hre.ethers.formatEther(nexToNxyAmount), "NEX for NXY");

  // Get token information
  console.log("\n📊 Getting token information...");
  const nxiInfo = await nxi.getTokenInfo();
  const nxyInfo = await nxy.getTokenInfo();

  console.log("NXI Token Info:");
  console.log("  Name:", nxiInfo.tokenName);
  console.log("  Symbol:", nxiInfo.tokenSymbol);
  console.log("  Total Supply:", hre.ethers.formatEther(nxiInfo.currentSupply));
  console.log("  NEX Swap Rate:", nxiInfo.nexRate.toString(), "NXI per NEX");
  console.log("  NEX Balance:", hre.ethers.formatEther(nxiInfo.nexBalance), "NEX");

  console.log("NXY Token Info:");
  console.log("  Name:", nxyInfo.tokenName);
  console.log("  Symbol:", nxyInfo.tokenSymbol);
  console.log("  Total Supply:", hre.ethers.formatEther(nxyInfo.currentSupply));
  console.log("  NEX Swap Rate:", nxyInfo.nexRate.toString(), "NXY per NEX");
  console.log("  NEX Balance:", hre.ethers.formatEther(nxyInfo.nexBalance), "NEX");
  console.log("  Reward Pool:", hre.ethers.formatEther(nxyInfo.rewardPool), "NEX");

  // Save deployment addresses
  const addresses = {
    network: hre.network.name,
    chainId: hre.network.config.chainId,
    deployer: deployerAddress,
    NXI: nxiAddress,
    NXY: nxyAddress,
    NexusTokenSwap: nexusSwapAddress,
    deploymentBlock: await hre.ethers.provider.getBlockNumber(),
    timestamp: new Date().toISOString(),
    tokenInfo: {
      nxi: {
        name: nxiInfo.tokenName,
        symbol: nxiInfo.tokenSymbol,
        decimals: Number(nxiInfo.tokenDecimals),
        initialSupply: hre.ethers.formatEther(nxiInfo.currentSupply),
        maxSupply: hre.ethers.formatEther(nxiInfo.maxSupply),
        nexSwapRate: nxiInfo.nexRate.toString()
      },
      nxy: {
        name: nxyInfo.tokenName,
        symbol: nxyInfo.tokenSymbol,
        decimals: Number(nxyInfo.tokenDecimals),
        initialSupply: hre.ethers.formatEther(nxyInfo.currentSupply),
        maxSupply: hre.ethers.formatEther(nxyInfo.maxSupply),
        nexSwapRate: nxyInfo.nexRate.toString()
      }
    },
    features: {
      nexIntegration: true,
      antiWhaleProtection: true,
      pausableTokens: true,
      rewardMechanism: true,
      swapFees: true,
      emergencyFunctions: true
    }
  };

  // Save to contracts directory
  const contractsFilePath = path.join(__dirname, "..", "nexusDeployedAddresses.json");
  fs.writeFileSync(contractsFilePath, JSON.stringify(addresses, null, 2));
  console.log("📁 Contract addresses saved to:", contractsFilePath);

  // Save to lib directory for frontend
  const libFilePath = path.join(__dirname, "..", "..", "lib", "nexusDeployedAddresses.json");
  fs.writeFileSync(libFilePath, JSON.stringify(addresses, null, 2));
  console.log("📁 Contract addresses saved to:", libFilePath);

  // Save to frontend public directory
  const publicFilePath = path.join(__dirname, "..", "..", "frontend", "public", "nexusDeployedAddresses.json");
  fs.writeFileSync(publicFilePath, JSON.stringify(addresses, null, 2));
  console.log("📁 Contract addresses saved to:", publicFilePath);

  console.log("\n🎉 Nexus NXI/NXY Deployment completed successfully!");
  console.log("\n📋 Contract Summary:");
  console.log("├── NXI Token (Nexus Index Token):", nxiAddress);
  console.log("├── NXY Token (Nexus Yield Token):", nxyAddress);
  console.log("└── NexusTokenSwap:", nexusSwapAddress);

  console.log("\n🔗 View on Nexus Explorer:");
  console.log("├── NXI: https://testnet3.explorer.nexus.xyz/address/" + nxiAddress);
  console.log("├── NXY: https://testnet3.explorer.nexus.xyz/address/" + nxyAddress);
  console.log("└── Swap: https://testnet3.explorer.nexus.xyz/address/" + nexusSwapAddress);

  console.log("\n🚀 Nexus Integration Features:");
  console.log("✅ NEX ⇄ NXI Direct Swaps");
  console.log("✅ NEX ⇄ NXY Direct Swaps with Reward Pool");
  console.log("✅ NXI ⇄ NXY Token Swaps with Fees");
  console.log("✅ Anti-whale Transfer Limits");
  console.log("✅ Pausable Emergency Functions");
  console.log("✅ Whitelisting for DeFi Contracts");
  console.log("✅ Reward Distribution Mechanism");

  console.log("\n📝 Swap Rates:");
  console.log("├── 1 NEX = 100 NXI (adjustable)");
  console.log("├── 1 NEX = 150 NXY (adjustable)");
  console.log("├── 1 NXI = 1.2 NXY (0.3% fee)");
  console.log("└── 1 NXY = 0.8 NXI (0.3% fee)");

  console.log("\n🔄 Usage Examples:");
  console.log("1. Send NEX directly to NXI contract → Get NXI tokens");
  console.log("2. Send NEX directly to NXY contract → Get NXY tokens + contribute to reward pool");
  console.log("3. Use NexusTokenSwap to swap NXI ⇄ NXY");
  console.log("4. Hold NXY tokens → Claim proportional NEX rewards");
  console.log("5. All swaps respect anti-whale limits and pausable states");

  console.log("\n🎯 Ready for Nexus DeFi Integration!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exitCode = 1;
  });