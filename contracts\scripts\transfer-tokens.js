const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  // Get recipient address from environment variable or hardcode for testing
  const recipientAddress = process.env.RECIPIENT_ADDRESS || "0xbec5df51eb4a39e5c0eac282b93c7e9608a7b0fa";

  if (!recipientAddress) {
    console.error("❌ Please provide recipient address");
    console.log("Set RECIPIENT_ADDRESS environment variable or edit the script");
    process.exit(1);
  }

  console.log("🚀 Starting token transfer...");
  console.log("Network:", hre.network.name);
  console.log("Recipient:", recipientAddress);

  // Load deployed addresses
  const addressesPath = path.join(__dirname, "..", "deployedAddresses.json");
  
  if (!fs.existsSync(addressesPath)) {
    console.error("❌ deployedAddresses.json not found!");
    console.log("Please run deployment first: npm run deploy");
    process.exit(1);
  }

  const addresses = JSON.parse(fs.readFileSync(addressesPath, "utf8"));
  console.log("📄 Loaded contract addresses");

  // Get the deployer account (should be the one with tokens)
  const [deployer] = await hre.ethers.getSigners();
  const deployerAddress = await deployer.getAddress();
  console.log("Deployer address:", deployerAddress);

  // Check if deployer matches the one in addresses file
  if (deployerAddress.toLowerCase() !== addresses.deployer.toLowerCase()) {
    console.warn("⚠️  Warning: Current deployer doesn't match the one in addresses file");
    console.log("Current:", deployerAddress);
    console.log("Expected:", addresses.deployer);
  }

  try {
    // Get TokenA contract
    const tokenA = await hre.ethers.getContractAt("TokenA", addresses.TokenA);
    
    // Check deployer balance
    const deployerBalance = await tokenA.balanceOf(deployerAddress);
    console.log("Deployer TokenA balance:", hre.ethers.formatEther(deployerBalance));

    if (deployerBalance === 0n) {
      console.error("❌ Deployer has no TokenA tokens!");
      process.exit(1);
    }

    // Transfer amount (1000 tokens)
    const transferAmount = hre.ethers.parseEther("1000");
    
    if (deployerBalance < transferAmount) {
      console.error("❌ Insufficient balance for transfer");
      console.log("Required:", hre.ethers.formatEther(transferAmount));
      console.log("Available:", hre.ethers.formatEther(deployerBalance));
      process.exit(1);
    }

    console.log("\n💸 Transferring tokens...");
    console.log("Amount:", hre.ethers.formatEther(transferAmount), "TokenA");
    console.log("From:", deployerAddress);
    console.log("To:", recipientAddress);

    // Execute transfer
    const transferTx = await tokenA.transfer(recipientAddress, transferAmount);
    console.log("Transaction hash:", transferTx.hash);
    
    // Wait for confirmation
    console.log("⏳ Waiting for confirmation...");
    await transferTx.wait();

    // Check final balances
    const recipientBalance = await tokenA.balanceOf(recipientAddress);
    const newDeployerBalance = await tokenA.balanceOf(deployerAddress);

    console.log("\n✅ Transfer completed successfully!");
    console.log("📊 Final balances:");
    console.log("├── Deployer:", hre.ethers.formatEther(newDeployerBalance), "TokenA");
    console.log("└── Recipient:", hre.ethers.formatEther(recipientBalance), "TokenA");

    console.log("\n🔗 View transaction on Nexus Explorer:");
    console.log("https://explorer.nexus.xyz/tx/" + transferTx.hash);

  } catch (error) {
    console.error("❌ Transfer failed:", error.message);
    process.exitCode = 1;
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exitCode = 1;
  });
