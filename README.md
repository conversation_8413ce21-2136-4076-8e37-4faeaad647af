# Nexus Token Swap DeFi Application

## Overview 
This project demonstrates fundamental decentralized finance (DeFi) operations on Nexus. Users can:
- Connect their crypto wallets
- Perform token swaps (Token A ⇄ Token B) at a fixed 1:1 ratio
- Understand the workflow of token approvals, transactions, and balance management

This example serves as a starting point for developers interested in creating more sophisticated financial applications.

## Project Context
Decentralized exchanges (DEXs) allow direct peer-to-peer cryptocurrency transactions without intermediaries, leveraging smart contracts for secure and trustless swaps. This Nexus token swap example illustrates blockchain concepts such as smart contract deployment, ERC20 token standards, wallet integrations, and real-time transaction management.

## Project Structure
```
nexus-swap-example/
├── contracts/               # Hardhat project for deploying TokenA, TokenB, and TokenSwap
├── frontend/               # Next.js frontend application
├── lib/                    # Shared frontend utilities (e.g., deployed contract addresses)
├── scripts/                # Hardhat scripts
├── test/                   # Contract tests
├── .env                    # Environment variables for local setup
├── hardhat.config.js       # Hardhat configuration file
├── README.md               # Project documentation
```

## Quick Start

### 1. Install Dependencies
```bash
# Install contract dependencies
cd contracts
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

### 2. Configure Environment
```bash
# Copy environment template
cp .env.example .env
# Add your private key to .env file
```

### 3. Deploy Contracts
```bash
cd contracts
npx hardhat run scripts/deploy.js --network nexus
```

### 4. Run Frontend
```bash
cd frontend
npm run dev
```

## Key Features

### Smart Contract Architecture
- Utilizes robust ERC20 token standards provided by OpenZeppelin
- Straightforward 1:1 swap logic ensuring simplicity and transparency
- Secure approval and transaction processes

### Frontend Implementation
- Modern UI leveraging Next.js and Tailwind CSS
- Dynamic real-time token balance updates
- User-friendly wallet connection and transaction status indicators
- Comprehensive error handling and notifications for a smooth user experience

### Demonstrated DeFi Concepts
- Token approvals and transaction flow
- Multi-step DeFi transactions (approve and swap)
- Real-time balance and contract interaction management
- Monitoring blockchain events for accurate transaction feedback

## Network Configuration
- **Network**: Nexus Testnet III
- **RPC URL**: https://testnet3.rpc.nexus.xyz
- **Chain ID**: 3940
- **Explorer**: https://explorer.nexus.xyz

## Security Notes
- Never share your private key
- Never commit private keys to version control
- Keep private keys secure and backed up
- Use test accounts for development

## Resources
- [Nexus Documentation](https://docs.nexus.xyz)
- [Nexus Explorer](https://explorer.nexus.xyz)
- [Nexus Faucet](https://faucet.nexus.xyz)
