{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "names": ["callServer", "ACTION", "NEXT_ROUTER_STATE_TREE", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "createFromFetch", "encodeReply", "process", "env", "NEXT_RUNTIME", "require", "addBasePath", "createHrefFromUrl", "handleExternalUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "CacheStates", "handleMutable", "fillLazyItemsTillLeafWithHead", "createEmptyCacheNode", "extractPathFromFlightRouterState", "fetchServerAction", "state", "actionId", "actionArgs", "body", "newNextUrl", "tree", "includeNextUrl", "nextUrl", "res", "fetch", "method", "headers", "Accept", "encodeURIComponent", "JSON", "stringify", "__NEXT_ACTIONS_DEPLOYMENT_ID", "NEXT_DEPLOYMENT_ID", "location", "get", "revalidatedParts", "revalidatedHeader", "parse", "paths", "tag", "cookie", "e", "redirectLocation", "URL", "canonicalUrl", "window", "href", "undefined", "isFlightResponse", "response", "Promise", "resolve", "actionFlightData", "actionResult", "serverActionReducer", "action", "reject", "mutable", "currentTree", "preserveCustomHistoryState", "inFlightServerAction", "then", "flightData", "pushRef", "pendingPush", "actionResultResolved", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "Error", "cacheNodeSeedData", "head", "slice", "subTreeData", "cache", "status", "READY", "prefetchCache", "Map", "patchedTree", "newHref", "reason"], "mappings": "AAKA,SAASA,UAAU,QAAQ,2BAA0B;AACrD,SACEC,MAAM,EACNC,sBAAsB,EACtBC,QAAQ,EACRC,uBAAuB,QAClB,2BAA0B;AACjC,gEAAgE;AAChE,oEAAoE;AACpE,gEAAgE;AAChE,gEAAgE;AAChE,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAE,GACpC,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AASd,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SACEC,WAAW,QAEN,2DAA0D;AACjE,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,6BAA6B,QAAQ,yCAAwC;AACtF,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,gCAAgC,QAAQ,0BAAyB;AAa1E,eAAeC,kBACbC,KAA2B,EAC3B,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,OAAO,MAAMnB,YAAYkB;IAE/B,MAAME,aAAaN,iCAAiCE,MAAMK,IAAI;IAC9D,sFAAsF;IACtF,uGAAuG;IACvG,wFAAwF;IACxF,iFAAiF;IACjF,MAAMC,iBAAiBN,MAAMO,OAAO,IAAIP,MAAMO,OAAO,KAAKH;IAE1D,MAAMI,MAAM,MAAMC,MAAM,IAAI;QAC1BC,QAAQ;QACRC,SAAS;YACPC,QAAQ9B;YACR,CAACH,OAAO,EAAEsB;YACV,CAACrB,uBAAuB,EAAEiC,mBAAmBC,KAAKC,SAAS,CAACf,MAAMK,IAAI;YACtE,GAAIpB,QAAQC,GAAG,CAAC8B,4BAA4B,IAC5C/B,QAAQC,GAAG,CAAC+B,kBAAkB,GAC1B;gBACE,mBAAmBhC,QAAQC,GAAG,CAAC+B,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAIX,iBACA;gBACE,CAACzB,SAAS,EAAEmB,MAAMO,OAAO;YAC3B,IACA,CAAC,CAAC;QACR;QACAJ;IACF;IAEA,MAAMe,WAAWV,IAAIG,OAAO,CAACQ,GAAG,CAAC;IACjC,IAAIC;IACJ,IAAI;QACF,MAAMC,oBAAoBP,KAAKQ,KAAK,CAClCd,IAAIG,OAAO,CAACQ,GAAG,CAAC,2BAA2B;QAE7CC,mBAAmB;YACjBG,OAAOF,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCG,KAAK,CAAC,CAACH,iBAAiB,CAAC,EAAE;YAC3BI,QAAQJ,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOK,GAAG;QACVN,mBAAmB;YACjBG,OAAO,EAAE;YACTC,KAAK;YACLC,QAAQ;QACV;IACF;IAEA,MAAME,mBAAmBT,WACrB,IAAIU,IACFvC,YAAY6B,WACZ,sFAAsF;IACtF,IAAIU,IAAI5B,MAAM6B,YAAY,EAAEC,OAAOZ,QAAQ,CAACa,IAAI,KAElDC;IAEJ,IAAIC,mBACFzB,IAAIG,OAAO,CAACQ,GAAG,CAAC,oBAAoBrC;IAEtC,IAAImD,kBAAkB;QACpB,MAAMC,WAAiC,MAAMnD,gBAC3CoD,QAAQC,OAAO,CAAC5B,MAChB;YACE9B;QACF;QAGF,IAAIwC,UAAU;YACZ,qEAAqE;YACrE,MAAM,GAAGmB,iBAAiB,GAAG,AAACH,mBAAAA,WAAoB,EAAE;YACpD,OAAO;gBACLG,kBAAkBA;gBAClBV;gBACAP;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,CAACkB,cAAc,GAAGD,iBAAiB,CAAC,GAAG,AAACH,mBAAAA,WAAoB,EAAE;QACpE,OAAO;YACLI;YACAD;YACAV;YACAP;QACF;IACF;IACA,OAAO;QACLO;QACAP;IACF;AACF;AAEA;;;CAGC,GACD,OAAO,SAASmB,oBACdvC,KAA2B,EAC3BwC,MAA0B;IAE1B,MAAM,EAAEJ,OAAO,EAAEK,MAAM,EAAE,GAAGD;IAC5B,MAAME,UAA+B,CAAC;IACtC,MAAMX,OAAO/B,MAAM6B,YAAY;IAE/B,IAAIc,cAAc3C,MAAMK,IAAI;IAE5BqC,QAAQE,0BAA0B,GAAG;IACrCF,QAAQG,oBAAoB,GAAG9C,kBAAkBC,OAAOwC;IAExD,gDAAgD;IAEhD,OAAOE,QAAQG,oBAAoB,CAACC,IAAI,CACtC;YAAC,EAAER,YAAY,EAAED,kBAAkBU,UAAU,EAAEpB,gBAAgB,EAAE;QAC/D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIA,kBAAkB;YACpB3B,MAAMgD,OAAO,CAACC,WAAW,GAAG;YAC5BP,QAAQO,WAAW,GAAG;QACxB;QAEA,IAAI,CAACF,YAAY;YACf,IAAI,CAACL,QAAQQ,oBAAoB,EAAE;gBACjCd,QAAQE;gBACRI,QAAQQ,oBAAoB,GAAG;YACjC;YAEA,2EAA2E;YAC3E,IAAIvB,kBAAkB;gBACpB,OAAOpC,kBACLS,OACA0C,SACAf,iBAAiBI,IAAI,EACrB/B,MAAMgD,OAAO,CAACC,WAAW;YAE7B;YACA,OAAOjD;QACT;QAEA,IAAI,OAAO+C,eAAe,UAAU;YAClC,4DAA4D;YAC5D,OAAOxD,kBACLS,OACA0C,SACAK,YACA/C,MAAMgD,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DP,QAAQG,oBAAoB,GAAG;QAE/B,KAAK,MAAMM,kBAAkBJ,WAAY;YACvC,oFAAoF;YACpF,IAAII,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOtD;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAACuD,UAAU,GAAGJ;YACpB,MAAMK,UAAUhE,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJmD,aACAY;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIC,MAAM;YAClB;YAEA,IAAIhE,4BAA4BkD,aAAaa,UAAU;gBACrD,OAAOjE,kBACLS,OACA0C,SACAX,MACA/B,MAAMgD,OAAO,CAACC,WAAW;YAE7B;YAEA,0DAA0D;YAC1D,MAAM,CAACS,mBAAmBC,KAAK,GAAGR,eAAeS,KAAK,CAAC,CAAC;YACxD,MAAMC,cACJH,sBAAsB,OAAOA,iBAAiB,CAAC,EAAE,GAAG;YAEtD,8FAA8F;YAC9F,IAAIG,gBAAgB,MAAM;gBACxB,MAAMC,QAAmBjE;gBACzBiE,MAAMC,MAAM,GAAGrE,YAAYsE,KAAK;gBAChCF,MAAMD,WAAW,GAAGA;gBACpBjE,8BACEkE,OACA,4FAA4F;gBAC5F9B,WACAuB,WACAG,mBACAC;gBAEFjB,QAAQoB,KAAK,GAAGA;gBAChBpB,QAAQuB,aAAa,GAAG,IAAIC;YAC9B;YAEAxB,QAAQyB,WAAW,GAAGX;YACtBd,QAAQb,YAAY,GAAGE;YAEvBY,cAAca;QAChB;QAEA,IAAI7B,kBAAkB;YACpB,MAAMyC,UAAU9E,kBAAkBqC,kBAAkB;YACpDe,QAAQb,YAAY,GAAGuC;QACzB;QAEA,IAAI,CAAC1B,QAAQQ,oBAAoB,EAAE;YACjCd,QAAQE;YACRI,QAAQQ,oBAAoB,GAAG;QACjC;QACA,OAAOvD,cAAcK,OAAO0C;IAC9B,GACA,CAAChB;QACC,IAAIA,EAAEqC,MAAM,KAAK,YAAY;YAC3B,IAAI,CAACrB,QAAQQ,oBAAoB,EAAE;gBACjCT,OAAOf,EAAE2C,MAAM;gBACf3B,QAAQQ,oBAAoB,GAAG;YACjC;YAEA,mHAAmH;YACnH,OAAOlD;QACT;QAEA,MAAM0B;IACR;AAEJ"}