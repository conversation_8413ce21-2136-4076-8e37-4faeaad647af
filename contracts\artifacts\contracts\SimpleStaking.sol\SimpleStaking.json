{"_format": "hh-sol-artifact-1", "contractName": "SimpleStaking", "sourceName": "contracts/SimpleStaking.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_stakingToken", "type": "address"}, {"internalType": "address", "name": "_rewardToken", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}], "name": "RewardPaid", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newRate", "type": "uint256"}], "name": "RewardRateUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Staked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Withdrawn", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "addRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "balances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimReward", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "compound", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "earned", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "exit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getStakingInfo", "outputs": [{"internalType": "uint256", "name": "stakedBalance", "type": "uint256"}, {"internalType": "uint256", "name": "earnedRewards", "type": "uint256"}, {"internalType": "uint256", "name": "stakingTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "totalPoolStaked", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "times", "type": "uint256"}], "name": "multiClaim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "name": "multiStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "name": "multiWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rewardPerToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardPerTokenStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "rewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_rewardRate", "type": "uint256"}], "name": "setRewardRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "stakingTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakingToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalStaked", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userRewardPerTokenPaid", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106101a85760003560e01c8063a694fc3a116100f9578063df136d6511610097578063f2fde38b11610071578063f2fde38b14610382578063f63257ef14610395578063f69e2046146103a8578063f7c618c1146103b057600080fd5b8063df136d651461035e578063e6fecdde14610367578063e9fad8ee1461037a57600080fd5b8063beceed39116100d3578063beceed3914610332578063c8f33c9114610345578063cd3daf9d1461034e578063db2e21bc1461035657600080fd5b8063a694fc3a146102e4578063aa4704f3146102f7578063b88a802f1461032a57600080fd5b8063715018a611610166578063817b1cd211610140578063817b1cd2146102975780638b876347146102a05780638da5cb5b146102c05780639e447fc6146102d157600080fd5b8063715018a61461025b57806372f702f3146102635780637b0a47ee1461028e57600080fd5b80628cc262146101ad5780630700037d146101d357806327e235e3146101f35780632e1a7d4d1461021357806353fdeb8114610228578063583d42fd1461023b575b600080fd5b6101c06101bb366004611588565b6103c3565b6040519081526020015b60405180910390f35b6101c06101e1366004611588565b60096020526000908152604090205481565b6101c0610201366004611588565b600a6020526000908152604090205481565b6102266102213660046115b8565b610440565b005b6102266102363660046115b8565b61064b565b6101c0610249366004611588565b600b6020526000908152604090205481565b61022661082e565b600254610276906001600160a01b031681565b6040516001600160a01b0390911681526020016101ca565b6101c060045481565b6101c060075481565b6101c06102ae366004611588565b60086020526000908152604090205481565b6001546001600160a01b0316610276565b6102266102df3660046115b8565b610842565b6102266102f23660046115b8565b6108db565b61030a610305366004611588565b610a8f565b6040805194855260208501939093529183015260608201526080016101ca565b610226610ae2565b6102266103403660046115b8565b610c64565b6101c060055481565b6101c0610d04565b610226610d62565b6101c060065481565b6102266103753660046115d1565b610e30565b6102266110cf565b610226610390366004611588565b6110f0565b6102266103a33660046115d1565b61112b565b610226611379565b600354610276906001600160a01b031681565b6001600160a01b0381166000908152600960209081526040808320546008909252822054670de0b6b3a7640000906103f9610d04565b610403919061165c565b6001600160a01b0385166000908152600a6020526040902054610426919061166f565b6104309190611686565b61043a91906116a8565b92915050565b6104486114df565b33610451610d04565b600655426005556001600160a01b0381161561049c57610470816103c3565b6001600160a01b0382166000908152600960209081526040808320939093556006546008909152919020555b600082116104e55760405162461bcd60e51b8152602060048201526011602482015270043616e6e6f74207769746864726177203607c1b60448201526064015b60405180910390fd5b336000908152600a602052604090205482111561053b5760405162461bcd60e51b8152602060048201526014602482015273496e73756666696369656e742062616c616e636560601b60448201526064016104dc565b816007600082825461054d919061165c565b9091555050336000908152600a60205260408120805484929061057190849061165c565b909155505060025460405163a9059cbb60e01b8152336004820152602481018490526001600160a01b039091169063a9059cbb906044016020604051808303816000875af11580156105c7573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906105eb91906116bb565b6106075760405162461bcd60e51b81526004016104dc906116dd565b60405182815233907f7084f5476618d8e60b11ef0d7d3f06914655adb8793e28ff7f018d4c76d505d5906020015b60405180910390a2506106486001600055565b50565b6106536114df565b3361065c610d04565b600655426005556001600160a01b038116156106a75761067b816103c3565b6001600160a01b0382166000908152600960209081526040808320939093556006546008909152919020555b33600090815260096020526040902054806106fa5760405162461bcd60e51b81526020600482015260136024820152724e6f207265776172647320746f20636c61696d60681b60448201526064016104dc565b3360008181526009602052604080822091909155600354905163a9059cbb60e01b81526004810192909252602482018390526001600160a01b03169063a9059cbb906044016020604051808303816000875af115801561075e573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061078291906116bb565b6107c75760405162461bcd60e51b815260206004820152601660248201527514995dd85c99081d1c985b9cd9995c8819985a5b195960521b60448201526064016104dc565b60005b8381101561082157337fe2403640ba68fed3a2f88b7557551d1993f84b99bb10ff833f0cf8db0c5e04866107fe8685611686565b60405190815260200160405180910390a28061081981611706565b9150506107ca565b5050506106486001600055565b610836611509565b6108406000611536565b565b61084a611509565b6000610854610d04565b600655426005556001600160a01b0381161561089f57610873816103c3565b6001600160a01b0382166000908152600960209081526040808320939093556006546008909152919020555b60048290556040518281527f41d466ebd06fb97e7786086ac8b69b7eb7da798592036251291d34e9791cde019060200160405180910390a15050565b6108e36114df565b336108ec610d04565b600655426005556001600160a01b038116156109375761090b816103c3565b6001600160a01b0382166000908152600960209081526040808320939093556006546008909152919020555b600082116109785760405162461bcd60e51b815260206004820152600e60248201526d043616e6e6f74207374616b6520360941b60448201526064016104dc565b816007600082825461098a91906116a8565b9091555050336000908152600a6020526040812080548492906109ae9084906116a8565b9091555050336000818152600b60205260409081902042905560025490516323b872dd60e01b81526004810192909252306024830152604482018490526001600160a01b0316906323b872dd906064016020604051808303816000875af1158015610a1d573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610a4191906116bb565b610a5d5760405162461bcd60e51b81526004016104dc906116dd565b60405182815233907f9e71bc8eea02a63969f509818f2dafb9254532904319f9dbda79b67bd34a5f3d90602001610635565b6001600160a01b0381166000908152600a6020526040812054819081908190610ab7866103c3565b6001600160a01b03969096166000908152600b60205260409020546007549197909550909350915050565b610aea6114df565b33610af3610d04565b600655426005556001600160a01b03811615610b3e57610b12816103c3565b6001600160a01b0382166000908152600960209081526040808320939093556006546008909152919020555b336000908152600960205260409020548015610c58573360008181526009602052604080822091909155600354905163a9059cbb60e01b81526004810192909252602482018390526001600160a01b03169063a9059cbb906044016020604051808303816000875af1158015610bb8573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610bdc91906116bb565b610c215760405162461bcd60e51b815260206004820152601660248201527514995dd85c99081d1c985b9cd9995c8819985a5b195960521b60448201526064016104dc565b60405181815233907fe2403640ba68fed3a2f88b7557551d1993f84b99bb10ff833f0cf8db0c5e0486906020015b60405180910390a25b50506108406001600055565b610c6c611509565b6003546040516323b872dd60e01b8152336004820152306024820152604481018390526001600160a01b03909116906323b872dd906064015b6020604051808303816000875af1158015610cc4573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610ce891906116bb565b6106485760405162461bcd60e51b81526004016104dc906116dd565b6000600754600003610d17575060065490565b600754600454600554610d2a904261165c565b610d34919061166f565b610d4690670de0b6b3a764000061166f565b610d509190611686565b600654610d5d91906116a8565b905090565b610d6a611509565b6003546040516370a0823160e01b81523060048201526000916001600160a01b0316906370a0823190602401602060405180830381865afa158015610db3573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610dd7919061171f565b6003549091506001600160a01b031663a9059cbb610dfd6001546001600160a01b031690565b6040516001600160e01b031960e084901b1681526001600160a01b03909116600482015260248101849052604401610ca5565b610e386114df565b33610e41610d04565b600655426005556001600160a01b03811615610e8c57610e60816103c3565b6001600160a01b0382166000908152600960209081526040808320939093556006546008909152919020555b6000805b83811015610f2c576000858583818110610eac57610eac611738565b9050602002013511610ef45760405162461bcd60e51b8152602060048201526011602482015270043616e6e6f74207769746864726177203607c1b60448201526064016104dc565b848482818110610f0657610f06611738565b9050602002013582610f1891906116a8565b915080610f2481611706565b915050610e90565b50336000908152600a6020526040902054811115610f835760405162461bcd60e51b8152602060048201526014602482015273496e73756666696369656e742062616c616e636560601b60448201526064016104dc565b8060076000828254610f95919061165c565b9091555050336000908152600a602052604081208054839290610fb990849061165c565b909155505060025460405163a9059cbb60e01b8152336004820152602481018390526001600160a01b039091169063a9059cbb906044016020604051808303816000875af115801561100f573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061103391906116bb565b61104f5760405162461bcd60e51b81526004016104dc906116dd565b60005b838110156110be57337f7084f5476618d8e60b11ef0d7d3f06914655adb8793e28ff7f018d4c76d505d586868481811061108e5761108e611738565b905060200201356040516110a491815260200190565b60405180910390a2806110b681611706565b915050611052565b5050506110cb6001600055565b5050565b336000908152600a60205260409020546110e890610440565b610840610ae2565b6110f8611509565b6001600160a01b03811661112257604051631e4fbdf760e01b8152600060048201526024016104dc565b61064881611536565b6111336114df565b3361113c610d04565b600655426005556001600160a01b038116156111875761115b816103c3565b6001600160a01b0382166000908152600960209081526040808320939093556006546008909152919020555b6000805b838110156112245760008585838181106111a7576111a7611738565b90506020020135116111ec5760405162461bcd60e51b815260206004820152600e60248201526d043616e6e6f74207374616b6520360941b60448201526064016104dc565b8484828181106111fe576111fe611738565b905060200201358261121091906116a8565b91508061121c81611706565b91505061118b565b50806007600082825461123791906116a8565b9091555050336000908152600a60205260408120805483929061125b9084906116a8565b9091555050336000818152600b60205260409081902042905560025490516323b872dd60e01b81526004810192909252306024830152604482018390526001600160a01b0316906323b872dd906064016020604051808303816000875af11580156112ca573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906112ee91906116bb565b61130a5760405162461bcd60e51b81526004016104dc906116dd565b60005b838110156110be57337f9e71bc8eea02a63969f509818f2dafb9254532904319f9dbda79b67bd34a5f3d86868481811061134957611349611738565b9050602002013560405161135f91815260200190565b60405180910390a28061137181611706565b91505061130d565b6113816114df565b3361138a610d04565b600655426005556001600160a01b038116156113d5576113a9816103c3565b6001600160a01b0382166000908152600960209081526040808320939093556006546008909152919020555b336000908152600960205260409020548061142b5760405162461bcd60e51b8152602060048201526016602482015275139bc81c995dd85c991cc81d1bc818dbdb5c1bdd5b9960521b60448201526064016104dc565b3360009081526009602052604081208190556007805483929061144f9084906116a8565b9091555050336000908152600a6020526040812080548392906114739084906116a8565b909155505060405181815233907fe2403640ba68fed3a2f88b7557551d1993f84b99bb10ff833f0cf8db0c5e04869060200160405180910390a260405181815233907f9e71bc8eea02a63969f509818f2dafb9254532904319f9dbda79b67bd34a5f3d90602001610c4f565b60026000540361150257604051633ee5aeb560e01b815260040160405180910390fd5b6002600055565b6001546001600160a01b031633146108405760405163118cdaa760e01b81523360048201526024016104dc565b600180546001600160a01b038381166001600160a01b0319831681179093556040519116919082907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a35050565b60006020828403121561159a57600080fd5b81356001600160a01b03811681146115b157600080fd5b9392505050565b6000602082840312156115ca57600080fd5b5035919050565b600080602083850312156115e457600080fd5b823567ffffffffffffffff808211156115fc57600080fd5b818501915085601f83011261161057600080fd5b81358181111561161f57600080fd5b8660208260051b850101111561163457600080fd5b60209290920196919550909350505050565b634e487b7160e01b600052601160045260246000fd5b8181038181111561043a5761043a611646565b808202811582820484141761043a5761043a611646565b6000826116a357634e487b7160e01b600052601260045260246000fd5b500490565b8082018082111561043a5761043a611646565b6000602082840312156116cd57600080fd5b815180151581146115b157600080fd5b6020808252600f908201526e151c985b9cd9995c8819985a5b1959608a1b604082015260600190565b60006001820161171857611718611646565b5060010190565b60006020828403121561173157600080fd5b5051919050565b634e487b7160e01b600052603260045260246000fdfea2646970667358221220c56cf6a2cb1dc3ab206497944bb1c8205ede67acfcbf7defac03da45390eda3f64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}