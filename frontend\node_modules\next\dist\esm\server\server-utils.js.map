{"version": 3, "sources": ["../../src/server/server-utils.ts"], "names": ["format", "formatUrl", "parse", "parseUrl", "normalizeLocalePath", "getPathMatch", "getNamedRouteRegex", "getRouteMatcher", "matchHas", "prepareDestination", "removeTrailingSlash", "normalizeRscURL", "NEXT_QUERY_PARAM_PREFIX", "normalizeVercelUrl", "req", "trustQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pageIsDynamic", "defaultRouteRegex", "_parsedUrl", "url", "search", "key", "Object", "keys", "query", "startsWith", "groups", "includes", "interpolateDynamicPath", "pathname", "params", "param", "optional", "repeat", "builtParam", "paramIdx", "indexOf", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "slice", "length", "normalizeDynamicRouteParams", "ignoreOptional", "defaultRouteMatches", "hasValidParams", "reduce", "prev", "val", "defaultValue", "isOptional", "isDefaultValue", "some", "defaultVal", "undefined", "split", "getUtils", "page", "i18n", "basePath", "rewrites", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "checkRewrite", "rewrite", "matcher", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "assign", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appendParamsToQuery", "destination", "protocol", "replace", "RegExp", "destLocalePathResult", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "renderOpts", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "matchesHasLocale", "normalizedKey", "substring", "routeKeyNames", "filterLocaleItem", "isCatchAll", "_val", "item", "toLowerCase", "locale", "splice", "every", "name", "keyName", "paramName", "pos", "parseInt", "headers"], "mappings": "AAOA,SAASA,UAAUC,SAAS,EAAEC,SAASC,QAAQ,QAAQ,MAAK;AAC5D,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SAASC,YAAY,QAAQ,wCAAuC;AACpE,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SACEC,QAAQ,EACRC,kBAAkB,QACb,iDAAgD;AACvD,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,eAAe,QAAQ,uCAAsC;AACtE,SAASC,uBAAuB,QAAQ,mBAAkB;AAE1D,OAAO,SAASC,mBACdC,GAAoB,EACpBC,UAAmB,EACnBC,SAAoB,EACpBC,aAAuB,EACvBC,iBAAqE;IAErE,mEAAmE;IACnE,gDAAgD;IAChD,IAAID,iBAAiBF,cAAcG,mBAAmB;QACpD,MAAMC,aAAahB,SAASW,IAAIM,GAAG,EAAG;QACtC,OAAO,AAACD,WAAmBE,MAAM;QAEjC,KAAK,MAAMC,OAAOC,OAAOC,IAAI,CAACL,WAAWM,KAAK,EAAG;YAC/C,IACE,AAACH,QAAQV,2BACPU,IAAII,UAAU,CAACd,4BACjB,AAACI,CAAAA,aAAaO,OAAOC,IAAI,CAACN,kBAAkBS,MAAM,CAAA,EAAGC,QAAQ,CAACN,MAC9D;gBACA,OAAOH,WAAWM,KAAK,CAACH,IAAI;YAC9B;QACF;QACAR,IAAIM,GAAG,GAAGnB,UAAUkB;IACtB;AACF;AAEA,OAAO,SAASU,uBACdC,QAAgB,EAChBC,MAAsB,EACtBb,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOY;IAE/B,KAAK,MAAME,SAAST,OAAOC,IAAI,CAACN,kBAAkBS,MAAM,EAAG;QACzD,MAAM,EAAEM,QAAQ,EAAEC,MAAM,EAAE,GAAGhB,kBAAkBS,MAAM,CAACK,MAAM;QAC5D,IAAIG,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,GAAG,EAAEF,MAAM,CAAC,CAAC;QAEnD,IAAIC,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,MAAMC,WAAWN,SAAUO,OAAO,CAACF;QAEnC,IAAIC,WAAW,CAAC,GAAG;YACjB,IAAIE;YACJ,MAAMC,QAAQR,MAAM,CAACC,MAAM;YAE3B,IAAIQ,MAAMC,OAAO,CAACF,QAAQ;gBACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;YACjE,OAAO,IAAIN,OAAO;gBAChBD,aAAaM,mBAAmBL;YAClC,OAAO;gBACLD,aAAa;YACf;YAEAR,WACEA,SAASgB,KAAK,CAAC,GAAGV,YAClBE,aACAR,SAASgB,KAAK,CAACV,WAAWD,WAAWY,MAAM;QAC/C;IACF;IAEA,OAAOjB;AACT;AAEA,OAAO,SAASkB,4BACdjB,MAAsB,EACtBkB,cAAwB,EACxB/B,iBAAqE,EACrEgC,mBAAgD;IAEhD,IAAIC,iBAAiB;IACrB,IAAI,CAACjC,mBAAmB,OAAO;QAAEa;QAAQoB,gBAAgB;IAAM;IAE/DpB,SAASR,OAAOC,IAAI,CAACN,kBAAkBS,MAAM,EAAEyB,MAAM,CAAC,CAACC,MAAM/B;QAC3D,IAAIiB,QAAuCR,MAAM,CAACT,IAAI;QAEtD,IAAI,OAAOiB,UAAU,UAAU;YAC7BA,QAAQ5B,gBAAgB4B;QAC1B;QACA,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YACxBA,QAAQA,MAAMG,GAAG,CAAC,CAACY;gBACjB,IAAI,OAAOA,QAAQ,UAAU;oBAC3BA,MAAM3C,gBAAgB2C;gBACxB;gBACA,OAAOA;YACT;QACF;QAEA,uDAAuD;QACvD,0DAA0D;QAC1D,sCAAsC;QACtC,MAAMC,eAAeL,mBAAoB,CAAC5B,IAAI;QAC9C,MAAMkC,aAAatC,kBAAmBS,MAAM,CAACL,IAAI,CAACW,QAAQ;QAE1D,MAAMwB,iBAAiBjB,MAAMC,OAAO,CAACc,gBACjCA,aAAaG,IAAI,CAAC,CAACC;YACjB,OAAOnB,MAAMC,OAAO,CAACF,SACjBA,MAAMmB,IAAI,CAAC,CAACJ,MAAQA,IAAI1B,QAAQ,CAAC+B,eACjCpB,yBAAAA,MAAOX,QAAQ,CAAC+B;QACtB,KACApB,yBAAAA,MAAOX,QAAQ,CAAC2B;QAEpB,IACEE,kBACC,OAAOlB,UAAU,eAAe,CAAEiB,CAAAA,cAAcP,cAAa,GAC9D;YACAE,iBAAiB;QACnB;QAEA,gEAAgE;QAChE,oBAAoB;QACpB,IACEK,cACC,CAAA,CAACjB,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMQ,MAAM,KAAK,KACjB,6CAA6C;QAC7C,+CAA+C;QAC9CR,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAEjB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;YACAiB,QAAQqB;YACR,OAAO7B,MAAM,CAACT,IAAI;QACpB;QAEA,+DAA+D;QAC/D,6CAA6C;QAC7C,IACEiB,SACA,OAAOA,UAAU,YACjBrB,kBAAmBS,MAAM,CAACL,IAAI,CAACY,MAAM,EACrC;YACAK,QAAQA,MAAMsB,KAAK,CAAC;QACtB;QAEA,IAAItB,OAAO;YACTc,IAAI,CAAC/B,IAAI,GAAGiB;QACd;QACA,OAAOc;IACT,GAAG,CAAC;IAEJ,OAAO;QACLtB;QACAoB;IACF;AACF;AAEA,OAAO,SAASW,SAAS,EACvBC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRjD,aAAa,EACbkD,aAAa,EACbC,aAAa,EAad;IACC,IAAIlD;IACJ,IAAImD;IACJ,IAAInB;IAEJ,IAAIjC,eAAe;QACjBC,oBAAoBZ,mBAAmByD,MAAM;QAC7CM,sBAAsB9D,gBAAgBW;QACtCgC,sBAAsBmB,oBAAoBN;IAC5C;IAEA,SAASO,eAAexD,GAAoB,EAAEyD,SAA6B;QACzE,MAAMC,gBAAgB,CAAC;QACvB,IAAIC,aAAaF,UAAUzC,QAAQ;QAEnC,MAAM4C,cAAc;YAClB,MAAMC,oBAAoBjE,oBAAoB+D,cAAc;YAC5D,OACEE,sBAAsBjE,oBAAoBqD,UAC1CM,uCAAAA,oBAAsBM;QAE1B;QAEA,MAAMC,eAAe,CAACC;YACpB,MAAMC,UAAUzE,aACdwE,QAAQE,MAAM,GAAIZ,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEa,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAACd;YACf;YAEF,IAAIrC,SAAS+C,QAAQP,UAAUzC,QAAQ;YAEvC,IAAI,AAAC+C,CAAAA,QAAQM,GAAG,IAAIN,QAAQO,OAAO,AAAD,KAAMrD,QAAQ;gBAC9C,MAAMsD,YAAY7E,SAChBM,KACAyD,UAAU9C,KAAK,EACfoD,QAAQM,GAAG,EACXN,QAAQO,OAAO;gBAGjB,IAAIC,WAAW;oBACb9D,OAAO+D,MAAM,CAACvD,QAAQsD;gBACxB,OAAO;oBACLtD,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,MAAM,EAAEwD,iBAAiB,EAAEC,SAAS,EAAE,GAAG/E,mBAAmB;oBAC1DgF,qBAAqB;oBACrBC,aAAab,QAAQa,WAAW;oBAChC3D,QAAQA;oBACRN,OAAO8C,UAAU9C,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAI8D,kBAAkBI,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEApE,OAAO+D,MAAM,CAACd,eAAegB,WAAWzD;gBACxCR,OAAO+D,MAAM,CAACf,UAAU9C,KAAK,EAAE8D,kBAAkB9D,KAAK;gBACtD,OAAO,AAAC8D,kBAA0B9D,KAAK;gBAEvCF,OAAO+D,MAAM,CAACf,WAAWgB;gBAEzBd,aAAaF,UAAUzC,QAAQ;gBAE/B,IAAImC,UAAU;oBACZQ,aACEA,WAAYmB,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAE5B,SAAS,CAAC,GAAG,OAAO;gBAC3D;gBAEA,IAAID,MAAM;oBACR,MAAM8B,uBAAuB1F,oBAC3BqE,YACAT,KAAK+B,OAAO;oBAEdtB,aAAaqB,qBAAqBhE,QAAQ;oBAC1CyC,UAAU9C,KAAK,CAACuE,kBAAkB,GAChCF,qBAAqBG,cAAc,IAAIlE,OAAOiE,kBAAkB;gBACpE;gBAEA,IAAIvB,eAAeV,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAI9C,iBAAiBoD,qBAAqB;oBACxC,MAAM6B,gBAAgB7B,oBAAoBI;oBAC1C,IAAIyB,eAAe;wBACjB3B,UAAU9C,KAAK,GAAG;4BAChB,GAAG8C,UAAU9C,KAAK;4BAClB,GAAGyE,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMrB,WAAWX,SAASiC,WAAW,IAAI,EAAE,CAAE;YAChDvB,aAAaC;QACf;QAEA,IAAIJ,eAAeV,MAAM;YACvB,IAAIqC,WAAW;YAEf,KAAK,MAAMvB,WAAWX,SAASmC,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAWxB,aAAaC;gBACxB,IAAIuB,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC1B,eAAe;gBAC/B,KAAK,MAAMG,WAAWX,SAASoC,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAWxB,aAAaC;oBACxB,IAAIuB,UAAU;gBAChB;YACF;QACF;QACA,OAAO5B;IACT;IAEA,SAAS+B,0BACPzF,GAAoB,EACpB0F,UAAgB,EAChBP,cAAuB;QAEvB,OAAO1F,gBACL,AAAC;YACC,MAAM,EAAEoB,MAAM,EAAE8E,SAAS,EAAE,GAAGvF;YAE9B,OAAO;gBACLwF,IAAI;oBACF,qDAAqD;oBACrDC,MAAM,CAACC;wBACL,MAAMC,MAAMtF,OAAOuF,WAAW,CAAC,IAAIC,gBAAgBH;wBACnD,MAAMI,mBACJhD,QAAQiC,kBAAkBY,GAAG,CAAC,IAAI,KAAKZ;wBAEzC,KAAK,MAAM3E,OAAOC,OAAOC,IAAI,CAACqF,KAAM;4BAClC,MAAMtE,QAAQsE,GAAG,CAACvF,IAAI;4BAEtB,IACEA,QAAQV,2BACRU,IAAII,UAAU,CAACd,0BACf;gCACA,MAAMqG,gBAAgB3F,IAAI4F,SAAS,CACjCtG,wBAAwBmC,MAAM;gCAEhC8D,GAAG,CAACI,cAAc,GAAG1E;gCACrB,OAAOsE,GAAG,CAACvF,IAAI;4BACjB;wBACF;wBAEA,mCAAmC;wBACnC,MAAM6F,gBAAgB5F,OAAOC,IAAI,CAACiF,aAAa,CAAC;wBAChD,MAAMW,mBAAmB,CAAC9D;4BACxB,IAAIU,MAAM;gCACR,gDAAgD;gCAChD,4CAA4C;gCAC5C,WAAW;gCACX,MAAMqD,aAAa7E,MAAMC,OAAO,CAACa;gCACjC,MAAMgE,OAAOD,aAAa/D,GAAG,CAAC,EAAE,GAAGA;gCAEnC,IACE,OAAOgE,SAAS,YAChBtD,KAAK+B,OAAO,CAACrC,IAAI,CAAC,CAAC6D;oCACjB,IAAIA,KAAKC,WAAW,OAAOF,KAAKE,WAAW,IAAI;wCAC7CvB,iBAAiBsB;wCACjBf,WAAWiB,MAAM,GAAGxB;wCACpB,OAAO;oCACT;oCACA,OAAO;gCACT,IACA;oCACA,wCAAwC;oCACxC,IAAIoB,YAAY;wCACZ/D,IAAiBoE,MAAM,CAAC,GAAG;oCAC/B;oCAEA,sCAAsC;oCACtC,qBAAqB;oCACrB,OAAOL,aAAa/D,IAAIP,MAAM,KAAK,IAAI;gCACzC;4BACF;4BACA,OAAO;wBACT;wBAEA,IAAIoE,cAAcQ,KAAK,CAAC,CAACC,OAASf,GAAG,CAACe,KAAK,GAAG;4BAC5C,OAAOT,cAAc/D,MAAM,CAAC,CAACC,MAAMwE;gCACjC,MAAMC,YAAYrB,6BAAAA,SAAW,CAACoB,QAAQ;gCAEtC,IAAIC,aAAa,CAACV,iBAAiBP,GAAG,CAACgB,QAAQ,GAAG;oCAChDxE,IAAI,CAAC1B,MAAM,CAACmG,UAAU,CAACC,GAAG,CAAC,GAAGlB,GAAG,CAACgB,QAAQ;gCAC5C;gCACA,OAAOxE;4BACT,GAAG,CAAC;wBACN;wBAEA,OAAO9B,OAAOC,IAAI,CAACqF,KAAKzD,MAAM,CAAC,CAACC,MAAM/B;4BACpC,IAAI,CAAC8F,iBAAiBP,GAAG,CAACvF,IAAI,GAAG;gCAC/B,IAAI2F,gBAAgB3F;gCAEpB,IAAI0F,kBAAkB;oCACpBC,gBAAgBe,SAAS1G,KAAK,MAAM,IAAI;gCAC1C;gCACA,OAAOC,OAAO+D,MAAM,CAACjC,MAAM;oCACzB,CAAC4D,cAAc,EAAEJ,GAAG,CAACvF,IAAI;gCAC3B;4BACF;4BACA,OAAO+B;wBACT,GAAG,CAAC;oBACN;gBACF;gBACA1B;YACF;QACF,KACAb,IAAImH,OAAO,CAAC,sBAAsB;IACtC;IAEA,OAAO;QACL3D;QACApD;QACAmD;QACAnB;QACAqD;QACAvD,6BAA6B,CAC3BjB,QACAkB,iBAEAD,4BACEjB,QACAkB,gBACA/B,mBACAgC;QAEJrC,oBAAoB,CAClBC,KACAC,YACAC,YAEAH,mBACEC,KACAC,YACAC,WACAC,eACAC;QAEJW,wBAAwB,CACtBC,UACAC,SACGF,uBAAuBC,UAAUC,QAAQb;IAChD;AACF"}