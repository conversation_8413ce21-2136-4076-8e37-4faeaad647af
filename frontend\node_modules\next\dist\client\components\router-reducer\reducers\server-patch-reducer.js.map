{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts"], "names": ["serverPatchReducer", "state", "action", "flightData", "overrideCanonicalUrl", "mutable", "preserveCustomHistoryState", "handleExternalUrl", "pushRef", "pendingPush", "currentTree", "tree", "currentCache", "cache", "flightDataPath", "flightSegmentPath", "slice", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "canonicalUrl", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "createEmptyCacheNode", "applyFlightData", "patchedTree", "handleMutable"], "mappings": ";;;;+<PERSON>eg<PERSON>;;;eAAAA;;;mCAfkB;6CACU;6CACA;iCAOV;iCACF;+BACF;2BAEO;AAE9B,SAASA,mBACdC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,UAAU,EAAEC,oBAAoB,EAAE,GAAGF;IAE7C,MAAMG,UAAmB,CAAC;IAE1BA,QAAQC,0BAA0B,GAAG;IAErC,4DAA4D;IAC5D,IAAI,OAAOH,eAAe,UAAU;QAClC,OAAOI,IAAAA,kCAAiB,EACtBN,OACAI,SACAF,YACAF,MAAMO,OAAO,CAACC,WAAW;IAE7B;IAEA,IAAIC,cAAcT,MAAMU,IAAI;IAC5B,IAAIC,eAAeX,MAAMY,KAAK;IAE9B,KAAK,MAAMC,kBAAkBX,WAAY;QACvC,mFAAmF;QACnF,MAAMY,oBAAoBD,eAAeE,KAAK,CAAC,GAAG,CAAC;QAEnD,MAAM,CAACC,UAAU,GAAGH,eAAeE,KAAK,CAAC,CAAC,GAAG,CAAC;QAC9C,MAAME,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;QACtB;YAAC;eAAOJ;SAAkB,EAC1BL,aACAO;QAGF,IAAIC,YAAY,MAAM;YACpB,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAIC,IAAAA,wDAA2B,EAACX,aAAaQ,UAAU;YACrD,OAAOX,IAAAA,kCAAiB,EACtBN,OACAI,SACAJ,MAAMqB,YAAY,EAClBrB,MAAMO,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMc,2BAA2BnB,uBAC7BoB,IAAAA,oCAAiB,EAACpB,wBAClBqB;QAEJ,IAAIF,0BAA0B;YAC5BlB,QAAQiB,YAAY,GAAGC;QACzB;QAEA,MAAMV,QAAmBa,IAAAA,+BAAoB;QAC7CC,IAAAA,gCAAe,EAACf,cAAcC,OAAOC;QAErCT,QAAQuB,WAAW,GAAGV;QACtBb,QAAQQ,KAAK,GAAGA;QAEhBD,eAAeC;QACfH,cAAcQ;IAChB;IAEA,OAAOW,IAAAA,4BAAa,EAAC5B,OAAOI;AAC9B"}