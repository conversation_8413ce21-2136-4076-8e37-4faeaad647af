"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@noble";
exports.ids = ["vendor-chunks/@noble"];
exports.modules = {

/***/ "(ssr)/./node_modules/@noble/curves/esm/_shortw_utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/@noble/curves/esm/_shortw_utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCurve: () => (/* binding */ createCurve),\n/* harmony export */   getHash: () => (/* binding */ getHash)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/hmac */ \"(ssr)/./node_modules/@noble/hashes/esm/hmac.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */ \n\n\n// connects noble-curves to noble-hashes\nfunction getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs)=>(0,_noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__.hmac)(hash, key, (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.concatBytes)(...msgs)),\n        randomBytes: _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.randomBytes\n    };\n}\nfunction createCurve(curveDef, defHash) {\n    const create = (hash)=>(0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__.weierstrass)({\n            ...curveDef,\n            ...getHash(hash)\n        });\n    return Object.freeze({\n        ...create(defHash),\n        create\n    });\n} //# sourceMappingURL=_shortw_utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vX3Nob3J0d191dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBLG9FQUFvRSxHQUMxQjtBQUNxQjtBQUNQO0FBQ3hELHdDQUF3QztBQUNqQyxTQUFTSSxRQUFRQyxJQUFJO0lBQ3hCLE9BQU87UUFDSEE7UUFDQUwsTUFBTSxDQUFDTSxLQUFLLEdBQUdDLE9BQVNQLHdEQUFJQSxDQUFDSyxNQUFNQyxLQUFLTCxnRUFBV0EsSUFBSU07UUFDdkRMLFdBQVdBLDhEQUFBQTtJQUNmO0FBQ0o7QUFDTyxTQUFTTSxZQUFZQyxRQUFRLEVBQUVDLE9BQU87SUFDekMsTUFBTUMsU0FBUyxDQUFDTixPQUFTRixxRUFBV0EsQ0FBQztZQUFFLEdBQUdNLFFBQVE7WUFBRSxHQUFHTCxRQUFRQyxLQUFLO1FBQUM7SUFDckUsT0FBT08sT0FBT0MsTUFBTSxDQUFDO1FBQUUsR0FBR0YsT0FBT0QsUUFBUTtRQUFFQztJQUFPO0FBQ3RELEVBQ0EseUNBQXlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dXMtc3dhcC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9Abm9ibGUvY3VydmVzL2VzbS9fc2hvcnR3X3V0aWxzLmpzP2YyNDQiXSwic291cmNlc0NvbnRlbnQiOlsiLyohIG5vYmxlLWN1cnZlcyAtIE1JVCBMaWNlbnNlIChjKSAyMDIyIFBhdWwgTWlsbGVyIChwYXVsbWlsbHIuY29tKSAqL1xuaW1wb3J0IHsgaG1hYyB9IGZyb20gJ0Bub2JsZS9oYXNoZXMvaG1hYyc7XG5pbXBvcnQgeyBjb25jYXRCeXRlcywgcmFuZG9tQnl0ZXMgfSBmcm9tICdAbm9ibGUvaGFzaGVzL3V0aWxzJztcbmltcG9ydCB7IHdlaWVyc3RyYXNzIH0gZnJvbSAnLi9hYnN0cmFjdC93ZWllcnN0cmFzcy5qcyc7XG4vLyBjb25uZWN0cyBub2JsZS1jdXJ2ZXMgdG8gbm9ibGUtaGFzaGVzXG5leHBvcnQgZnVuY3Rpb24gZ2V0SGFzaChoYXNoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgaGFzaCxcbiAgICAgICAgaG1hYzogKGtleSwgLi4ubXNncykgPT4gaG1hYyhoYXNoLCBrZXksIGNvbmNhdEJ5dGVzKC4uLm1zZ3MpKSxcbiAgICAgICAgcmFuZG9tQnl0ZXMsXG4gICAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVDdXJ2ZShjdXJ2ZURlZiwgZGVmSGFzaCkge1xuICAgIGNvbnN0IGNyZWF0ZSA9IChoYXNoKSA9PiB3ZWllcnN0cmFzcyh7IC4uLmN1cnZlRGVmLCAuLi5nZXRIYXNoKGhhc2gpIH0pO1xuICAgIHJldHVybiBPYmplY3QuZnJlZXplKHsgLi4uY3JlYXRlKGRlZkhhc2gpLCBjcmVhdGUgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1fc2hvcnR3X3V0aWxzLmpzLm1hcCJdLCJuYW1lcyI6WyJobWFjIiwiY29uY2F0Qnl0ZXMiLCJyYW5kb21CeXRlcyIsIndlaWVyc3RyYXNzIiwiZ2V0SGFzaCIsImhhc2giLCJrZXkiLCJtc2dzIiwiY3JlYXRlQ3VydmUiLCJjdXJ2ZURlZiIsImRlZkhhc2giLCJjcmVhdGUiLCJPYmplY3QiLCJmcmVlemUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/_shortw_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/abstract/curve.js":
/*!**********************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/curve.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateBasic: () => (/* binding */ validateBasic),\n/* harmony export */   wNAF: () => (/* binding */ wNAF)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./modular.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */ // Abelian group utilities\n\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\n// Elliptic curve multiplication of Point by scalar. Fragile.\n// Scalars should always be less than curve order: this should be checked inside of a curve itself.\n// Creates precomputation tables for fast multiplication:\n// - private scalar is split by fixed size windows of W bits\n// - every window point is collected from window's table & added to accumulator\n// - since windows are different, same point inside tables won't be accessed more than once per calc\n// - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n// - +1 window is neccessary for wNAF\n// - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n// TODO: Research returning 2d JS array of windows, instead of a single window. This would allow\n// windows to be in different memory locations\nfunction wNAF(c, bits) {\n    const constTimeNegate = (condition, item)=>{\n        const neg = item.negate();\n        return condition ? neg : item;\n    };\n    const opts = (W)=>{\n        const windows = Math.ceil(bits / W) + 1; // +1, because\n        const windowSize = 2 ** (W - 1); // -1 because we skip zero\n        return {\n            windows,\n            windowSize\n        };\n    };\n    return {\n        constTimeNegate,\n        // non-const time multiplication ladder\n        unsafeLadder (elm, n) {\n            let p = c.ZERO;\n            let d = elm;\n            while(n > _0n){\n                if (n & _1n) p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @returns precomputed point tables flattened to a single array\n         */ precomputeWindow (elm, W) {\n            const { windows, windowSize } = opts(W);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for(let window = 0; window < windows; window++){\n                base = p;\n                points.push(base);\n                // =1, because we skip zero\n                for(let i = 1; i < windowSize; i++){\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */ wNAF (W, precomputes, n) {\n            // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n            // But need to carefully remove other checks before wNAF. ORDER == bits here\n            const { windows, windowSize } = opts(W);\n            let p = c.ZERO;\n            let f = c.BASE;\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for(let window = 0; window < windows; window++){\n                const offset = window * windowSize;\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n                // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n                // there is negate now: it is possible that negated element from low value\n                // would be the same as high element, which will create carry into next window.\n                // It's not obvious how this can fail, but still worth investigating later.\n                // Check if we're onto Zero point.\n                // Add random point inside current window to f.\n                const offset1 = offset;\n                const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n                const cond1 = window % 2 !== 0;\n                const cond2 = wbits < 0;\n                if (wbits === 0) {\n                    // The most important part for const-time getPublicKey\n                    f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n                } else {\n                    p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n                }\n            }\n            // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n            // Even if the variable is still unused, there are some checks which will\n            // throw an exception, so compiler needs to prove they won't happen, which is hard.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return {\n                p,\n                f\n            };\n        },\n        wNAFCached (P, precomputesMap, n, transform) {\n            // @ts-ignore\n            const W = P._WINDOW_SIZE || 1;\n            // Calculate precomputes on a first run, reuse them after\n            let comp = precomputesMap.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1) {\n                    precomputesMap.set(P, transform(comp));\n                }\n            }\n            return this.wNAF(W, comp, n);\n        }\n    };\n}\nfunction validateBasic(curve) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_0__.validateField)(curve.Fp);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.validateObject)(curve, {\n        n: \"bigint\",\n        h: \"bigint\",\n        Gx: \"field\",\n        Gy: \"field\"\n    }, {\n        nBitLength: \"isSafeInteger\",\n        nByteLength: \"isSafeInteger\"\n    });\n    // Set defaults\n    return Object.freeze({\n        ...(0,_modular_js__WEBPACK_IMPORTED_MODULE_0__.nLength)(curve.n, curve.nBitLength),\n        ...curve,\n        ...{\n            p: curve.Fp.ORDER\n        }\n    });\n} //# sourceMappingURL=curve.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vYWJzdHJhY3QvY3VydmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLG9FQUFvRSxHQUNwRSwwQkFBMEI7QUFDNEI7QUFDVjtBQUM1QyxNQUFNRyxNQUFNQyxPQUFPO0FBQ25CLE1BQU1DLE1BQU1ELE9BQU87QUFDbkIsNkRBQTZEO0FBQzdELG1HQUFtRztBQUNuRyx5REFBeUQ7QUFDekQsNERBQTREO0FBQzVELCtFQUErRTtBQUMvRSxvR0FBb0c7QUFDcEcsb0dBQW9HO0FBQ3BHLHFDQUFxQztBQUNyQyxrR0FBa0c7QUFDbEcsZ0dBQWdHO0FBQ2hHLDhDQUE4QztBQUN2QyxTQUFTRSxLQUFLQyxDQUFDLEVBQUVDLElBQUk7SUFDeEIsTUFBTUMsa0JBQWtCLENBQUNDLFdBQVdDO1FBQ2hDLE1BQU1DLE1BQU1ELEtBQUtFLE1BQU07UUFDdkIsT0FBT0gsWUFBWUUsTUFBTUQ7SUFDN0I7SUFDQSxNQUFNRyxPQUFPLENBQUNDO1FBQ1YsTUFBTUMsVUFBVUMsS0FBS0MsSUFBSSxDQUFDVixPQUFPTyxLQUFLLEdBQUcsY0FBYztRQUN2RCxNQUFNSSxhQUFhLEtBQU1KLENBQUFBLElBQUksSUFBSSwwQkFBMEI7UUFDM0QsT0FBTztZQUFFQztZQUFTRztRQUFXO0lBQ2pDO0lBQ0EsT0FBTztRQUNIVjtRQUNBLHVDQUF1QztRQUN2Q1csY0FBYUMsR0FBRyxFQUFFQyxDQUFDO1lBQ2YsSUFBSUMsSUFBSWhCLEVBQUVpQixJQUFJO1lBQ2QsSUFBSUMsSUFBSUo7WUFDUixNQUFPQyxJQUFJbkIsSUFBSztnQkFDWixJQUFJbUIsSUFBSWpCLEtBQ0prQixJQUFJQSxFQUFFRyxHQUFHLENBQUNEO2dCQUNkQSxJQUFJQSxFQUFFRSxNQUFNO2dCQUNaTCxNQUFNakI7WUFDVjtZQUNBLE9BQU9rQjtRQUNYO1FBQ0E7Ozs7Ozs7OztTQVNDLEdBQ0RLLGtCQUFpQlAsR0FBRyxFQUFFTixDQUFDO1lBQ25CLE1BQU0sRUFBRUMsT0FBTyxFQUFFRyxVQUFVLEVBQUUsR0FBR0wsS0FBS0M7WUFDckMsTUFBTWMsU0FBUyxFQUFFO1lBQ2pCLElBQUlOLElBQUlGO1lBQ1IsSUFBSVMsT0FBT1A7WUFDWCxJQUFLLElBQUlRLFNBQVMsR0FBR0EsU0FBU2YsU0FBU2UsU0FBVTtnQkFDN0NELE9BQU9QO2dCQUNQTSxPQUFPRyxJQUFJLENBQUNGO2dCQUNaLDJCQUEyQjtnQkFDM0IsSUFBSyxJQUFJRyxJQUFJLEdBQUdBLElBQUlkLFlBQVljLElBQUs7b0JBQ2pDSCxPQUFPQSxLQUFLSixHQUFHLENBQUNIO29CQUNoQk0sT0FBT0csSUFBSSxDQUFDRjtnQkFDaEI7Z0JBQ0FQLElBQUlPLEtBQUtILE1BQU07WUFDbkI7WUFDQSxPQUFPRTtRQUNYO1FBQ0E7Ozs7OztTQU1DLEdBQ0R2QixNQUFLUyxDQUFDLEVBQUVtQixXQUFXLEVBQUVaLENBQUM7WUFDbEIsZ0dBQWdHO1lBQ2hHLDRFQUE0RTtZQUM1RSxNQUFNLEVBQUVOLE9BQU8sRUFBRUcsVUFBVSxFQUFFLEdBQUdMLEtBQUtDO1lBQ3JDLElBQUlRLElBQUloQixFQUFFaUIsSUFBSTtZQUNkLElBQUlXLElBQUk1QixFQUFFNkIsSUFBSTtZQUNkLE1BQU1DLE9BQU9qQyxPQUFPLEtBQUtXLElBQUksSUFBSSwrQ0FBK0M7WUFDaEYsTUFBTXVCLFlBQVksS0FBS3ZCO1lBQ3ZCLE1BQU13QixVQUFVbkMsT0FBT1c7WUFDdkIsSUFBSyxJQUFJZ0IsU0FBUyxHQUFHQSxTQUFTZixTQUFTZSxTQUFVO2dCQUM3QyxNQUFNUyxTQUFTVCxTQUFTWjtnQkFDeEIsa0JBQWtCO2dCQUNsQixJQUFJc0IsUUFBUUMsT0FBT3BCLElBQUllO2dCQUN2QiwwQkFBMEI7Z0JBQzFCZixNQUFNaUI7Z0JBQ04sMkRBQTJEO2dCQUMzRCxtQkFBbUI7Z0JBQ25CLElBQUlFLFFBQVF0QixZQUFZO29CQUNwQnNCLFNBQVNIO29CQUNUaEIsS0FBS2pCO2dCQUNUO2dCQUNBLDZGQUE2RjtnQkFDN0YscUZBQXFGO2dCQUNyRiwwRUFBMEU7Z0JBQzFFLCtFQUErRTtnQkFDL0UsMkVBQTJFO2dCQUMzRSxrQ0FBa0M7Z0JBQ2xDLCtDQUErQztnQkFDL0MsTUFBTXNDLFVBQVVIO2dCQUNoQixNQUFNSSxVQUFVSixTQUFTdkIsS0FBSzRCLEdBQUcsQ0FBQ0osU0FBUyxHQUFHLDBCQUEwQjtnQkFDeEUsTUFBTUssUUFBUWYsU0FBUyxNQUFNO2dCQUM3QixNQUFNZ0IsUUFBUU4sUUFBUTtnQkFDdEIsSUFBSUEsVUFBVSxHQUFHO29CQUNiLHNEQUFzRDtvQkFDdEROLElBQUlBLEVBQUVULEdBQUcsQ0FBQ2pCLGdCQUFnQnFDLE9BQU9aLFdBQVcsQ0FBQ1MsUUFBUTtnQkFDekQsT0FDSztvQkFDRHBCLElBQUlBLEVBQUVHLEdBQUcsQ0FBQ2pCLGdCQUFnQnNDLE9BQU9iLFdBQVcsQ0FBQ1UsUUFBUTtnQkFDekQ7WUFDSjtZQUNBLHdGQUF3RjtZQUN4Rix5RUFBeUU7WUFDekUsbUZBQW1GO1lBQ25GLHdFQUF3RTtZQUN4RSw0REFBNEQ7WUFDNUQsT0FBTztnQkFBRXJCO2dCQUFHWTtZQUFFO1FBQ2xCO1FBQ0FhLFlBQVdDLENBQUMsRUFBRUMsY0FBYyxFQUFFNUIsQ0FBQyxFQUFFNkIsU0FBUztZQUN0QyxhQUFhO1lBQ2IsTUFBTXBDLElBQUlrQyxFQUFFRyxZQUFZLElBQUk7WUFDNUIseURBQXlEO1lBQ3pELElBQUlDLE9BQU9ILGVBQWVJLEdBQUcsQ0FBQ0w7WUFDOUIsSUFBSSxDQUFDSSxNQUFNO2dCQUNQQSxPQUFPLElBQUksQ0FBQ3pCLGdCQUFnQixDQUFDcUIsR0FBR2xDO2dCQUNoQyxJQUFJQSxNQUFNLEdBQUc7b0JBQ1RtQyxlQUFlSyxHQUFHLENBQUNOLEdBQUdFLFVBQVVFO2dCQUNwQztZQUNKO1lBQ0EsT0FBTyxJQUFJLENBQUMvQyxJQUFJLENBQUNTLEdBQUdzQyxNQUFNL0I7UUFDOUI7SUFDSjtBQUNKO0FBQ08sU0FBU2tDLGNBQWNDLEtBQUs7SUFDL0J6RCwwREFBYUEsQ0FBQ3lELE1BQU1DLEVBQUU7SUFDdEJ4RCx5REFBY0EsQ0FBQ3VELE9BQU87UUFDbEJuQyxHQUFHO1FBQ0hxQyxHQUFHO1FBQ0hDLElBQUk7UUFDSkMsSUFBSTtJQUNSLEdBQUc7UUFDQ0MsWUFBWTtRQUNaQyxhQUFhO0lBQ2pCO0lBQ0EsZUFBZTtJQUNmLE9BQU9DLE9BQU9DLE1BQU0sQ0FBQztRQUNqQixHQUFHaEUsb0RBQU9BLENBQUN3RCxNQUFNbkMsQ0FBQyxFQUFFbUMsTUFBTUssVUFBVSxDQUFDO1FBQ3JDLEdBQUdMLEtBQUs7UUFDUixHQUFHO1lBQUVsQyxHQUFHa0MsTUFBTUMsRUFBRSxDQUFDUSxLQUFLO1FBQUMsQ0FBQztJQUM1QjtBQUNKLEVBQ0EsaUNBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dXMtc3dhcC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9Abm9ibGUvY3VydmVzL2VzbS9hYnN0cmFjdC9jdXJ2ZS5qcz82MWQ5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qISBub2JsZS1jdXJ2ZXMgLSBNSVQgTGljZW5zZSAoYykgMjAyMiBQYXVsIE1pbGxlciAocGF1bG1pbGxyLmNvbSkgKi9cbi8vIEFiZWxpYW4gZ3JvdXAgdXRpbGl0aWVzXG5pbXBvcnQgeyB2YWxpZGF0ZUZpZWxkLCBuTGVuZ3RoIH0gZnJvbSAnLi9tb2R1bGFyLmpzJztcbmltcG9ydCB7IHZhbGlkYXRlT2JqZWN0IH0gZnJvbSAnLi91dGlscy5qcyc7XG5jb25zdCBfMG4gPSBCaWdJbnQoMCk7XG5jb25zdCBfMW4gPSBCaWdJbnQoMSk7XG4vLyBFbGxpcHRpYyBjdXJ2ZSBtdWx0aXBsaWNhdGlvbiBvZiBQb2ludCBieSBzY2FsYXIuIEZyYWdpbGUuXG4vLyBTY2FsYXJzIHNob3VsZCBhbHdheXMgYmUgbGVzcyB0aGFuIGN1cnZlIG9yZGVyOiB0aGlzIHNob3VsZCBiZSBjaGVja2VkIGluc2lkZSBvZiBhIGN1cnZlIGl0c2VsZi5cbi8vIENyZWF0ZXMgcHJlY29tcHV0YXRpb24gdGFibGVzIGZvciBmYXN0IG11bHRpcGxpY2F0aW9uOlxuLy8gLSBwcml2YXRlIHNjYWxhciBpcyBzcGxpdCBieSBmaXhlZCBzaXplIHdpbmRvd3Mgb2YgVyBiaXRzXG4vLyAtIGV2ZXJ5IHdpbmRvdyBwb2ludCBpcyBjb2xsZWN0ZWQgZnJvbSB3aW5kb3cncyB0YWJsZSAmIGFkZGVkIHRvIGFjY3VtdWxhdG9yXG4vLyAtIHNpbmNlIHdpbmRvd3MgYXJlIGRpZmZlcmVudCwgc2FtZSBwb2ludCBpbnNpZGUgdGFibGVzIHdvbid0IGJlIGFjY2Vzc2VkIG1vcmUgdGhhbiBvbmNlIHBlciBjYWxjXG4vLyAtIGVhY2ggbXVsdGlwbGljYXRpb24gaXMgJ01hdGguY2VpbChDVVJWRV9PUkRFUiAvIPCdkYopICsgMScgcG9pbnQgYWRkaXRpb25zIChmaXhlZCBmb3IgYW55IHNjYWxhcilcbi8vIC0gKzEgd2luZG93IGlzIG5lY2Nlc3NhcnkgZm9yIHdOQUZcbi8vIC0gd05BRiByZWR1Y2VzIHRhYmxlIHNpemU6IDJ4IGxlc3MgbWVtb3J5ICsgMnggZmFzdGVyIGdlbmVyYXRpb24sIGJ1dCAxMCUgc2xvd2VyIG11bHRpcGxpY2F0aW9uXG4vLyBUT0RPOiBSZXNlYXJjaCByZXR1cm5pbmcgMmQgSlMgYXJyYXkgb2Ygd2luZG93cywgaW5zdGVhZCBvZiBhIHNpbmdsZSB3aW5kb3cuIFRoaXMgd291bGQgYWxsb3dcbi8vIHdpbmRvd3MgdG8gYmUgaW4gZGlmZmVyZW50IG1lbW9yeSBsb2NhdGlvbnNcbmV4cG9ydCBmdW5jdGlvbiB3TkFGKGMsIGJpdHMpIHtcbiAgICBjb25zdCBjb25zdFRpbWVOZWdhdGUgPSAoY29uZGl0aW9uLCBpdGVtKSA9PiB7XG4gICAgICAgIGNvbnN0IG5lZyA9IGl0ZW0ubmVnYXRlKCk7XG4gICAgICAgIHJldHVybiBjb25kaXRpb24gPyBuZWcgOiBpdGVtO1xuICAgIH07XG4gICAgY29uc3Qgb3B0cyA9IChXKSA9PiB7XG4gICAgICAgIGNvbnN0IHdpbmRvd3MgPSBNYXRoLmNlaWwoYml0cyAvIFcpICsgMTsgLy8gKzEsIGJlY2F1c2VcbiAgICAgICAgY29uc3Qgd2luZG93U2l6ZSA9IDIgKiogKFcgLSAxKTsgLy8gLTEgYmVjYXVzZSB3ZSBza2lwIHplcm9cbiAgICAgICAgcmV0dXJuIHsgd2luZG93cywgd2luZG93U2l6ZSB9O1xuICAgIH07XG4gICAgcmV0dXJuIHtcbiAgICAgICAgY29uc3RUaW1lTmVnYXRlLFxuICAgICAgICAvLyBub24tY29uc3QgdGltZSBtdWx0aXBsaWNhdGlvbiBsYWRkZXJcbiAgICAgICAgdW5zYWZlTGFkZGVyKGVsbSwgbikge1xuICAgICAgICAgICAgbGV0IHAgPSBjLlpFUk87XG4gICAgICAgICAgICBsZXQgZCA9IGVsbTtcbiAgICAgICAgICAgIHdoaWxlIChuID4gXzBuKSB7XG4gICAgICAgICAgICAgICAgaWYgKG4gJiBfMW4pXG4gICAgICAgICAgICAgICAgICAgIHAgPSBwLmFkZChkKTtcbiAgICAgICAgICAgICAgICBkID0gZC5kb3VibGUoKTtcbiAgICAgICAgICAgICAgICBuID4+PSBfMW47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcDtcbiAgICAgICAgfSxcbiAgICAgICAgLyoqXG4gICAgICAgICAqIENyZWF0ZXMgYSB3TkFGIHByZWNvbXB1dGF0aW9uIHdpbmRvdy4gVXNlZCBmb3IgY2FjaGluZy5cbiAgICAgICAgICogRGVmYXVsdCB3aW5kb3cgc2l6ZSBpcyBzZXQgYnkgYHV0aWxzLnByZWNvbXB1dGUoKWAgYW5kIGlzIGVxdWFsIHRvIDguXG4gICAgICAgICAqIE51bWJlciBvZiBwcmVjb21wdXRlZCBwb2ludHMgZGVwZW5kcyBvbiB0aGUgY3VydmUgc2l6ZTpcbiAgICAgICAgICogMl4o8J2RiuKIkjEpICogKE1hdGguY2VpbCjwnZGbIC8g8J2RiikgKyAxKSwgd2hlcmU6XG4gICAgICAgICAqIC0g8J2RiiBpcyB0aGUgd2luZG93IHNpemVcbiAgICAgICAgICogLSDwnZGbIGlzIHRoZSBiaXRsZW5ndGggb2YgdGhlIGN1cnZlIG9yZGVyLlxuICAgICAgICAgKiBGb3IgYSAyNTYtYml0IGN1cnZlIGFuZCB3aW5kb3cgc2l6ZSA4LCB0aGUgbnVtYmVyIG9mIHByZWNvbXB1dGVkIHBvaW50cyBpcyAxMjggKiAzMyA9IDQyMjQuXG4gICAgICAgICAqIEByZXR1cm5zIHByZWNvbXB1dGVkIHBvaW50IHRhYmxlcyBmbGF0dGVuZWQgdG8gYSBzaW5nbGUgYXJyYXlcbiAgICAgICAgICovXG4gICAgICAgIHByZWNvbXB1dGVXaW5kb3coZWxtLCBXKSB7XG4gICAgICAgICAgICBjb25zdCB7IHdpbmRvd3MsIHdpbmRvd1NpemUgfSA9IG9wdHMoVyk7XG4gICAgICAgICAgICBjb25zdCBwb2ludHMgPSBbXTtcbiAgICAgICAgICAgIGxldCBwID0gZWxtO1xuICAgICAgICAgICAgbGV0IGJhc2UgPSBwO1xuICAgICAgICAgICAgZm9yIChsZXQgd2luZG93ID0gMDsgd2luZG93IDwgd2luZG93czsgd2luZG93KyspIHtcbiAgICAgICAgICAgICAgICBiYXNlID0gcDtcbiAgICAgICAgICAgICAgICBwb2ludHMucHVzaChiYXNlKTtcbiAgICAgICAgICAgICAgICAvLyA9MSwgYmVjYXVzZSB3ZSBza2lwIHplcm9cbiAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMTsgaSA8IHdpbmRvd1NpemU7IGkrKykge1xuICAgICAgICAgICAgICAgICAgICBiYXNlID0gYmFzZS5hZGQocCk7XG4gICAgICAgICAgICAgICAgICAgIHBvaW50cy5wdXNoKGJhc2UpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBwID0gYmFzZS5kb3VibGUoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBwb2ludHM7XG4gICAgICAgIH0sXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBJbXBsZW1lbnRzIGVjIG11bHRpcGxpY2F0aW9uIHVzaW5nIHByZWNvbXB1dGVkIHRhYmxlcyBhbmQgdy1hcnkgbm9uLWFkamFjZW50IGZvcm0uXG4gICAgICAgICAqIEBwYXJhbSBXIHdpbmRvdyBzaXplXG4gICAgICAgICAqIEBwYXJhbSBwcmVjb21wdXRlcyBwcmVjb21wdXRlZCB0YWJsZXNcbiAgICAgICAgICogQHBhcmFtIG4gc2NhbGFyICh3ZSBkb24ndCBjaGVjayBoZXJlLCBidXQgc2hvdWxkIGJlIGxlc3MgdGhhbiBjdXJ2ZSBvcmRlcilcbiAgICAgICAgICogQHJldHVybnMgcmVhbCBhbmQgZmFrZSAoZm9yIGNvbnN0LXRpbWUpIHBvaW50c1xuICAgICAgICAgKi9cbiAgICAgICAgd05BRihXLCBwcmVjb21wdXRlcywgbikge1xuICAgICAgICAgICAgLy8gVE9ETzogbWF5YmUgY2hlY2sgdGhhdCBzY2FsYXIgaXMgbGVzcyB0aGFuIGdyb3VwIG9yZGVyPyB3TkFGIGJlaGF2aW91cyBpcyB1bmRlZmluZWQgb3RoZXJ3aXNlXG4gICAgICAgICAgICAvLyBCdXQgbmVlZCB0byBjYXJlZnVsbHkgcmVtb3ZlIG90aGVyIGNoZWNrcyBiZWZvcmUgd05BRi4gT1JERVIgPT0gYml0cyBoZXJlXG4gICAgICAgICAgICBjb25zdCB7IHdpbmRvd3MsIHdpbmRvd1NpemUgfSA9IG9wdHMoVyk7XG4gICAgICAgICAgICBsZXQgcCA9IGMuWkVSTztcbiAgICAgICAgICAgIGxldCBmID0gYy5CQVNFO1xuICAgICAgICAgICAgY29uc3QgbWFzayA9IEJpZ0ludCgyICoqIFcgLSAxKTsgLy8gQ3JlYXRlIG1hc2sgd2l0aCBXIG9uZXM6IDBiMTExMSBmb3IgVz00IGV0Yy5cbiAgICAgICAgICAgIGNvbnN0IG1heE51bWJlciA9IDIgKiogVztcbiAgICAgICAgICAgIGNvbnN0IHNoaWZ0QnkgPSBCaWdJbnQoVyk7XG4gICAgICAgICAgICBmb3IgKGxldCB3aW5kb3cgPSAwOyB3aW5kb3cgPCB3aW5kb3dzOyB3aW5kb3crKykge1xuICAgICAgICAgICAgICAgIGNvbnN0IG9mZnNldCA9IHdpbmRvdyAqIHdpbmRvd1NpemU7XG4gICAgICAgICAgICAgICAgLy8gRXh0cmFjdCBXIGJpdHMuXG4gICAgICAgICAgICAgICAgbGV0IHdiaXRzID0gTnVtYmVyKG4gJiBtYXNrKTtcbiAgICAgICAgICAgICAgICAvLyBTaGlmdCBudW1iZXIgYnkgVyBiaXRzLlxuICAgICAgICAgICAgICAgIG4gPj49IHNoaWZ0Qnk7XG4gICAgICAgICAgICAgICAgLy8gSWYgdGhlIGJpdHMgYXJlIGJpZ2dlciB0aGFuIG1heCBzaXplLCB3ZSdsbCBzcGxpdCB0aG9zZS5cbiAgICAgICAgICAgICAgICAvLyArMjI0ID0+IDI1NiAtIDMyXG4gICAgICAgICAgICAgICAgaWYgKHdiaXRzID4gd2luZG93U2l6ZSkge1xuICAgICAgICAgICAgICAgICAgICB3Yml0cyAtPSBtYXhOdW1iZXI7XG4gICAgICAgICAgICAgICAgICAgIG4gKz0gXzFuO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBUaGlzIGNvZGUgd2FzIGZpcnN0IHdyaXR0ZW4gd2l0aCBhc3N1bXB0aW9uIHRoYXQgJ2YnIGFuZCAncCcgd2lsbCBuZXZlciBiZSBpbmZpbml0eSBwb2ludDpcbiAgICAgICAgICAgICAgICAvLyBzaW5jZSBlYWNoIGFkZGl0aW9uIGlzIG11bHRpcGxpZWQgYnkgMiAqKiBXLCBpdCBjYW5ub3QgY2FuY2VsIGVhY2ggb3RoZXIuIEhvd2V2ZXIsXG4gICAgICAgICAgICAgICAgLy8gdGhlcmUgaXMgbmVnYXRlIG5vdzogaXQgaXMgcG9zc2libGUgdGhhdCBuZWdhdGVkIGVsZW1lbnQgZnJvbSBsb3cgdmFsdWVcbiAgICAgICAgICAgICAgICAvLyB3b3VsZCBiZSB0aGUgc2FtZSBhcyBoaWdoIGVsZW1lbnQsIHdoaWNoIHdpbGwgY3JlYXRlIGNhcnJ5IGludG8gbmV4dCB3aW5kb3cuXG4gICAgICAgICAgICAgICAgLy8gSXQncyBub3Qgb2J2aW91cyBob3cgdGhpcyBjYW4gZmFpbCwgYnV0IHN0aWxsIHdvcnRoIGludmVzdGlnYXRpbmcgbGF0ZXIuXG4gICAgICAgICAgICAgICAgLy8gQ2hlY2sgaWYgd2UncmUgb250byBaZXJvIHBvaW50LlxuICAgICAgICAgICAgICAgIC8vIEFkZCByYW5kb20gcG9pbnQgaW5zaWRlIGN1cnJlbnQgd2luZG93IHRvIGYuXG4gICAgICAgICAgICAgICAgY29uc3Qgb2Zmc2V0MSA9IG9mZnNldDtcbiAgICAgICAgICAgICAgICBjb25zdCBvZmZzZXQyID0gb2Zmc2V0ICsgTWF0aC5hYnMod2JpdHMpIC0gMTsgLy8gLTEgYmVjYXVzZSB3ZSBza2lwIHplcm9cbiAgICAgICAgICAgICAgICBjb25zdCBjb25kMSA9IHdpbmRvdyAlIDIgIT09IDA7XG4gICAgICAgICAgICAgICAgY29uc3QgY29uZDIgPSB3Yml0cyA8IDA7XG4gICAgICAgICAgICAgICAgaWYgKHdiaXRzID09PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFRoZSBtb3N0IGltcG9ydGFudCBwYXJ0IGZvciBjb25zdC10aW1lIGdldFB1YmxpY0tleVxuICAgICAgICAgICAgICAgICAgICBmID0gZi5hZGQoY29uc3RUaW1lTmVnYXRlKGNvbmQxLCBwcmVjb21wdXRlc1tvZmZzZXQxXSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgcCA9IHAuYWRkKGNvbnN0VGltZU5lZ2F0ZShjb25kMiwgcHJlY29tcHV0ZXNbb2Zmc2V0Ml0pKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBKSVQtY29tcGlsZXIgc2hvdWxkIG5vdCBlbGltaW5hdGUgZiBoZXJlLCBzaW5jZSBpdCB3aWxsIGxhdGVyIGJlIHVzZWQgaW4gbm9ybWFsaXplWigpXG4gICAgICAgICAgICAvLyBFdmVuIGlmIHRoZSB2YXJpYWJsZSBpcyBzdGlsbCB1bnVzZWQsIHRoZXJlIGFyZSBzb21lIGNoZWNrcyB3aGljaCB3aWxsXG4gICAgICAgICAgICAvLyB0aHJvdyBhbiBleGNlcHRpb24sIHNvIGNvbXBpbGVyIG5lZWRzIHRvIHByb3ZlIHRoZXkgd29uJ3QgaGFwcGVuLCB3aGljaCBpcyBoYXJkLlxuICAgICAgICAgICAgLy8gQXQgdGhpcyBwb2ludCB0aGVyZSBpcyBhIHdheSB0byBGIGJlIGluZmluaXR5LXBvaW50IGV2ZW4gaWYgcCBpcyBub3QsXG4gICAgICAgICAgICAvLyB3aGljaCBtYWtlcyBpdCBsZXNzIGNvbnN0LXRpbWU6IGFyb3VuZCAxIGJpZ2ludCBtdWx0aXBseS5cbiAgICAgICAgICAgIHJldHVybiB7IHAsIGYgfTtcbiAgICAgICAgfSxcbiAgICAgICAgd05BRkNhY2hlZChQLCBwcmVjb21wdXRlc01hcCwgbiwgdHJhbnNmb3JtKSB7XG4gICAgICAgICAgICAvLyBAdHMtaWdub3JlXG4gICAgICAgICAgICBjb25zdCBXID0gUC5fV0lORE9XX1NJWkUgfHwgMTtcbiAgICAgICAgICAgIC8vIENhbGN1bGF0ZSBwcmVjb21wdXRlcyBvbiBhIGZpcnN0IHJ1biwgcmV1c2UgdGhlbSBhZnRlclxuICAgICAgICAgICAgbGV0IGNvbXAgPSBwcmVjb21wdXRlc01hcC5nZXQoUCk7XG4gICAgICAgICAgICBpZiAoIWNvbXApIHtcbiAgICAgICAgICAgICAgICBjb21wID0gdGhpcy5wcmVjb21wdXRlV2luZG93KFAsIFcpO1xuICAgICAgICAgICAgICAgIGlmIChXICE9PSAxKSB7XG4gICAgICAgICAgICAgICAgICAgIHByZWNvbXB1dGVzTWFwLnNldChQLCB0cmFuc2Zvcm0oY29tcCkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB0aGlzLndOQUYoVywgY29tcCwgbik7XG4gICAgICAgIH0sXG4gICAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZUJhc2ljKGN1cnZlKSB7XG4gICAgdmFsaWRhdGVGaWVsZChjdXJ2ZS5GcCk7XG4gICAgdmFsaWRhdGVPYmplY3QoY3VydmUsIHtcbiAgICAgICAgbjogJ2JpZ2ludCcsXG4gICAgICAgIGg6ICdiaWdpbnQnLFxuICAgICAgICBHeDogJ2ZpZWxkJyxcbiAgICAgICAgR3k6ICdmaWVsZCcsXG4gICAgfSwge1xuICAgICAgICBuQml0TGVuZ3RoOiAnaXNTYWZlSW50ZWdlcicsXG4gICAgICAgIG5CeXRlTGVuZ3RoOiAnaXNTYWZlSW50ZWdlcicsXG4gICAgfSk7XG4gICAgLy8gU2V0IGRlZmF1bHRzXG4gICAgcmV0dXJuIE9iamVjdC5mcmVlemUoe1xuICAgICAgICAuLi5uTGVuZ3RoKGN1cnZlLm4sIGN1cnZlLm5CaXRMZW5ndGgpLFxuICAgICAgICAuLi5jdXJ2ZSxcbiAgICAgICAgLi4ueyBwOiBjdXJ2ZS5GcC5PUkRFUiB9LFxuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3VydmUuanMubWFwIl0sIm5hbWVzIjpbInZhbGlkYXRlRmllbGQiLCJuTGVuZ3RoIiwidmFsaWRhdGVPYmplY3QiLCJfMG4iLCJCaWdJbnQiLCJfMW4iLCJ3TkFGIiwiYyIsImJpdHMiLCJjb25zdFRpbWVOZWdhdGUiLCJjb25kaXRpb24iLCJpdGVtIiwibmVnIiwibmVnYXRlIiwib3B0cyIsIlciLCJ3aW5kb3dzIiwiTWF0aCIsImNlaWwiLCJ3aW5kb3dTaXplIiwidW5zYWZlTGFkZGVyIiwiZWxtIiwibiIsInAiLCJaRVJPIiwiZCIsImFkZCIsImRvdWJsZSIsInByZWNvbXB1dGVXaW5kb3ciLCJwb2ludHMiLCJiYXNlIiwid2luZG93IiwicHVzaCIsImkiLCJwcmVjb21wdXRlcyIsImYiLCJCQVNFIiwibWFzayIsIm1heE51bWJlciIsInNoaWZ0QnkiLCJvZmZzZXQiLCJ3Yml0cyIsIk51bWJlciIsIm9mZnNldDEiLCJvZmZzZXQyIiwiYWJzIiwiY29uZDEiLCJjb25kMiIsIndOQUZDYWNoZWQiLCJQIiwicHJlY29tcHV0ZXNNYXAiLCJ0cmFuc2Zvcm0iLCJfV0lORE9XX1NJWkUiLCJjb21wIiwiZ2V0Iiwic2V0IiwidmFsaWRhdGVCYXNpYyIsImN1cnZlIiwiRnAiLCJoIiwiR3giLCJHeSIsIm5CaXRMZW5ndGgiLCJuQnl0ZUxlbmd0aCIsIk9iamVjdCIsImZyZWV6ZSIsIk9SREVSIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/abstract/curve.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/abstract/hash-to-curve.js":
/*!******************************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/hash-to-curve.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   expand_message_xmd: () => (/* binding */ expand_message_xmd),\n/* harmony export */   expand_message_xof: () => (/* binding */ expand_message_xof),\n/* harmony export */   hash_to_field: () => (/* binding */ hash_to_field),\n/* harmony export */   isogenyMap: () => (/* binding */ isogenyMap)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n\n\nfunction validateDST(dst) {\n    if (dst instanceof Uint8Array) return dst;\n    if (typeof dst === \"string\") return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)(dst);\n    throw new Error(\"DST must be Uint8Array or string\");\n}\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE;\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value, length) {\n    if (value < 0 || value >= 1 << 8 * length) {\n        throw new Error(`bad I2OSP call: value=${value} length=${length}`);\n    }\n    const res = Array.from({\n        length\n    }).fill(0);\n    for(let i = length - 1; i >= 0; i--){\n        res[i] = value & 0xff;\n        value >>>= 8;\n    }\n    return new Uint8Array(res);\n}\nfunction strxor(a, b) {\n    const arr = new Uint8Array(a.length);\n    for(let i = 0; i < a.length; i++){\n        arr[i] = a[i] ^ b[i];\n    }\n    return arr;\n}\nfunction isBytes(item) {\n    if (!(item instanceof Uint8Array)) throw new Error(\"Uint8Array expected\");\n}\nfunction isNum(item) {\n    if (!Number.isSafeInteger(item)) throw new Error(\"number expected\");\n}\n// Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits\n// https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1\nfunction expand_message_xmd(msg, DST, lenInBytes, H) {\n    isBytes(msg);\n    isBytes(DST);\n    isNum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    if (DST.length > 255) DST = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)(\"H2C-OVERSIZE-DST-\"), DST));\n    const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n    const ell = Math.ceil(lenInBytes / b_in_bytes);\n    if (ell > 255) throw new Error(\"Invalid xmd length\");\n    const DST_prime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(DST, i2osp(DST.length, 1));\n    const Z_pad = i2osp(0, r_in_bytes);\n    const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n    const b = new Array(ell);\n    const b_0 = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n    b[0] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(b_0, i2osp(1, 1), DST_prime));\n    for(let i = 1; i <= ell; i++){\n        const args = [\n            strxor(b_0, b[i - 1]),\n            i2osp(i + 1, 1),\n            DST_prime\n        ];\n        b[i] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...args));\n    }\n    const pseudo_random_bytes = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...b);\n    return pseudo_random_bytes.slice(0, lenInBytes);\n}\n// Produces a uniformly random byte string using an extendable-output function (XOF) H.\n// 1. The collision resistance of H MUST be at least k bits.\n// 2. H MUST be an XOF that has been proved indifferentiable from\n//    a random oracle under a reasonable cryptographic assumption.\n// https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2\nfunction expand_message_xof(msg, DST, lenInBytes, k, H) {\n    isBytes(msg);\n    isBytes(DST);\n    isNum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n    if (DST.length > 255) {\n        const dkLen = Math.ceil(2 * k / 8);\n        DST = H.create({\n            dkLen\n        }).update((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)(\"H2C-OVERSIZE-DST-\")).update(DST).digest();\n    }\n    if (lenInBytes > 65535 || DST.length > 255) throw new Error(\"expand_message_xof: invalid lenInBytes\");\n    return H.create({\n        dkLen: lenInBytes\n    }).update(msg).update(i2osp(lenInBytes, 2))// 2. DST_prime = DST || I2OSP(len(DST), 1)\n    .update(DST).update(i2osp(DST.length, 1)).digest();\n}\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F\n * https://www.rfc-editor.org/rfc/rfc9380#section-5.2\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */ function hash_to_field(msg, count, options) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(options, {\n        DST: \"stringOrUint8Array\",\n        p: \"bigint\",\n        m: \"isSafeInteger\",\n        k: \"isSafeInteger\",\n        hash: \"hash\"\n    });\n    const { p, k, m, hash, expand, DST: _DST } = options;\n    isBytes(msg);\n    isNum(count);\n    const DST = validateDST(_DST);\n    const log2p = p.toString(2).length;\n    const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n    const len_in_bytes = count * m * L;\n    let prb; // pseudo_random_bytes\n    if (expand === \"xmd\") {\n        prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n    } else if (expand === \"xof\") {\n        prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n    } else if (expand === \"_internal_pass\") {\n        // for internal tests only\n        prb = msg;\n    } else {\n        throw new Error('expand must be \"xmd\" or \"xof\"');\n    }\n    const u = new Array(count);\n    for(let i = 0; i < count; i++){\n        const e = new Array(m);\n        for(let j = 0; j < m; j++){\n            const elm_offset = L * (j + i * m);\n            const tv = prb.subarray(elm_offset, elm_offset + L);\n            e[j] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.mod)(os2ip(tv), p);\n        }\n        u[i] = e;\n    }\n    return u;\n}\nfunction isogenyMap(field, map) {\n    // Make same order as in spec\n    const COEFF = map.map((i)=>Array.from(i).reverse());\n    return (x, y)=>{\n        const [xNum, xDen, yNum, yDen] = COEFF.map((val)=>val.reduce((acc, i)=>field.add(field.mul(acc, x), i)));\n        x = field.div(xNum, xDen); // xNum / xDen\n        y = field.mul(y, field.div(yNum, yDen)); // y * (yNum / yDev)\n        return {\n            x,\n            y\n        };\n    };\n}\nfunction createHasher(Point, mapToCurve, def) {\n    if (typeof mapToCurve !== \"function\") throw new Error(\"mapToCurve() must be defined\");\n    return {\n        // Encodes byte string to elliptic curve.\n        // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        hashToCurve (msg, options) {\n            const u = hash_to_field(msg, 2, {\n                ...def,\n                DST: def.DST,\n                ...options\n            });\n            const u0 = Point.fromAffine(mapToCurve(u[0]));\n            const u1 = Point.fromAffine(mapToCurve(u[1]));\n            const P = u0.add(u1).clearCofactor();\n            P.assertValidity();\n            return P;\n        },\n        // Encodes byte string to elliptic curve.\n        // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        encodeToCurve (msg, options) {\n            const u = hash_to_field(msg, 1, {\n                ...def,\n                DST: def.encodeDST,\n                ...options\n            });\n            const P = Point.fromAffine(mapToCurve(u[0])).clearCofactor();\n            P.assertValidity();\n            return P;\n        }\n    };\n} //# sourceMappingURL=hash-to-curve.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vYWJzdHJhY3QvaGFzaC10by1jdXJ2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQW1DO0FBQ29EO0FBQ3ZGLFNBQVNLLFlBQVlDLEdBQUc7SUFDcEIsSUFBSUEsZUFBZUMsWUFDZixPQUFPRDtJQUNYLElBQUksT0FBT0EsUUFBUSxVQUNmLE9BQU9ILHNEQUFXQSxDQUFDRztJQUN2QixNQUFNLElBQUlFLE1BQU07QUFDcEI7QUFDQSw2RkFBNkY7QUFDN0YsTUFBTUMsUUFBUVIsc0RBQWVBO0FBQzdCLDRDQUE0QztBQUM1QyxTQUFTUyxNQUFNQyxLQUFLLEVBQUVDLE1BQU07SUFDeEIsSUFBSUQsUUFBUSxLQUFLQSxTQUFTLEtBQU0sSUFBSUMsUUFBUztRQUN6QyxNQUFNLElBQUlKLE1BQU0sQ0FBQyxzQkFBc0IsRUFBRUcsTUFBTSxRQUFRLEVBQUVDLE9BQU8sQ0FBQztJQUNyRTtJQUNBLE1BQU1DLE1BQU1DLE1BQU1DLElBQUksQ0FBQztRQUFFSDtJQUFPLEdBQUdJLElBQUksQ0FBQztJQUN4QyxJQUFLLElBQUlDLElBQUlMLFNBQVMsR0FBR0ssS0FBSyxHQUFHQSxJQUFLO1FBQ2xDSixHQUFHLENBQUNJLEVBQUUsR0FBR04sUUFBUTtRQUNqQkEsV0FBVztJQUNmO0lBQ0EsT0FBTyxJQUFJSixXQUFXTTtBQUMxQjtBQUNBLFNBQVNLLE9BQU9DLENBQUMsRUFBRUMsQ0FBQztJQUNoQixNQUFNQyxNQUFNLElBQUlkLFdBQVdZLEVBQUVQLE1BQU07SUFDbkMsSUFBSyxJQUFJSyxJQUFJLEdBQUdBLElBQUlFLEVBQUVQLE1BQU0sRUFBRUssSUFBSztRQUMvQkksR0FBRyxDQUFDSixFQUFFLEdBQUdFLENBQUMsQ0FBQ0YsRUFBRSxHQUFHRyxDQUFDLENBQUNILEVBQUU7SUFDeEI7SUFDQSxPQUFPSTtBQUNYO0FBQ0EsU0FBU0MsUUFBUUMsSUFBSTtJQUNqQixJQUFJLENBQUVBLENBQUFBLGdCQUFnQmhCLFVBQVMsR0FDM0IsTUFBTSxJQUFJQyxNQUFNO0FBQ3hCO0FBQ0EsU0FBU2dCLE1BQU1ELElBQUk7SUFDZixJQUFJLENBQUNFLE9BQU9DLGFBQWEsQ0FBQ0gsT0FDdEIsTUFBTSxJQUFJZixNQUFNO0FBQ3hCO0FBQ0Esb0dBQW9HO0FBQ3BHLHVEQUF1RDtBQUNoRCxTQUFTbUIsbUJBQW1CQyxHQUFHLEVBQUVDLEdBQUcsRUFBRUMsVUFBVSxFQUFFQyxDQUFDO0lBQ3REVCxRQUFRTTtJQUNSTixRQUFRTztJQUNSTCxNQUFNTTtJQUNOLHVEQUF1RDtJQUN2RCxJQUFJRCxJQUFJakIsTUFBTSxHQUFHLEtBQ2JpQixNQUFNRSxFQUFFN0Isc0RBQVdBLENBQUNDLHNEQUFXQSxDQUFDLHNCQUFzQjBCO0lBQzFELE1BQU0sRUFBRUcsV0FBV0MsVUFBVSxFQUFFQyxVQUFVQyxVQUFVLEVBQUUsR0FBR0o7SUFDeEQsTUFBTUssTUFBTUMsS0FBS0MsSUFBSSxDQUFDUixhQUFhRztJQUNuQyxJQUFJRyxNQUFNLEtBQ04sTUFBTSxJQUFJNUIsTUFBTTtJQUNwQixNQUFNK0IsWUFBWXJDLHNEQUFXQSxDQUFDMkIsS0FBS25CLE1BQU1tQixJQUFJakIsTUFBTSxFQUFFO0lBQ3JELE1BQU00QixRQUFROUIsTUFBTSxHQUFHeUI7SUFDdkIsTUFBTU0sWUFBWS9CLE1BQU1vQixZQUFZLElBQUksbUJBQW1CO0lBQzNELE1BQU1WLElBQUksSUFBSU4sTUFBTXNCO0lBQ3BCLE1BQU1NLE1BQU1YLEVBQUU3QixzREFBV0EsQ0FBQ3NDLE9BQU9aLEtBQUthLFdBQVcvQixNQUFNLEdBQUcsSUFBSTZCO0lBQzlEbkIsQ0FBQyxDQUFDLEVBQUUsR0FBR1csRUFBRTdCLHNEQUFXQSxDQUFDd0MsS0FBS2hDLE1BQU0sR0FBRyxJQUFJNkI7SUFDdkMsSUFBSyxJQUFJdEIsSUFBSSxHQUFHQSxLQUFLbUIsS0FBS25CLElBQUs7UUFDM0IsTUFBTTBCLE9BQU87WUFBQ3pCLE9BQU93QixLQUFLdEIsQ0FBQyxDQUFDSCxJQUFJLEVBQUU7WUFBR1AsTUFBTU8sSUFBSSxHQUFHO1lBQUlzQjtTQUFVO1FBQ2hFbkIsQ0FBQyxDQUFDSCxFQUFFLEdBQUdjLEVBQUU3QixzREFBV0EsSUFBSXlDO0lBQzVCO0lBQ0EsTUFBTUMsc0JBQXNCMUMsc0RBQVdBLElBQUlrQjtJQUMzQyxPQUFPd0Isb0JBQW9CQyxLQUFLLENBQUMsR0FBR2Y7QUFDeEM7QUFDQSx1RkFBdUY7QUFDdkYsNERBQTREO0FBQzVELGlFQUFpRTtBQUNqRSxrRUFBa0U7QUFDbEUsdURBQXVEO0FBQ2hELFNBQVNnQixtQkFBbUJsQixHQUFHLEVBQUVDLEdBQUcsRUFBRUMsVUFBVSxFQUFFaUIsQ0FBQyxFQUFFaEIsQ0FBQztJQUN6RFQsUUFBUU07SUFDUk4sUUFBUU87SUFDUkwsTUFBTU07SUFDTix1REFBdUQ7SUFDdkQsb0ZBQW9GO0lBQ3BGLElBQUlELElBQUlqQixNQUFNLEdBQUcsS0FBSztRQUNsQixNQUFNb0MsUUFBUVgsS0FBS0MsSUFBSSxDQUFDLElBQUtTLElBQUs7UUFDbENsQixNQUFNRSxFQUFFa0IsTUFBTSxDQUFDO1lBQUVEO1FBQU0sR0FBR0UsTUFBTSxDQUFDL0Msc0RBQVdBLENBQUMsc0JBQXNCK0MsTUFBTSxDQUFDckIsS0FBS3NCLE1BQU07SUFDekY7SUFDQSxJQUFJckIsYUFBYSxTQUFTRCxJQUFJakIsTUFBTSxHQUFHLEtBQ25DLE1BQU0sSUFBSUosTUFBTTtJQUNwQixPQUFRdUIsRUFBRWtCLE1BQU0sQ0FBQztRQUFFRCxPQUFPbEI7SUFBVyxHQUNoQ29CLE1BQU0sQ0FBQ3RCLEtBQ1BzQixNQUFNLENBQUN4QyxNQUFNb0IsWUFBWSxHQUMxQiwyQ0FBMkM7S0FDMUNvQixNQUFNLENBQUNyQixLQUNQcUIsTUFBTSxDQUFDeEMsTUFBTW1CLElBQUlqQixNQUFNLEVBQUUsSUFDekJ1QyxNQUFNO0FBQ2Y7QUFDQTs7Ozs7OztDQU9DLEdBQ00sU0FBU0MsY0FBY3hCLEdBQUcsRUFBRXlCLEtBQUssRUFBRUMsT0FBTztJQUM3Q2xELHlEQUFjQSxDQUFDa0QsU0FBUztRQUNwQnpCLEtBQUs7UUFDTDBCLEdBQUc7UUFDSEMsR0FBRztRQUNIVCxHQUFHO1FBQ0hVLE1BQU07SUFDVjtJQUNBLE1BQU0sRUFBRUYsQ0FBQyxFQUFFUixDQUFDLEVBQUVTLENBQUMsRUFBRUMsSUFBSSxFQUFFQyxNQUFNLEVBQUU3QixLQUFLOEIsSUFBSSxFQUFFLEdBQUdMO0lBQzdDaEMsUUFBUU07SUFDUkosTUFBTTZCO0lBQ04sTUFBTXhCLE1BQU14QixZQUFZc0Q7SUFDeEIsTUFBTUMsUUFBUUwsRUFBRU0sUUFBUSxDQUFDLEdBQUdqRCxNQUFNO0lBQ2xDLE1BQU1rRCxJQUFJekIsS0FBS0MsSUFBSSxDQUFDLENBQUNzQixRQUFRYixDQUFBQSxJQUFLLElBQUksdUNBQXVDO0lBQzdFLE1BQU1nQixlQUFlVixRQUFRRyxJQUFJTTtJQUNqQyxJQUFJRSxLQUFLLHNCQUFzQjtJQUMvQixJQUFJTixXQUFXLE9BQU87UUFDbEJNLE1BQU1yQyxtQkFBbUJDLEtBQUtDLEtBQUtrQyxjQUFjTjtJQUNyRCxPQUNLLElBQUlDLFdBQVcsT0FBTztRQUN2Qk0sTUFBTWxCLG1CQUFtQmxCLEtBQUtDLEtBQUtrQyxjQUFjaEIsR0FBR1U7SUFDeEQsT0FDSyxJQUFJQyxXQUFXLGtCQUFrQjtRQUNsQywwQkFBMEI7UUFDMUJNLE1BQU1wQztJQUNWLE9BQ0s7UUFDRCxNQUFNLElBQUlwQixNQUFNO0lBQ3BCO0lBQ0EsTUFBTXlELElBQUksSUFBSW5ELE1BQU11QztJQUNwQixJQUFLLElBQUlwQyxJQUFJLEdBQUdBLElBQUlvQyxPQUFPcEMsSUFBSztRQUM1QixNQUFNaUQsSUFBSSxJQUFJcEQsTUFBTTBDO1FBQ3BCLElBQUssSUFBSVcsSUFBSSxHQUFHQSxJQUFJWCxHQUFHVyxJQUFLO1lBQ3hCLE1BQU1DLGFBQWFOLElBQUtLLENBQUFBLElBQUlsRCxJQUFJdUMsQ0FBQUE7WUFDaEMsTUFBTWEsS0FBS0wsSUFBSU0sUUFBUSxDQUFDRixZQUFZQSxhQUFhTjtZQUNqREksQ0FBQyxDQUFDQyxFQUFFLEdBQUduRSxnREFBR0EsQ0FBQ1MsTUFBTTRELEtBQUtkO1FBQzFCO1FBQ0FVLENBQUMsQ0FBQ2hELEVBQUUsR0FBR2lEO0lBQ1g7SUFDQSxPQUFPRDtBQUNYO0FBQ08sU0FBU00sV0FBV0MsS0FBSyxFQUFFQyxHQUFHO0lBQ2pDLDZCQUE2QjtJQUM3QixNQUFNQyxRQUFRRCxJQUFJQSxHQUFHLENBQUMsQ0FBQ3hELElBQU1ILE1BQU1DLElBQUksQ0FBQ0UsR0FBRzBELE9BQU87SUFDbEQsT0FBTyxDQUFDQyxHQUFHQztRQUNQLE1BQU0sQ0FBQ0MsTUFBTUMsTUFBTUMsTUFBTUMsS0FBSyxHQUFHUCxNQUFNRCxHQUFHLENBQUMsQ0FBQ1MsTUFBUUEsSUFBSUMsTUFBTSxDQUFDLENBQUNDLEtBQUtuRSxJQUFNdUQsTUFBTWEsR0FBRyxDQUFDYixNQUFNYyxHQUFHLENBQUNGLEtBQUtSLElBQUkzRDtRQUN4RzJELElBQUlKLE1BQU1lLEdBQUcsQ0FBQ1QsTUFBTUMsT0FBTyxjQUFjO1FBQ3pDRixJQUFJTCxNQUFNYyxHQUFHLENBQUNULEdBQUdMLE1BQU1lLEdBQUcsQ0FBQ1AsTUFBTUMsUUFBUSxvQkFBb0I7UUFDN0QsT0FBTztZQUFFTDtZQUFHQztRQUFFO0lBQ2xCO0FBQ0o7QUFDTyxTQUFTVyxhQUFhQyxLQUFLLEVBQUVDLFVBQVUsRUFBRUMsR0FBRztJQUMvQyxJQUFJLE9BQU9ELGVBQWUsWUFDdEIsTUFBTSxJQUFJbEYsTUFBTTtJQUNwQixPQUFPO1FBQ0gseUNBQXlDO1FBQ3pDLHNFQUFzRTtRQUN0RW9GLGFBQVloRSxHQUFHLEVBQUUwQixPQUFPO1lBQ3BCLE1BQU1XLElBQUliLGNBQWN4QixLQUFLLEdBQUc7Z0JBQUUsR0FBRytELEdBQUc7Z0JBQUU5RCxLQUFLOEQsSUFBSTlELEdBQUc7Z0JBQUUsR0FBR3lCLE9BQU87WUFBQztZQUNuRSxNQUFNdUMsS0FBS0osTUFBTUssVUFBVSxDQUFDSixXQUFXekIsQ0FBQyxDQUFDLEVBQUU7WUFDM0MsTUFBTThCLEtBQUtOLE1BQU1LLFVBQVUsQ0FBQ0osV0FBV3pCLENBQUMsQ0FBQyxFQUFFO1lBQzNDLE1BQU0rQixJQUFJSCxHQUFHUixHQUFHLENBQUNVLElBQUlFLGFBQWE7WUFDbENELEVBQUVFLGNBQWM7WUFDaEIsT0FBT0Y7UUFDWDtRQUNBLHlDQUF5QztRQUN6Qyx3RUFBd0U7UUFDeEVHLGVBQWN2RSxHQUFHLEVBQUUwQixPQUFPO1lBQ3RCLE1BQU1XLElBQUliLGNBQWN4QixLQUFLLEdBQUc7Z0JBQUUsR0FBRytELEdBQUc7Z0JBQUU5RCxLQUFLOEQsSUFBSVMsU0FBUztnQkFBRSxHQUFHOUMsT0FBTztZQUFDO1lBQ3pFLE1BQU0wQyxJQUFJUCxNQUFNSyxVQUFVLENBQUNKLFdBQVd6QixDQUFDLENBQUMsRUFBRSxHQUFHZ0MsYUFBYTtZQUMxREQsRUFBRUUsY0FBYztZQUNoQixPQUFPRjtRQUNYO0lBQ0o7QUFDSixFQUNBLHlDQUF5QyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHVzLXN3YXAtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vYWJzdHJhY3QvaGFzaC10by1jdXJ2ZS5qcz8yMGQyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1vZCB9IGZyb20gJy4vbW9kdWxhci5qcyc7XG5pbXBvcnQgeyBieXRlc1RvTnVtYmVyQkUsIGNvbmNhdEJ5dGVzLCB1dGY4VG9CeXRlcywgdmFsaWRhdGVPYmplY3QgfSBmcm9tICcuL3V0aWxzLmpzJztcbmZ1bmN0aW9uIHZhbGlkYXRlRFNUKGRzdCkge1xuICAgIGlmIChkc3QgaW5zdGFuY2VvZiBVaW50OEFycmF5KVxuICAgICAgICByZXR1cm4gZHN0O1xuICAgIGlmICh0eXBlb2YgZHN0ID09PSAnc3RyaW5nJylcbiAgICAgICAgcmV0dXJuIHV0ZjhUb0J5dGVzKGRzdCk7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdEU1QgbXVzdCBiZSBVaW50OEFycmF5IG9yIHN0cmluZycpO1xufVxuLy8gT2N0ZXQgU3RyZWFtIHRvIEludGVnZXIuIFwic3BlY1wiIGltcGxlbWVudGF0aW9uIG9mIG9zMmlwIGlzIDIuNXggc2xvd2VyIHZzIGJ5dGVzVG9OdW1iZXJCRS5cbmNvbnN0IG9zMmlwID0gYnl0ZXNUb051bWJlckJFO1xuLy8gSW50ZWdlciB0byBPY3RldCBTdHJlYW0gKG51bWJlclRvQnl0ZXNCRSlcbmZ1bmN0aW9uIGkyb3NwKHZhbHVlLCBsZW5ndGgpIHtcbiAgICBpZiAodmFsdWUgPCAwIHx8IHZhbHVlID49IDEgPDwgKDggKiBsZW5ndGgpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgYmFkIEkyT1NQIGNhbGw6IHZhbHVlPSR7dmFsdWV9IGxlbmd0aD0ke2xlbmd0aH1gKTtcbiAgICB9XG4gICAgY29uc3QgcmVzID0gQXJyYXkuZnJvbSh7IGxlbmd0aCB9KS5maWxsKDApO1xuICAgIGZvciAobGV0IGkgPSBsZW5ndGggLSAxOyBpID49IDA7IGktLSkge1xuICAgICAgICByZXNbaV0gPSB2YWx1ZSAmIDB4ZmY7XG4gICAgICAgIHZhbHVlID4+Pj0gODtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KHJlcyk7XG59XG5mdW5jdGlvbiBzdHJ4b3IoYSwgYikge1xuICAgIGNvbnN0IGFyciA9IG5ldyBVaW50OEFycmF5KGEubGVuZ3RoKTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGEubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgYXJyW2ldID0gYVtpXSBeIGJbaV07XG4gICAgfVxuICAgIHJldHVybiBhcnI7XG59XG5mdW5jdGlvbiBpc0J5dGVzKGl0ZW0pIHtcbiAgICBpZiAoIShpdGVtIGluc3RhbmNlb2YgVWludDhBcnJheSkpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignVWludDhBcnJheSBleHBlY3RlZCcpO1xufVxuZnVuY3Rpb24gaXNOdW0oaXRlbSkge1xuICAgIGlmICghTnVtYmVyLmlzU2FmZUludGVnZXIoaXRlbSkpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignbnVtYmVyIGV4cGVjdGVkJyk7XG59XG4vLyBQcm9kdWNlcyBhIHVuaWZvcm1seSByYW5kb20gYnl0ZSBzdHJpbmcgdXNpbmcgYSBjcnlwdG9ncmFwaGljIGhhc2ggZnVuY3Rpb24gSCB0aGF0IG91dHB1dHMgYiBiaXRzXG4vLyBodHRwczovL3d3dy5yZmMtZWRpdG9yLm9yZy9yZmMvcmZjOTM4MCNzZWN0aW9uLTUuMy4xXG5leHBvcnQgZnVuY3Rpb24gZXhwYW5kX21lc3NhZ2VfeG1kKG1zZywgRFNULCBsZW5JbkJ5dGVzLCBIKSB7XG4gICAgaXNCeXRlcyhtc2cpO1xuICAgIGlzQnl0ZXMoRFNUKTtcbiAgICBpc051bShsZW5JbkJ5dGVzKTtcbiAgICAvLyBodHRwczovL3d3dy5yZmMtZWRpdG9yLm9yZy9yZmMvcmZjOTM4MCNzZWN0aW9uLTUuMy4zXG4gICAgaWYgKERTVC5sZW5ndGggPiAyNTUpXG4gICAgICAgIERTVCA9IEgoY29uY2F0Qnl0ZXModXRmOFRvQnl0ZXMoJ0gyQy1PVkVSU0laRS1EU1QtJyksIERTVCkpO1xuICAgIGNvbnN0IHsgb3V0cHV0TGVuOiBiX2luX2J5dGVzLCBibG9ja0xlbjogcl9pbl9ieXRlcyB9ID0gSDtcbiAgICBjb25zdCBlbGwgPSBNYXRoLmNlaWwobGVuSW5CeXRlcyAvIGJfaW5fYnl0ZXMpO1xuICAgIGlmIChlbGwgPiAyNTUpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCB4bWQgbGVuZ3RoJyk7XG4gICAgY29uc3QgRFNUX3ByaW1lID0gY29uY2F0Qnl0ZXMoRFNULCBpMm9zcChEU1QubGVuZ3RoLCAxKSk7XG4gICAgY29uc3QgWl9wYWQgPSBpMm9zcCgwLCByX2luX2J5dGVzKTtcbiAgICBjb25zdCBsX2lfYl9zdHIgPSBpMm9zcChsZW5JbkJ5dGVzLCAyKTsgLy8gbGVuX2luX2J5dGVzX3N0clxuICAgIGNvbnN0IGIgPSBuZXcgQXJyYXkoZWxsKTtcbiAgICBjb25zdCBiXzAgPSBIKGNvbmNhdEJ5dGVzKFpfcGFkLCBtc2csIGxfaV9iX3N0ciwgaTJvc3AoMCwgMSksIERTVF9wcmltZSkpO1xuICAgIGJbMF0gPSBIKGNvbmNhdEJ5dGVzKGJfMCwgaTJvc3AoMSwgMSksIERTVF9wcmltZSkpO1xuICAgIGZvciAobGV0IGkgPSAxOyBpIDw9IGVsbDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IGFyZ3MgPSBbc3RyeG9yKGJfMCwgYltpIC0gMV0pLCBpMm9zcChpICsgMSwgMSksIERTVF9wcmltZV07XG4gICAgICAgIGJbaV0gPSBIKGNvbmNhdEJ5dGVzKC4uLmFyZ3MpKTtcbiAgICB9XG4gICAgY29uc3QgcHNldWRvX3JhbmRvbV9ieXRlcyA9IGNvbmNhdEJ5dGVzKC4uLmIpO1xuICAgIHJldHVybiBwc2V1ZG9fcmFuZG9tX2J5dGVzLnNsaWNlKDAsIGxlbkluQnl0ZXMpO1xufVxuLy8gUHJvZHVjZXMgYSB1bmlmb3JtbHkgcmFuZG9tIGJ5dGUgc3RyaW5nIHVzaW5nIGFuIGV4dGVuZGFibGUtb3V0cHV0IGZ1bmN0aW9uIChYT0YpIEguXG4vLyAxLiBUaGUgY29sbGlzaW9uIHJlc2lzdGFuY2Ugb2YgSCBNVVNUIGJlIGF0IGxlYXN0IGsgYml0cy5cbi8vIDIuIEggTVVTVCBiZSBhbiBYT0YgdGhhdCBoYXMgYmVlbiBwcm92ZWQgaW5kaWZmZXJlbnRpYWJsZSBmcm9tXG4vLyAgICBhIHJhbmRvbSBvcmFjbGUgdW5kZXIgYSByZWFzb25hYmxlIGNyeXB0b2dyYXBoaWMgYXNzdW1wdGlvbi5cbi8vIGh0dHBzOi8vd3d3LnJmYy1lZGl0b3Iub3JnL3JmYy9yZmM5MzgwI3NlY3Rpb24tNS4zLjJcbmV4cG9ydCBmdW5jdGlvbiBleHBhbmRfbWVzc2FnZV94b2YobXNnLCBEU1QsIGxlbkluQnl0ZXMsIGssIEgpIHtcbiAgICBpc0J5dGVzKG1zZyk7XG4gICAgaXNCeXRlcyhEU1QpO1xuICAgIGlzTnVtKGxlbkluQnl0ZXMpO1xuICAgIC8vIGh0dHBzOi8vd3d3LnJmYy1lZGl0b3Iub3JnL3JmYy9yZmM5MzgwI3NlY3Rpb24tNS4zLjNcbiAgICAvLyBEU1QgPSBIKCdIMkMtT1ZFUlNJWkUtRFNULScgfHwgYV92ZXJ5X2xvbmdfRFNULCBNYXRoLmNlaWwoKGxlbkluQnl0ZXMgKiBrKSAvIDgpKTtcbiAgICBpZiAoRFNULmxlbmd0aCA+IDI1NSkge1xuICAgICAgICBjb25zdCBka0xlbiA9IE1hdGguY2VpbCgoMiAqIGspIC8gOCk7XG4gICAgICAgIERTVCA9IEguY3JlYXRlKHsgZGtMZW4gfSkudXBkYXRlKHV0ZjhUb0J5dGVzKCdIMkMtT1ZFUlNJWkUtRFNULScpKS51cGRhdGUoRFNUKS5kaWdlc3QoKTtcbiAgICB9XG4gICAgaWYgKGxlbkluQnl0ZXMgPiA2NTUzNSB8fCBEU1QubGVuZ3RoID4gMjU1KVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2V4cGFuZF9tZXNzYWdlX3hvZjogaW52YWxpZCBsZW5JbkJ5dGVzJyk7XG4gICAgcmV0dXJuIChILmNyZWF0ZSh7IGRrTGVuOiBsZW5JbkJ5dGVzIH0pXG4gICAgICAgIC51cGRhdGUobXNnKVxuICAgICAgICAudXBkYXRlKGkyb3NwKGxlbkluQnl0ZXMsIDIpKVxuICAgICAgICAvLyAyLiBEU1RfcHJpbWUgPSBEU1QgfHwgSTJPU1AobGVuKERTVCksIDEpXG4gICAgICAgIC51cGRhdGUoRFNUKVxuICAgICAgICAudXBkYXRlKGkyb3NwKERTVC5sZW5ndGgsIDEpKVxuICAgICAgICAuZGlnZXN0KCkpO1xufVxuLyoqXG4gKiBIYXNoZXMgYXJiaXRyYXJ5LWxlbmd0aCBieXRlIHN0cmluZ3MgdG8gYSBsaXN0IG9mIG9uZSBvciBtb3JlIGVsZW1lbnRzIG9mIGEgZmluaXRlIGZpZWxkIEZcbiAqIGh0dHBzOi8vd3d3LnJmYy1lZGl0b3Iub3JnL3JmYy9yZmM5MzgwI3NlY3Rpb24tNS4yXG4gKiBAcGFyYW0gbXNnIGEgYnl0ZSBzdHJpbmcgY29udGFpbmluZyB0aGUgbWVzc2FnZSB0byBoYXNoXG4gKiBAcGFyYW0gY291bnQgdGhlIG51bWJlciBvZiBlbGVtZW50cyBvZiBGIHRvIG91dHB1dFxuICogQHBhcmFtIG9wdGlvbnMgYHtEU1Q6IHN0cmluZywgcDogYmlnaW50LCBtOiBudW1iZXIsIGs6IG51bWJlciwgZXhwYW5kOiAneG1kJyB8ICd4b2YnLCBoYXNoOiBIfWAsIHNlZSBhYm92ZVxuICogQHJldHVybnMgW3VfMCwgLi4uLCB1Xyhjb3VudCAtIDEpXSwgYSBsaXN0IG9mIGZpZWxkIGVsZW1lbnRzLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFzaF90b19maWVsZChtc2csIGNvdW50LCBvcHRpb25zKSB7XG4gICAgdmFsaWRhdGVPYmplY3Qob3B0aW9ucywge1xuICAgICAgICBEU1Q6ICdzdHJpbmdPclVpbnQ4QXJyYXknLFxuICAgICAgICBwOiAnYmlnaW50JyxcbiAgICAgICAgbTogJ2lzU2FmZUludGVnZXInLFxuICAgICAgICBrOiAnaXNTYWZlSW50ZWdlcicsXG4gICAgICAgIGhhc2g6ICdoYXNoJyxcbiAgICB9KTtcbiAgICBjb25zdCB7IHAsIGssIG0sIGhhc2gsIGV4cGFuZCwgRFNUOiBfRFNUIH0gPSBvcHRpb25zO1xuICAgIGlzQnl0ZXMobXNnKTtcbiAgICBpc051bShjb3VudCk7XG4gICAgY29uc3QgRFNUID0gdmFsaWRhdGVEU1QoX0RTVCk7XG4gICAgY29uc3QgbG9nMnAgPSBwLnRvU3RyaW5nKDIpLmxlbmd0aDtcbiAgICBjb25zdCBMID0gTWF0aC5jZWlsKChsb2cycCArIGspIC8gOCk7IC8vIHNlY3Rpb24gNS4xIG9mIGlldGYgZHJhZnQgbGluayBhYm92ZVxuICAgIGNvbnN0IGxlbl9pbl9ieXRlcyA9IGNvdW50ICogbSAqIEw7XG4gICAgbGV0IHByYjsgLy8gcHNldWRvX3JhbmRvbV9ieXRlc1xuICAgIGlmIChleHBhbmQgPT09ICd4bWQnKSB7XG4gICAgICAgIHByYiA9IGV4cGFuZF9tZXNzYWdlX3htZChtc2csIERTVCwgbGVuX2luX2J5dGVzLCBoYXNoKTtcbiAgICB9XG4gICAgZWxzZSBpZiAoZXhwYW5kID09PSAneG9mJykge1xuICAgICAgICBwcmIgPSBleHBhbmRfbWVzc2FnZV94b2YobXNnLCBEU1QsIGxlbl9pbl9ieXRlcywgaywgaGFzaCk7XG4gICAgfVxuICAgIGVsc2UgaWYgKGV4cGFuZCA9PT0gJ19pbnRlcm5hbF9wYXNzJykge1xuICAgICAgICAvLyBmb3IgaW50ZXJuYWwgdGVzdHMgb25seVxuICAgICAgICBwcmIgPSBtc2c7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2V4cGFuZCBtdXN0IGJlIFwieG1kXCIgb3IgXCJ4b2ZcIicpO1xuICAgIH1cbiAgICBjb25zdCB1ID0gbmV3IEFycmF5KGNvdW50KTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGNvdW50OyBpKyspIHtcbiAgICAgICAgY29uc3QgZSA9IG5ldyBBcnJheShtKTtcbiAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCBtOyBqKyspIHtcbiAgICAgICAgICAgIGNvbnN0IGVsbV9vZmZzZXQgPSBMICogKGogKyBpICogbSk7XG4gICAgICAgICAgICBjb25zdCB0diA9IHByYi5zdWJhcnJheShlbG1fb2Zmc2V0LCBlbG1fb2Zmc2V0ICsgTCk7XG4gICAgICAgICAgICBlW2pdID0gbW9kKG9zMmlwKHR2KSwgcCk7XG4gICAgICAgIH1cbiAgICAgICAgdVtpXSA9IGU7XG4gICAgfVxuICAgIHJldHVybiB1O1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzb2dlbnlNYXAoZmllbGQsIG1hcCkge1xuICAgIC8vIE1ha2Ugc2FtZSBvcmRlciBhcyBpbiBzcGVjXG4gICAgY29uc3QgQ09FRkYgPSBtYXAubWFwKChpKSA9PiBBcnJheS5mcm9tKGkpLnJldmVyc2UoKSk7XG4gICAgcmV0dXJuICh4LCB5KSA9PiB7XG4gICAgICAgIGNvbnN0IFt4TnVtLCB4RGVuLCB5TnVtLCB5RGVuXSA9IENPRUZGLm1hcCgodmFsKSA9PiB2YWwucmVkdWNlKChhY2MsIGkpID0+IGZpZWxkLmFkZChmaWVsZC5tdWwoYWNjLCB4KSwgaSkpKTtcbiAgICAgICAgeCA9IGZpZWxkLmRpdih4TnVtLCB4RGVuKTsgLy8geE51bSAvIHhEZW5cbiAgICAgICAgeSA9IGZpZWxkLm11bCh5LCBmaWVsZC5kaXYoeU51bSwgeURlbikpOyAvLyB5ICogKHlOdW0gLyB5RGV2KVxuICAgICAgICByZXR1cm4geyB4LCB5IH07XG4gICAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVIYXNoZXIoUG9pbnQsIG1hcFRvQ3VydmUsIGRlZikge1xuICAgIGlmICh0eXBlb2YgbWFwVG9DdXJ2ZSAhPT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdtYXBUb0N1cnZlKCkgbXVzdCBiZSBkZWZpbmVkJyk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgLy8gRW5jb2RlcyBieXRlIHN0cmluZyB0byBlbGxpcHRpYyBjdXJ2ZS5cbiAgICAgICAgLy8gaGFzaF90b19jdXJ2ZSBmcm9tIGh0dHBzOi8vd3d3LnJmYy1lZGl0b3Iub3JnL3JmYy9yZmM5MzgwI3NlY3Rpb24tM1xuICAgICAgICBoYXNoVG9DdXJ2ZShtc2csIG9wdGlvbnMpIHtcbiAgICAgICAgICAgIGNvbnN0IHUgPSBoYXNoX3RvX2ZpZWxkKG1zZywgMiwgeyAuLi5kZWYsIERTVDogZGVmLkRTVCwgLi4ub3B0aW9ucyB9KTtcbiAgICAgICAgICAgIGNvbnN0IHUwID0gUG9pbnQuZnJvbUFmZmluZShtYXBUb0N1cnZlKHVbMF0pKTtcbiAgICAgICAgICAgIGNvbnN0IHUxID0gUG9pbnQuZnJvbUFmZmluZShtYXBUb0N1cnZlKHVbMV0pKTtcbiAgICAgICAgICAgIGNvbnN0IFAgPSB1MC5hZGQodTEpLmNsZWFyQ29mYWN0b3IoKTtcbiAgICAgICAgICAgIFAuYXNzZXJ0VmFsaWRpdHkoKTtcbiAgICAgICAgICAgIHJldHVybiBQO1xuICAgICAgICB9LFxuICAgICAgICAvLyBFbmNvZGVzIGJ5dGUgc3RyaW5nIHRvIGVsbGlwdGljIGN1cnZlLlxuICAgICAgICAvLyBlbmNvZGVfdG9fY3VydmUgZnJvbSBodHRwczovL3d3dy5yZmMtZWRpdG9yLm9yZy9yZmMvcmZjOTM4MCNzZWN0aW9uLTNcbiAgICAgICAgZW5jb2RlVG9DdXJ2ZShtc2csIG9wdGlvbnMpIHtcbiAgICAgICAgICAgIGNvbnN0IHUgPSBoYXNoX3RvX2ZpZWxkKG1zZywgMSwgeyAuLi5kZWYsIERTVDogZGVmLmVuY29kZURTVCwgLi4ub3B0aW9ucyB9KTtcbiAgICAgICAgICAgIGNvbnN0IFAgPSBQb2ludC5mcm9tQWZmaW5lKG1hcFRvQ3VydmUodVswXSkpLmNsZWFyQ29mYWN0b3IoKTtcbiAgICAgICAgICAgIFAuYXNzZXJ0VmFsaWRpdHkoKTtcbiAgICAgICAgICAgIHJldHVybiBQO1xuICAgICAgICB9LFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1oYXNoLXRvLWN1cnZlLmpzLm1hcCJdLCJuYW1lcyI6WyJtb2QiLCJieXRlc1RvTnVtYmVyQkUiLCJjb25jYXRCeXRlcyIsInV0ZjhUb0J5dGVzIiwidmFsaWRhdGVPYmplY3QiLCJ2YWxpZGF0ZURTVCIsImRzdCIsIlVpbnQ4QXJyYXkiLCJFcnJvciIsIm9zMmlwIiwiaTJvc3AiLCJ2YWx1ZSIsImxlbmd0aCIsInJlcyIsIkFycmF5IiwiZnJvbSIsImZpbGwiLCJpIiwic3RyeG9yIiwiYSIsImIiLCJhcnIiLCJpc0J5dGVzIiwiaXRlbSIsImlzTnVtIiwiTnVtYmVyIiwiaXNTYWZlSW50ZWdlciIsImV4cGFuZF9tZXNzYWdlX3htZCIsIm1zZyIsIkRTVCIsImxlbkluQnl0ZXMiLCJIIiwib3V0cHV0TGVuIiwiYl9pbl9ieXRlcyIsImJsb2NrTGVuIiwicl9pbl9ieXRlcyIsImVsbCIsIk1hdGgiLCJjZWlsIiwiRFNUX3ByaW1lIiwiWl9wYWQiLCJsX2lfYl9zdHIiLCJiXzAiLCJhcmdzIiwicHNldWRvX3JhbmRvbV9ieXRlcyIsInNsaWNlIiwiZXhwYW5kX21lc3NhZ2VfeG9mIiwiayIsImRrTGVuIiwiY3JlYXRlIiwidXBkYXRlIiwiZGlnZXN0IiwiaGFzaF90b19maWVsZCIsImNvdW50Iiwib3B0aW9ucyIsInAiLCJtIiwiaGFzaCIsImV4cGFuZCIsIl9EU1QiLCJsb2cycCIsInRvU3RyaW5nIiwiTCIsImxlbl9pbl9ieXRlcyIsInByYiIsInUiLCJlIiwiaiIsImVsbV9vZmZzZXQiLCJ0diIsInN1YmFycmF5IiwiaXNvZ2VueU1hcCIsImZpZWxkIiwibWFwIiwiQ09FRkYiLCJyZXZlcnNlIiwieCIsInkiLCJ4TnVtIiwieERlbiIsInlOdW0iLCJ5RGVuIiwidmFsIiwicmVkdWNlIiwiYWNjIiwiYWRkIiwibXVsIiwiZGl2IiwiY3JlYXRlSGFzaGVyIiwiUG9pbnQiLCJtYXBUb0N1cnZlIiwiZGVmIiwiaGFzaFRvQ3VydmUiLCJ1MCIsImZyb21BZmZpbmUiLCJ1MSIsIlAiLCJjbGVhckNvZmFjdG9yIiwiYXNzZXJ0VmFsaWRpdHkiLCJlbmNvZGVUb0N1cnZlIiwiZW5jb2RlRFNUIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/abstract/hash-to-curve.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js":
/*!************************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/modular.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FpDiv: () => (/* binding */ FpDiv),\n/* harmony export */   FpInvertBatch: () => (/* binding */ FpInvertBatch),\n/* harmony export */   FpIsSquare: () => (/* binding */ FpIsSquare),\n/* harmony export */   FpPow: () => (/* binding */ FpPow),\n/* harmony export */   FpSqrt: () => (/* binding */ FpSqrt),\n/* harmony export */   FpSqrtEven: () => (/* binding */ FpSqrtEven),\n/* harmony export */   FpSqrtOdd: () => (/* binding */ FpSqrtOdd),\n/* harmony export */   getFieldBytesLength: () => (/* binding */ getFieldBytesLength),\n/* harmony export */   getMinHashLength: () => (/* binding */ getMinHashLength),\n/* harmony export */   hashToPrivateScalar: () => (/* binding */ hashToPrivateScalar),\n/* harmony export */   invert: () => (/* binding */ invert),\n/* harmony export */   isNegativeLE: () => (/* binding */ isNegativeLE),\n/* harmony export */   mapHashToField: () => (/* binding */ mapHashToField),\n/* harmony export */   mod: () => (/* binding */ mod),\n/* harmony export */   nLength: () => (/* binding */ nLength),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   pow2: () => (/* binding */ pow2),\n/* harmony export */   tonelliShanks: () => (/* binding */ tonelliShanks),\n/* harmony export */   validateField: () => (/* binding */ validateField)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */ // Utilities for modular arithmetics and finite fields\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3);\n// prettier-ignore\nconst _4n = BigInt(4), _5n = BigInt(5), _8n = BigInt(8);\n// prettier-ignore\nconst _9n = BigInt(9), _16n = BigInt(16);\n// Calculates a modulo b\nfunction mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */ // TODO: use field version && remove\nfunction pow(num, power, modulo) {\n    if (modulo <= _0n || power < _0n) throw new Error(\"Expected power/modulo > 0\");\n    if (modulo === _1n) return _0n;\n    let res = _1n;\n    while(power > _0n){\n        if (power & _1n) res = res * num % modulo;\n        num = num * num % modulo;\n        power >>= _1n;\n    }\n    return res;\n}\n// Does x ^ (2 ^ power) mod p. pow2(30, 4) == 30 ^ (2 ^ 4)\nfunction pow2(x, power, modulo) {\n    let res = x;\n    while(power-- > _0n){\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n// Inverses number over modulo\nfunction invert(number, modulo) {\n    if (number === _0n || modulo <= _0n) {\n        throw new Error(`invert: expected positive integers, got n=${number} mod=${modulo}`);\n    }\n    // Euclidean GCD https://brilliant.org/wiki/extended-euclidean-algorithm/\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while(a !== _0n){\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n) throw new Error(\"invert: does not exist\");\n    return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */ function tonelliShanks(P) {\n    // Legendre constant: used to calculate Legendre symbol (a | p),\n    // which denotes the value of a^((p-1)/2) (mod p).\n    // (a | p) ≡ 1    if a is a square (mod p)\n    // (a | p) ≡ -1   if a is not a square (mod p)\n    // (a | p) ≡ 0    if a ≡ 0 (mod p)\n    const legendreC = (P - _1n) / _2n;\n    let Q, S, Z;\n    // Step 1: By factoring out powers of 2 from p - 1,\n    // find q and s such that p - 1 = q*(2^s) with q odd\n    for(Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++);\n    // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n    for(Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++);\n    // Fast-path\n    if (S === 1) {\n        const p1div4 = (P + _1n) / _4n;\n        return function tonelliFast(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            if (!Fp.eql(Fp.sqr(root), n)) throw new Error(\"Cannot find square root\");\n            return root;\n        };\n    }\n    // Slow-path\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n        if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE)) throw new Error(\"Cannot find square root\");\n        let r = S;\n        // TODO: will fail at Fp2/etc\n        let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n        let x = Fp.pow(n, Q1div2); // first guess at the square root\n        let b = Fp.pow(n, Q); // first guess at the fudge factor\n        while(!Fp.eql(b, Fp.ONE)){\n            if (Fp.eql(b, Fp.ZERO)) return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n            // Find m such b^(2^m)==1\n            let m = 1;\n            for(let t2 = Fp.sqr(b); m < r; m++){\n                if (Fp.eql(t2, Fp.ONE)) break;\n                t2 = Fp.sqr(t2); // t2 *= t2\n            }\n            // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n            const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n            g = Fp.sqr(ge); // g = ge * ge\n            x = Fp.mul(x, ge); // x *= ge\n            b = Fp.mul(b, g); // b *= g\n            r = m;\n        }\n        return x;\n    };\n}\nfunction FpSqrt(P) {\n    // NOTE: different algorithms can give different roots, it is up to user to decide which one they want.\n    // For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n    // P ≡ 3 (mod 4)\n    // √n = n^((P+1)/4)\n    if (P % _4n === _3n) {\n        // Not all roots possible!\n        // const ORDER =\n        //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n        // const NUM = 72057594037927816n;\n        const p1div4 = (P + _1n) / _4n;\n        return function sqrt3mod4(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            // Throw if root**2 != n\n            if (!Fp.eql(Fp.sqr(root), n)) throw new Error(\"Cannot find square root\");\n            return root;\n        };\n    }\n    // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n    if (P % _8n === _5n) {\n        const c1 = (P - _5n) / _8n;\n        return function sqrt5mod8(Fp, n) {\n            const n2 = Fp.mul(n, _2n);\n            const v = Fp.pow(n2, c1);\n            const nv = Fp.mul(n, v);\n            const i = Fp.mul(Fp.mul(nv, _2n), v);\n            const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n            if (!Fp.eql(Fp.sqr(root), n)) throw new Error(\"Cannot find square root\");\n            return root;\n        };\n    }\n    // P ≡ 9 (mod 16)\n    if (P % _16n === _9n) {\n    // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n    // Means we cannot use sqrt for constants at all!\n    //\n    // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n    // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n    // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n    // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n    // sqrt = (x) => {\n    //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n    //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n    //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n    //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n    //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n    //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n    //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n    //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n    //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n    //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n    // }\n    }\n    // Other cases: Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nconst isNegativeLE = (num, modulo)=>(mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    \"create\",\n    \"isValid\",\n    \"is0\",\n    \"neg\",\n    \"inv\",\n    \"sqrt\",\n    \"sqr\",\n    \"eql\",\n    \"add\",\n    \"sub\",\n    \"mul\",\n    \"pow\",\n    \"div\",\n    \"addN\",\n    \"subN\",\n    \"mulN\",\n    \"sqrN\"\n];\nfunction validateField(field) {\n    const initial = {\n        ORDER: \"bigint\",\n        MASK: \"bigint\",\n        BYTES: \"isSafeInteger\",\n        BITS: \"isSafeInteger\"\n    };\n    const opts = FIELD_FIELDS.reduce((map, val)=>{\n        map[val] = \"function\";\n        return map;\n    }, initial);\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */ function FpPow(f, num, power) {\n    // Should have same speed as pow for bigints\n    // TODO: benchmark!\n    if (power < _0n) throw new Error(\"Expected power > 0\");\n    if (power === _0n) return f.ONE;\n    if (power === _1n) return num;\n    let p = f.ONE;\n    let d = num;\n    while(power > _0n){\n        if (power & _1n) p = f.mul(p, d);\n        d = f.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */ function FpInvertBatch(f, nums) {\n    const tmp = new Array(nums.length);\n    // Walk from first to last, multiply them by each other MOD p\n    const lastMultiplied = nums.reduce((acc, num, i)=>{\n        if (f.is0(num)) return acc;\n        tmp[i] = acc;\n        return f.mul(acc, num);\n    }, f.ONE);\n    // Invert last element\n    const inverted = f.inv(lastMultiplied);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i)=>{\n        if (f.is0(num)) return acc;\n        tmp[i] = f.mul(acc, tmp[i]);\n        return f.mul(acc, num);\n    }, inverted);\n    return tmp;\n}\nfunction FpDiv(f, lhs, rhs) {\n    return f.mul(lhs, typeof rhs === \"bigint\" ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n// This function returns True whenever the value x is a square in the field F.\nfunction FpIsSquare(f) {\n    const legendreConst = (f.ORDER - _1n) / _2n; // Integer arithmetic\n    return (x)=>{\n        const p = f.pow(x, legendreConst);\n        return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n    };\n}\n// CURVE.n lengths\nfunction nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return {\n        nBitLength: _nBitLength,\n        nByteLength\n    };\n}\n/**\n * Initializes a finite field over prime. **Non-primes are not supported.**\n * Do not init in loop: slow. Very fragile: always run a benchmark on a change.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */ function Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n) throw new Error(`Expected Field ORDER > 0, got ${ORDER}`);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048) throw new Error(\"Field lengths over 2048 bytes are not supported\");\n    const sqrtP = FpSqrt(ORDER);\n    const f = Object.freeze({\n        ORDER,\n        BITS,\n        BYTES,\n        MASK: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num)=>mod(num, ORDER),\n        isValid: (num)=>{\n            if (typeof num !== \"bigint\") throw new Error(`Invalid field element: expected bigint, got ${typeof num}`);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num)=>num === _0n,\n        isOdd: (num)=>(num & _1n) === _1n,\n        neg: (num)=>mod(-num, ORDER),\n        eql: (lhs, rhs)=>lhs === rhs,\n        sqr: (num)=>mod(num * num, ORDER),\n        add: (lhs, rhs)=>mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs)=>mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs)=>mod(lhs * rhs, ORDER),\n        pow: (num, power)=>FpPow(f, num, power),\n        div: (lhs, rhs)=>mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num)=>num * num,\n        addN: (lhs, rhs)=>lhs + rhs,\n        subN: (lhs, rhs)=>lhs - rhs,\n        mulN: (lhs, rhs)=>lhs * rhs,\n        inv: (num)=>invert(num, ORDER),\n        sqrt: redef.sqrt || ((n)=>sqrtP(f, n)),\n        invertBatch: (lst)=>FpInvertBatch(f, lst),\n        // TODO: do we really need constant cmov?\n        // We don't have const-time bigints anyway, so probably will be not very useful\n        cmov: (a, b, c)=>c ? b : a,\n        toBytes: (num)=>isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(num, BYTES) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, BYTES),\n        fromBytes: (bytes)=>{\n            if (bytes.length !== BYTES) throw new Error(`Fp.fromBytes: expected ${BYTES}, got ${bytes.length}`);\n            return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(bytes) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes);\n        }\n    });\n    return Object.freeze(f);\n}\nfunction FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd) throw new Error(`Field doesn't have isOdd`);\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nfunction FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd) throw new Error(`Field doesn't have isOdd`);\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use mapKeyToField instead\n */ function hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)(\"privateHash\", hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024) throw new Error(`hashToPrivateScalar: expected ${minLen}-1024 bytes of input, got ${hashLen}`);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(hash) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */ function getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== \"bigint\") throw new Error(\"field order must be bigint\");\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */ function getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */ function mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024) throw new Error(`expected ${minLen}-1024 bytes of input, got ${len}`);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(key) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(reduced, fieldLen) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(reduced, fieldLen);\n} //# sourceMappingURL=modular.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js":
/*!**********************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/utils.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitGet: () => (/* binding */ bitGet),\n/* harmony export */   bitLen: () => (/* binding */ bitLen),\n/* harmony export */   bitMask: () => (/* binding */ bitMask),\n/* harmony export */   bitSet: () => (/* binding */ bitSet),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToNumberBE: () => (/* binding */ bytesToNumberBE),\n/* harmony export */   bytesToNumberLE: () => (/* binding */ bytesToNumberLE),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHmacDrbg: () => (/* binding */ createHmacDrbg),\n/* harmony export */   ensureBytes: () => (/* binding */ ensureBytes),\n/* harmony export */   equalBytes: () => (/* binding */ equalBytes),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   hexToNumber: () => (/* binding */ hexToNumber),\n/* harmony export */   numberToBytesBE: () => (/* binding */ numberToBytesBE),\n/* harmony export */   numberToBytesLE: () => (/* binding */ numberToBytesLE),\n/* harmony export */   numberToHexUnpadded: () => (/* binding */ numberToHexUnpadded),\n/* harmony export */   numberToVarBytesBE: () => (/* binding */ numberToVarBytesBE),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   validateObject: () => (/* binding */ validateObject)\n/* harmony export */ });\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */ // 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst u8a = (a)=>a instanceof Uint8Array;\nconst hexes = /* @__PURE__ */ Array.from({\n    length: 256\n}, (_, i)=>i.toString(16).padStart(2, \"0\"));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */ function bytesToHex(bytes) {\n    if (!u8a(bytes)) throw new Error(\"Uint8Array expected\");\n    // pre-caching improves the speed 6x\n    let hex = \"\";\n    for(let i = 0; i < bytes.length; i++){\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\nfunction numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? `0${hex}` : hex;\n}\nfunction hexToNumber(hex) {\n    if (typeof hex !== \"string\") throw new Error(\"hex string expected, got \" + typeof hex);\n    // Big Endian\n    return BigInt(hex === \"\" ? \"0\" : `0x${hex}`);\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */ function hexToBytes(hex) {\n    if (typeof hex !== \"string\") throw new Error(\"hex string expected, got \" + typeof hex);\n    const len = hex.length;\n    if (len % 2) throw new Error(\"padded hex string expected, got unpadded hex of length \" + len);\n    const array = new Uint8Array(len / 2);\n    for(let i = 0; i < array.length; i++){\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0) throw new Error(\"Invalid byte sequence\");\n        array[i] = byte;\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nfunction bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nfunction bytesToNumberLE(bytes) {\n    if (!u8a(bytes)) throw new Error(\"Uint8Array expected\");\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nfunction numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, \"0\"));\n}\nfunction numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nfunction numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */ function ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === \"string\") {\n        try {\n            res = hexToBytes(hex);\n        } catch (e) {\n            throw new Error(`${title} must be valid hex string, got \"${hex}\". Cause: ${e}`);\n        }\n    } else if (u8a(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    } else {\n        throw new Error(`${title} must be hex string or Uint8Array`);\n    }\n    const len = res.length;\n    if (typeof expectedLength === \"number\" && len !== expectedLength) throw new Error(`${title} expected ${expectedLength} bytes, got ${len}`);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */ function concatBytes(...arrays) {\n    const r = new Uint8Array(arrays.reduce((sum, a)=>sum + a.length, 0));\n    let pad = 0; // walk through each item, ensure they have proper type\n    arrays.forEach((a)=>{\n        if (!u8a(a)) throw new Error(\"Uint8Array expected\");\n        r.set(a, pad);\n        pad += a.length;\n    });\n    return r;\n}\nfunction equalBytes(b1, b2) {\n    // We don't care about timing attacks here\n    if (b1.length !== b2.length) return false;\n    for(let i = 0; i < b1.length; i++)if (b1[i] !== b2[i]) return false;\n    return true;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */ function utf8ToBytes(str) {\n    if (typeof str !== \"string\") throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */ function bitLen(n) {\n    let len;\n    for(len = 0; n > _0n; n >>= _1n, len += 1);\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */ function bitGet(n, pos) {\n    return n >> BigInt(pos) & _1n;\n}\n/**\n * Sets single bit at position.\n */ const bitSet = (n, pos, value)=>{\n    return n | (value ? _1n : _0n) << BigInt(pos);\n};\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */ const bitMask = (n)=>(_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = (data)=>new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr)=>Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */ function createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== \"number\" || hashLen < 2) throw new Error(\"hashLen must be a number\");\n    if (typeof qByteLen !== \"number\" || qByteLen < 2) throw new Error(\"qByteLen must be a number\");\n    if (typeof hmacFn !== \"function\") throw new Error(\"hmacFn must be a function\");\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = ()=>{\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b)=>hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n())=>{\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([\n            0x00\n        ]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0) return;\n        k = h(u8fr([\n            0x01\n        ]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = ()=>{\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000) throw new Error(\"drbg: tried 1000 values\");\n        let len = 0;\n        const out = [];\n        while(len < qByteLen){\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred)=>{\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while(!(res = pred(gen())))reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val)=>typeof val === \"bigint\",\n    function: (val)=>typeof val === \"function\",\n    boolean: (val)=>typeof val === \"boolean\",\n    string: (val)=>typeof val === \"string\",\n    stringOrUint8Array: (val)=>typeof val === \"string\" || val instanceof Uint8Array,\n    isSafeInteger: (val)=>Number.isSafeInteger(val),\n    array: (val)=>Array.isArray(val),\n    field: (val, object)=>object.Fp.isValid(val),\n    hash: (val)=>typeof val === \"function\" && Number.isSafeInteger(val.outputLen)\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nfunction validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional)=>{\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== \"function\") throw new Error(`Invalid validator \"${type}\", expected function`);\n        const val = object[fieldName];\n        if (isOptional && val === undefined) return;\n        if (!checkVal(val, object)) {\n            throw new Error(`Invalid param ${String(fieldName)}=${val} (${typeof val}), expected ${type}`);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))checkField(fieldName, type, true);\n    return object;\n} // validate type tests\n // const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n // const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n // // Should fail type-check\n // const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n // const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n // const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n // const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/abstract/weierstrass.js":
/*!****************************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/weierstrass.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DER: () => (/* binding */ DER),\n/* harmony export */   SWUFpSqrtRatio: () => (/* binding */ SWUFpSqrtRatio),\n/* harmony export */   mapToCurveSimpleSWU: () => (/* binding */ mapToCurveSimpleSWU),\n/* harmony export */   weierstrass: () => (/* binding */ weierstrass),\n/* harmony export */   weierstrassPoints: () => (/* binding */ weierstrassPoints)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modular.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _curve_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./curve.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/curve.js\");\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */ // Short Weierstrass curve. The formula is: y² = x³ + ax + b\n\n\n\n\nfunction validatePointOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_0__.validateBasic)(curve);\n    _utils_js__WEBPACK_IMPORTED_MODULE_1__.validateObject(opts, {\n        a: \"field\",\n        b: \"field\"\n    }, {\n        allowedPrivateKeyLengths: \"array\",\n        wrapPrivateKey: \"boolean\",\n        isTorsionFree: \"function\",\n        clearCofactor: \"function\",\n        allowInfinityPoint: \"boolean\",\n        fromBytes: \"function\",\n        toBytes: \"function\"\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error(\"Endomorphism can only be defined for Koblitz curves that have a=0\");\n        }\n        if (typeof endo !== \"object\" || typeof endo.beta !== \"bigint\" || typeof endo.splitScalar !== \"function\") {\n            throw new Error(\"Expected endomorphism with beta: bigint and splitScalar: function\");\n        }\n    }\n    return Object.freeze({\n        ...opts\n    });\n}\n// ASN.1 DER encoding utilities\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = _utils_js__WEBPACK_IMPORTED_MODULE_1__;\nconst DER = {\n    // asn.1 DER encoding utils\n    Err: class DERErr extends Error {\n        constructor(m = \"\"){\n            super(m);\n        }\n    },\n    _parseInt (data) {\n        const { Err: E } = DER;\n        if (data.length < 2 || data[0] !== 0x02) throw new E(\"Invalid signature integer tag\");\n        const len = data[1];\n        const res = data.subarray(2, len + 2);\n        if (!len || res.length !== len) throw new E(\"Invalid signature integer: wrong length\");\n        // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n        // since we always use positive integers here. It must always be empty:\n        // - add zero byte if exists\n        // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n        if (res[0] & 128) throw new E(\"Invalid signature integer: negative\");\n        if (res[0] === 0x00 && !(res[1] & 128)) throw new E(\"Invalid signature integer: unnecessary leading zero\");\n        return {\n            d: b2n(res),\n            l: data.subarray(len + 2)\n        }; // d is data, l is left\n    },\n    toSig (hex) {\n        // parse DER signature\n        const { Err: E } = DER;\n        const data = typeof hex === \"string\" ? h2b(hex) : hex;\n        if (!(data instanceof Uint8Array)) throw new Error(\"ui8a expected\");\n        let l = data.length;\n        if (l < 2 || data[0] != 0x30) throw new E(\"Invalid signature tag\");\n        if (data[1] !== l - 2) throw new E(\"Invalid signature: incorrect length\");\n        const { d: r, l: sBytes } = DER._parseInt(data.subarray(2));\n        const { d: s, l: rBytesLeft } = DER._parseInt(sBytes);\n        if (rBytesLeft.length) throw new E(\"Invalid signature: left bytes after parsing\");\n        return {\n            r,\n            s\n        };\n    },\n    hexFromSig (sig) {\n        // Add leading zero if first byte has negative bit enabled. More details in '_parseInt'\n        const slice = (s)=>Number.parseInt(s[0], 16) & 8 ? \"00\" + s : s;\n        const h = (num)=>{\n            const hex = num.toString(16);\n            return hex.length & 1 ? `0${hex}` : hex;\n        };\n        const s = slice(h(sig.s));\n        const r = slice(h(sig.r));\n        const shl = s.length / 2;\n        const rhl = r.length / 2;\n        const sl = h(shl);\n        const rl = h(rhl);\n        return `30${h(rhl + shl + 4)}02${rl}${r}02${sl}${s}`;\n    }\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nfunction weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const toBytes = CURVE.toBytes || ((_c, point, _isCompressed)=>{\n        const a = point.toAffine();\n        return _utils_js__WEBPACK_IMPORTED_MODULE_1__.concatBytes(Uint8Array.from([\n            0x04\n        ]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n    });\n    const fromBytes = CURVE.fromBytes || ((bytes)=>{\n        // const head = bytes[0];\n        const tail = bytes.subarray(1);\n        // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n        const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n        const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n        return {\n            x,\n            y\n        };\n    });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula\n     * @returns y²\n     */ function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x2 * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n    }\n    // Validate whether the passed curve params are valid.\n    // We check if curve equation works for generator point.\n    // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n    // ProjectivePoint class has not been initialized yet.\n    if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx))) throw new Error(\"bad generator point: equation left != right\");\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return typeof num === \"bigint\" && _0n < num && num < CURVE.n;\n    }\n    function assertGE(num) {\n        if (!isWithinCurveOrder(num)) throw new Error(\"Expected valid bigint: 0 < bigint < curve.n\");\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n } = CURVE;\n        if (lengths && typeof key !== \"bigint\") {\n            if (key instanceof Uint8Array) key = _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToHex(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== \"string\" || !lengths.includes(key.length)) throw new Error(\"Invalid key\");\n            key = key.padStart(nByteLength * 2, \"0\");\n        }\n        let num;\n        try {\n            num = typeof key === \"bigint\" ? key : _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToNumberBE((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)(\"private key\", key, nByteLength));\n        } catch (error) {\n            throw new Error(`private key must be ${nByteLength} bytes, hex or bigint, not ${typeof key}`);\n        }\n        if (wrapPrivateKey) num = _modular_js__WEBPACK_IMPORTED_MODULE_2__.mod(num, n); // disabled by default, enabled for BLS\n        assertGE(num); // num in range [1..N-1]\n        return num;\n    }\n    const pointPrecomputes = new Map();\n    function assertPrjPoint(other) {\n        if (!(other instanceof Point)) throw new Error(\"ProjectivePoint expected\");\n    }\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */ class Point {\n        constructor(px, py, pz){\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            if (px == null || !Fp.isValid(px)) throw new Error(\"x required\");\n            if (py == null || !Fp.isValid(py)) throw new Error(\"y required\");\n            if (pz == null || !Fp.isValid(pz)) throw new Error(\"z required\");\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y)) throw new Error(\"invalid affine point\");\n            if (p instanceof Point) throw new Error(\"projective point not allowed\");\n            const is0 = (i)=>Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y)) return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */ static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p)=>p.pz));\n            return points.map((p, i)=>p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */ static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)(\"pointHex\", hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            this._WINDOW_SIZE = windowSize;\n            pointPrecomputes.delete(this);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            if (this.is0()) {\n                // (0, 1, 0) aka ZERO is invalid in most contexts.\n                // In BLS, ZERO can be serialized, so we allow it.\n                // (0, 0, 0) is wrong representation of ZERO and is always invalid.\n                if (CURVE.allowInfinityPoint && !Fp.is0(this.py)) return;\n                throw new Error(\"bad point: ZERO\");\n            }\n            // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n            const { x, y } = this.toAffine();\n            // Check if x, y are valid field elements\n            if (!Fp.isValid(x) || !Fp.isValid(y)) throw new Error(\"bad point: x or y not FE\");\n            const left = Fp.sqr(y); // y²\n            const right = weierstrassEquation(x); // x³ + ax + b\n            if (!Fp.eql(left, right)) throw new Error(\"bad point: equation left != right\");\n            if (!this.isTorsionFree()) throw new Error(\"bad point: not in prime-order subgroup\");\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd) return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */ equals(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */ negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, pointPrecomputes, n, (comp)=>{\n                const toInv = Fp.invertBatch(comp.map((p)=>p.pz));\n                return comp.map((p, i)=>p.toAffine(toInv[i])).map(Point.fromAffine);\n            });\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */ multiplyUnsafe(n) {\n            const I = Point.ZERO;\n            if (n === _0n) return I;\n            assertGE(n); // Will throw on 0\n            if (n === _1n) return this;\n            const { endo } = CURVE;\n            if (!endo) return wnaf.unsafeLadder(this, n);\n            // Apply endomorphism\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(n);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while(k1 > _0n || k2 > _0n){\n                if (k1 & _1n) k1p = k1p.add(d);\n                if (k2 & _1n) k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg) k1p = k1p.negate();\n            if (k2neg) k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */ multiply(scalar) {\n            assertGE(scalar);\n            let n = scalar;\n            let point, fake; // Fake point is used to const-time mult\n            const { endo } = CURVE;\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(n);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            } else {\n                const { p, f } = this.wNAF(n);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([\n                point,\n                fake\n            ])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */ multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            )=>a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a);\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            const { px: x, py: y, pz: z } = this;\n            const is0 = this.is0();\n            // If invZ was 0, we return zero point. However we still want to execute\n            // all operations, so we replace invZ with a random number, 1.\n            if (iz == null) iz = is0 ? Fp.ONE : Fp.inv(z);\n            const ax = Fp.mul(x, iz);\n            const ay = Fp.mul(y, iz);\n            const zz = Fp.mul(z, iz);\n            if (is0) return {\n                x: Fp.ZERO,\n                y: Fp.ZERO\n            };\n            if (!Fp.eql(zz, Fp.ONE)) throw new Error(\"invZ was invalid\");\n            return {\n                x: ax,\n                y: ay\n            };\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n) return true; // No subgroups, always torsion-free\n            if (isTorsionFree) return isTorsionFree(Point, this);\n            throw new Error(\"isTorsionFree() has not been declared for the elliptic curve\");\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n) return this; // Fast-path\n            if (clearCofactor) return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToHex(this.toRawBytes(isCompressed));\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n    const _bits = CURVE.nBitLength;\n    const wnaf = (0,_curve_js__WEBPACK_IMPORTED_MODULE_0__.wNAF)(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n    // Validate if generator point is on curve\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder\n    };\n}\nfunction validateOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_0__.validateBasic)(curve);\n    _utils_js__WEBPACK_IMPORTED_MODULE_1__.validateObject(opts, {\n        hash: \"hash\",\n        hmac: \"function\",\n        randomBytes: \"function\"\n    }, {\n        bits2int: \"function\",\n        bits2int_modN: \"function\",\n        lowS: \"boolean\"\n    });\n    return Object.freeze({\n        lowS: true,\n        ...opts\n    });\n}\nfunction weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function isValidFieldElement(num) {\n        return _0n < num && num < Fp.ORDER; // 0 is banned since it's not invertible FE\n    }\n    function modN(a) {\n        return _modular_js__WEBPACK_IMPORTED_MODULE_2__.mod(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return _modular_js__WEBPACK_IMPORTED_MODULE_2__.invert(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder } = weierstrassPoints({\n        ...CURVE,\n        toBytes (_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = _utils_js__WEBPACK_IMPORTED_MODULE_1__.concatBytes;\n            if (isCompressed) {\n                return cat(Uint8Array.from([\n                    point.hasEvenY() ? 0x02 : 0x03\n                ]), x);\n            } else {\n                return cat(Uint8Array.from([\n                    0x04\n                ]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes (bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToNumberBE(tail);\n                if (!isValidFieldElement(x)) throw new Error(\"Point is not on curve\");\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd) y = Fp.neg(y);\n                return {\n                    x,\n                    y\n                };\n            } else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return {\n                    x,\n                    y\n                };\n            } else {\n                throw new Error(`Point of length ${len} was invalid. Expected ${compressedLen} compressed bytes or ${uncompressedLen} uncompressed bytes`);\n            }\n        }\n    });\n    const numToNByteStr = (num)=>_utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToHex(_utils_js__WEBPACK_IMPORTED_MODULE_1__.numberToBytesBE(num, CURVE.nByteLength));\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to)=>_utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToNumberBE(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */ class Signature {\n        constructor(r, s, recovery){\n            this.r = r;\n            this.s = s;\n            this.recovery = recovery;\n            this.assertValidity();\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = CURVE.nByteLength;\n            hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)(\"compactSignature\", hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)(\"DER\", hex));\n            return new Signature(r, s);\n        }\n        assertValidity() {\n            // can use assertGE here\n            if (!isWithinCurveOrder(this.r)) throw new Error(\"r must be 0 < r < CURVE.n\");\n            if (!isWithinCurveOrder(this.s)) throw new Error(\"s must be 0 < s < CURVE.n\");\n        }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)(\"msgHash\", msgHash)); // Truncate hash\n            if (rec == null || ![\n                0,\n                1,\n                2,\n                3\n            ].includes(rec)) throw new Error(\"recovery id invalid\");\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER) throw new Error(\"recovery id 2 or 3 invalid\");\n            const prefix = (rec & 1) === 0 ? \"02\" : \"03\";\n            const R = Point.fromHex(prefix + numToNByteStr(radj));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q) throw new Error(\"point at infinify\"); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_1__.hexToBytes(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig({\n                r: this.r,\n                s: this.s\n            });\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return _utils_js__WEBPACK_IMPORTED_MODULE_1__.hexToBytes(this.toCompactHex());\n        }\n        toCompactHex() {\n            return numToNByteStr(this.r) + numToNByteStr(this.s);\n        }\n    }\n    const utils = {\n        isValidPrivateKey (privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            } catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */ randomPrivateKey: ()=>{\n            const length = _modular_js__WEBPACK_IMPORTED_MODULE_2__.getMinHashLength(CURVE.n);\n            return _modular_js__WEBPACK_IMPORTED_MODULE_2__.mapHashToField(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */ precompute (windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        }\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */ function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */ function isProbPub(item) {\n        const arr = item instanceof Uint8Array;\n        const str = typeof item === \"string\";\n        const len = (arr || str) && item.length;\n        if (arr) return len === compressedLen || len === uncompressedLen;\n        if (str) return len === 2 * compressedLen || len === 2 * uncompressedLen;\n        if (item instanceof Point) return true;\n        return false;\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */ function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA)) throw new Error(\"first arg must be private key\");\n        if (!isProbPub(publicB)) throw new Error(\"second arg must be public key\");\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int || function(bytes) {\n        // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n        // for some cases, since bytes.length * 8 is not actual bitLength.\n        const num = _utils_js__WEBPACK_IMPORTED_MODULE_1__.bytesToNumberBE(bytes); // check for == u8 done here\n        const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n        return delta > 0 ? num >> BigInt(delta) : num;\n    };\n    const bits2int_modN = CURVE.bits2int_modN || function(bytes) {\n        return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n    };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = _utils_js__WEBPACK_IMPORTED_MODULE_1__.bitMask(CURVE.nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */ function int2octets(num) {\n        if (typeof num !== \"bigint\") throw new Error(\"bigint expected\");\n        if (!(_0n <= num && num < ORDER_MASK)) throw new Error(`bigint expected < 2^${CURVE.nBitLength}`);\n        // works with order, can have different size than numToField!\n        return _utils_js__WEBPACK_IMPORTED_MODULE_1__.numberToBytesBE(num, CURVE.nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order, this will be wrong at least for P521.\n    // Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if ([\n            \"recovered\",\n            \"canonical\"\n        ].some((k)=>k in opts)) throw new Error(\"sign() legacy options not supported\");\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null) lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)(\"msgHash\", msgHash);\n        if (prehash) msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)(\"prehashed msgHash\", hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [\n            int2octets(d),\n            int2octets(h1int)\n        ];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push((0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)(\"extraEntropy\", e)); // check for being bytes\n        }\n        const seed = _utils_js__WEBPACK_IMPORTED_MODULE_1__.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k)) return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n) return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n) return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return {\n            seed,\n            k2sig\n        };\n    }\n    const defaultSigOpts = {\n        lowS: CURVE.lowS,\n        prehash: false\n    };\n    const defaultVerOpts = {\n        lowS: CURVE.lowS,\n        prehash: false\n    };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */ function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = _utils_js__WEBPACK_IMPORTED_MODULE_1__.createHmacDrbg(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */ function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)(\"msgHash\", msgHash);\n        publicKey = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureBytes)(\"publicKey\", publicKey);\n        if (\"strict\" in opts) throw new Error(\"options.strict was renamed to lowS\");\n        const { lowS, prehash } = opts;\n        let _sig = undefined;\n        let P;\n        try {\n            if (typeof sg === \"string\" || sg instanceof Uint8Array) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    _sig = Signature.fromDER(sg);\n                } catch (derError) {\n                    if (!(derError instanceof DER.Err)) throw derError;\n                    _sig = Signature.fromCompact(sg);\n                }\n            } else if (typeof sg === \"object\" && typeof sg.r === \"bigint\" && typeof sg.s === \"bigint\") {\n                const { r, s } = sg;\n                _sig = new Signature(r, s);\n            } else {\n                throw new Error(\"PARSE\");\n            }\n            P = Point.fromHex(publicKey);\n        } catch (error) {\n            if (error.message === \"PARSE\") throw new Error(`signature must be Signature instance, Uint8Array or hex string`);\n            return false;\n        }\n        if (lowS && _sig.hasHighS()) return false;\n        if (prehash) msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R) return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */ function SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for(let o = q - _1n; o % _2n === _0n; o /= _2n)l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << c1 - _1n - _1n;\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v)=>{\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for(let i = c1; i > _1n; i--){\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << tv5 - _1n; // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return {\n            isValid: isQR,\n            value: tv3\n        };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v)=>{\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return {\n                isValid: isQR,\n                value: y\n            }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */ function mapToCurveSimpleSWU(Fp, opts) {\n    _modular_js__WEBPACK_IMPORTED_MODULE_2__.validateField(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z)) throw new Error(\"mapToCurveSimpleSWU: invalid opts\");\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd) throw new Error(\"Fp.isOdd is not implemented!\");\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u)=>{\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        x = Fp.div(x, tv4); // 25.   x = x / tv4\n        return {\n            x,\n            y\n        };\n    };\n} //# sourceMappingURL=weierstrass.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/abstract/weierstrass.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/curves/esm/secp256k1.js":
/*!*****************************************************!*\
  !*** ./node_modules/@noble/curves/esm/secp256k1.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeToCurve: () => (/* binding */ encodeToCurve),\n/* harmony export */   hashToCurve: () => (/* binding */ hashToCurve),\n/* harmony export */   schnorr: () => (/* binding */ schnorr),\n/* harmony export */   secp256k1: () => (/* binding */ secp256k1)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @noble/hashes/sha256 */ \"(ssr)/./node_modules/@noble/hashes/esm/sha256.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/hashes/utils */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract/modular.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/* harmony import */ var _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./abstract/utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./abstract/hash-to-curve.js */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/hash-to-curve.js\");\n/* harmony import */ var _shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_shortw_utils.js */ \"(ssr)/./node_modules/@noble/curves/esm/_shortw_utils.js\");\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */ \n\n\n\n\n\n\nconst secp256k1P = BigInt(\"0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f\");\nconst secp256k1N = BigInt(\"0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141\");\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b)=>(a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */ function sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = y * y * y % P; // x^3, 11\n    const b3 = b2 * b2 * y % P; // x^7\n    const b6 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b3, _3n, P) * b3 % P;\n    const b9 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b6, _3n, P) * b3 % P;\n    const b11 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b9, _2n, P) * b2 % P;\n    const b22 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b11, _11n, P) * b11 % P;\n    const b44 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b22, _22n, P) * b22 % P;\n    const b88 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b44, _44n, P) * b44 % P;\n    const b176 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b88, _88n, P) * b88 % P;\n    const b220 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b176, _44n, P) * b44 % P;\n    const b223 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b220, _3n, P) * b3 % P;\n    const t1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b223, _23n, P) * b22 % P;\n    const t2 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t1, _6n, P) * b2 % P;\n    const root = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t2, _2n, P);\n    if (!Fp.eql(Fp.sqr(root), y)) throw new Error(\"Cannot find square root\");\n    return root;\n}\nconst Fp = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.Field)(secp256k1P, undefined, undefined, {\n    sqrt: sqrtMod\n});\nconst secp256k1 = (0,_shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__.createCurve)({\n    a: BigInt(0),\n    b: BigInt(7),\n    Fp,\n    n: secp256k1N,\n    // Base point (x, y) aka generator point\n    Gx: BigInt(\"55066263022277343669578718895168534326250603453777594175500187360389116729240\"),\n    Gy: BigInt(\"32670510020758816978083085130507043184471273380659243275938904335757337482424\"),\n    h: BigInt(1),\n    lowS: true,\n    /**\n     * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n     * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n     * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n     * Explanation: https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066\n     */ endo: {\n        beta: BigInt(\"0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee\"),\n        splitScalar: (k)=>{\n            const n = secp256k1N;\n            const a1 = BigInt(\"0x3086d221a7d46bcde86c90e49284eb15\");\n            const b1 = -_1n * BigInt(\"0xe4437ed6010e88286f547fa90abfe4c3\");\n            const a2 = BigInt(\"0x114ca50f7a8e2f3f657c1108d9d44cfd8\");\n            const b2 = a1;\n            const POW_2_128 = BigInt(\"0x100000000000000000000000000000000\"); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(k - c1 * a1 - c2 * a2, n);\n            let k2 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg) k1 = n - k1;\n            if (k2neg) k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error(\"splitScalar: Endomorphism failed, k=\" + k);\n            }\n            return {\n                k1neg,\n                k1,\n                k2neg,\n                k2\n            };\n        }\n    }\n}, _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\nconst fe = (x)=>typeof x === \"bigint\" && _0n < x && x < secp256k1P;\nconst ge = (x)=>typeof x === \"bigint\" && _0n < x && x < secp256k1N;\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */ const TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = (0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256)(Uint8Array.from(tag, (c)=>c.charCodeAt(0)));\n        tagP = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return (0,_noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256)((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point)=>point.toRawBytes(true).slice(1);\nconst numTo32b = (n)=>(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE)(n, 32);\nconst modP = (x)=>(0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1P);\nconst modN = (x)=>(0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q, a, b)=>Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return {\n        scalar: scalar,\n        bytes: pointToBytes(p)\n    };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */ function lift_x(x) {\n    if (!fe(x)) throw new Error(\"bad x: need 0 < x < p\"); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n) y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */ function challenge(...args) {\n    return modN((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(taggedHash(\"BIP0340/challenge\", ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */ function schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */ function schnorrSign(message, privateKey, auxRand = (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__.randomBytes)(32)) {\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)(\"message\", message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)(\"auxRand\", auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(taggedHash(\"BIP0340/aux\", a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash(\"BIP0340/nonce\", t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n) throw new Error(\"sign failed: k is zero\"); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px)) throw new Error(\"sign: Invalid signature produced\");\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */ function schnorrVerify(signature, message, publicKey) {\n    const sig = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)(\"signature\", signature, 64);\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)(\"message\", message);\n    const pub = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)(\"publicKey\", publicKey, 32);\n    try {\n        const P = lift_x((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!fe(r)) return false;\n        const s = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE)(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!ge(s)) return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r) return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    } catch (error) {\n        return false;\n    }\n}\nconst schnorr = /* @__PURE__ */ (()=>({\n        getPublicKey: schnorrGetPublicKey,\n        sign: schnorrSign,\n        verify: schnorrVerify,\n        utils: {\n            randomPrivateKey: secp256k1.utils.randomPrivateKey,\n            lift_x,\n            pointToBytes,\n            numberToBytesBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE,\n            bytesToNumberBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE,\n            taggedHash,\n            mod: _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod\n        }\n    }))();\nconst isoMap = /* @__PURE__ */ (()=>(0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.isogenyMap)(Fp, [\n        // xNum\n        [\n            \"0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7\",\n            \"0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581\",\n            \"0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262\",\n            \"0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c\"\n        ],\n        // xDen\n        [\n            \"0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b\",\n            \"0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14\",\n            \"0x0000000000000000000000000000000000000000000000000000000000000001\"\n        ],\n        // yNum\n        [\n            \"0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c\",\n            \"0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3\",\n            \"0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931\",\n            \"0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84\"\n        ],\n        // yDen\n        [\n            \"0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b\",\n            \"0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573\",\n            \"0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f\",\n            \"0x0000000000000000000000000000000000000000000000000000000000000001\"\n        ]\n    ].map((i)=>i.map((j)=>BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (()=>(0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__.mapToCurveSimpleSWU)(Fp, {\n        A: BigInt(\"0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533\"),\n        B: BigInt(\"1771\"),\n        Z: Fp.create(BigInt(\"-11\"))\n    }))();\nconst htf = /* @__PURE__ */ (()=>(0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.createHasher)(secp256k1.ProjectivePoint, (scalars)=>{\n        const { x, y } = mapSWU(Fp.create(scalars[0]));\n        return isoMap(x, y);\n    }, {\n        DST: \"secp256k1_XMD:SHA-256_SSWU_RO_\",\n        encodeDST: \"secp256k1_XMD:SHA-256_SSWU_NU_\",\n        p: Fp.ORDER,\n        m: 1,\n        k: 128,\n        expand: \"xmd\",\n        hash: _noble_hashes_sha256__WEBPACK_IMPORTED_MODULE_2__.sha256\n    }))();\nconst hashToCurve = /* @__PURE__ */ (()=>htf.hashToCurve)();\nconst encodeToCurve = /* @__PURE__ */ (()=>htf.encodeToCurve)(); //# sourceMappingURL=secp256k1.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/curves/esm/secp256k1.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_assert.js":
/*!***************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_assert.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bool: () => (/* binding */ bool),\n/* harmony export */   bytes: () => (/* binding */ bytes),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   exists: () => (/* binding */ exists),\n/* harmony export */   hash: () => (/* binding */ hash),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   output: () => (/* binding */ output)\n/* harmony export */ });\nfunction number(n) {\n    if (!Number.isSafeInteger(n) || n < 0) throw new Error(`Wrong positive integer: ${n}`);\n}\nfunction bool(b) {\n    if (typeof b !== \"boolean\") throw new Error(`Expected boolean, not ${b}`);\n}\nfunction bytes(b, ...lengths) {\n    if (!(b instanceof Uint8Array)) throw new Error(\"Expected Uint8Array\");\n    if (lengths.length > 0 && !lengths.includes(b.length)) throw new Error(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\nfunction hash(hash) {\n    if (typeof hash !== \"function\" || typeof hash.create !== \"function\") throw new Error(\"Hash should be wrapped by utils.wrapConstructor\");\n    number(hash.outputLen);\n    number(hash.blockLen);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed) throw new Error(\"Hash instance has been destroyed\");\n    if (checkFinished && instance.finished) throw new Error(\"Hash#digest() has already been called\");\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\n\nconst assert = {\n    number,\n    bool,\n    bytes,\n    hash,\n    exists,\n    output\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (assert); //# sourceMappingURL=_assert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_assert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_sha2.js":
/*!*************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_sha2.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SHA2: () => (/* binding */ SHA2)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n\n\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === \"function\") return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number(value >> _32n & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n// Base SHA2 class (RFC 6234)\nclass SHA2 extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE){\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(this.buffer);\n    }\n    update(data) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.exists)(this);\n        const { view, buffer, blockLen } = this;\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(data);\n        const len = data.length;\n        for(let pos = 0; pos < len;){\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(data);\n                for(; blockLen <= len - pos; pos += blockLen)this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.exists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.output)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 128;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for(let i = pos; i < blockLen; i++)buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4) throw new Error(\"_sha2: outputLen should be aligned to 32bit\");\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length) throw new Error(\"_sha2: outputLen bigger than state\");\n        for(let i = 0; i < outLen; i++)oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen) to.buffer.set(buffer);\n        return to;\n    }\n} //# sourceMappingURL=_sha2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_sha2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/_u64.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_u64.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   add3H: () => (/* binding */ add3H),\n/* harmony export */   add3L: () => (/* binding */ add3L),\n/* harmony export */   add4H: () => (/* binding */ add4H),\n/* harmony export */   add4L: () => (/* binding */ add4L),\n/* harmony export */   add5H: () => (/* binding */ add5H),\n/* harmony export */   add5L: () => (/* binding */ add5L),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fromBig: () => (/* binding */ fromBig),\n/* harmony export */   rotlBH: () => (/* binding */ rotlBH),\n/* harmony export */   rotlBL: () => (/* binding */ rotlBL),\n/* harmony export */   rotlSH: () => (/* binding */ rotlSH),\n/* harmony export */   rotlSL: () => (/* binding */ rotlSL),\n/* harmony export */   rotr32H: () => (/* binding */ rotr32H),\n/* harmony export */   rotr32L: () => (/* binding */ rotr32L),\n/* harmony export */   rotrBH: () => (/* binding */ rotrBH),\n/* harmony export */   rotrBL: () => (/* binding */ rotrBL),\n/* harmony export */   rotrSH: () => (/* binding */ rotrSH),\n/* harmony export */   rotrSL: () => (/* binding */ rotrSL),\n/* harmony export */   shrSH: () => (/* binding */ shrSH),\n/* harmony export */   shrSL: () => (/* binding */ shrSL),\n/* harmony export */   split: () => (/* binding */ split),\n/* harmony export */   toBig: () => (/* binding */ toBig)\n/* harmony export */ });\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n// We are not using BigUint64Array, because they are extremely slow as per 2022\nfunction fromBig(n, le = false) {\n    if (le) return {\n        h: Number(n & U32_MASK64),\n        l: Number(n >> _32n & U32_MASK64)\n    };\n    return {\n        h: Number(n >> _32n & U32_MASK64) | 0,\n        l: Number(n & U32_MASK64) | 0\n    };\n}\nfunction split(lst, le = false) {\n    let Ah = new Uint32Array(lst.length);\n    let Al = new Uint32Array(lst.length);\n    for(let i = 0; i < lst.length; i++){\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [\n            h,\n            l\n        ];\n    }\n    return [\n        Ah,\n        Al\n    ];\n}\nconst toBig = (h, l)=>BigInt(h >>> 0) << _32n | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s)=>h >>> s;\nconst shrSL = (h, l, s)=>h << 32 - s | l >>> s;\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s)=>h >>> s | l << 32 - s;\nconst rotrSL = (h, l, s)=>h << 32 - s | l >>> s;\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s)=>h << 64 - s | l >>> s - 32;\nconst rotrBL = (h, l, s)=>h >>> s - 32 | l << 64 - s;\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l)=>l;\nconst rotr32L = (h, _l)=>h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s)=>h << s | l >>> 32 - s;\nconst rotlSL = (h, l, s)=>l << s | h >>> 32 - s;\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s)=>l << s - 32 | h >>> 64 - s;\nconst rotlBL = (h, l, s)=>h << s - 32 | l >>> 64 - s;\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return {\n        h: Ah + Bh + (l / 2 ** 32 | 0) | 0,\n        l: l | 0\n    };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl)=>(Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch)=>Ah + Bh + Ch + (low / 2 ** 32 | 0) | 0;\nconst add4L = (Al, Bl, Cl, Dl)=>(Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh)=>Ah + Bh + Ch + Dh + (low / 2 ** 32 | 0) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El)=>(Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh)=>Ah + Bh + Ch + Dh + Eh + (low / 2 ** 32 | 0) | 0;\n// prettier-ignore\n\n// prettier-ignore\nconst u64 = {\n    fromBig,\n    split,\n    toBig,\n    shrSH,\n    shrSL,\n    rotrSH,\n    rotrSL,\n    rotrBH,\n    rotrBL,\n    rotr32H,\n    rotr32L,\n    rotlSH,\n    rotlSL,\n    rotlBH,\n    rotlBL,\n    add,\n    add3L,\n    add3H,\n    add4L,\n    add4H,\n    add5H,\n    add5L\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (u64); //# sourceMappingURL=_u64.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/_u64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js":
/*!******************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/cryptoNode.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crypto: () => (/* binding */ crypto)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// See utils.ts for details.\n// The file will throw on node.js 14 and earlier.\n// @ts-ignore\n\nconst crypto = /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === \"object\" && \"webcrypto\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) ? node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto : undefined; //# sourceMappingURL=cryptoNode.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vY3J5cHRvTm9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxvRkFBb0Y7QUFDcEYsNEJBQTRCO0FBQzVCLGlEQUFpRDtBQUNqRCxhQUFhO0FBQ3FCO0FBQzNCLE1BQU1DLFNBQVNELDJNQUFFQSxJQUFJLE9BQU9BLDJNQUFFQSxLQUFLLFlBQVksME5BQWlCQSxHQUFHQSxrREFBWSxHQUFHRyxVQUFVLENBQ25HLHNDQUFzQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHVzLXN3YXAtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vY3J5cHRvTm9kZS5qcz9kYTVkIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFdlIHVzZSBXZWJDcnlwdG8gYWthIGdsb2JhbFRoaXMuY3J5cHRvLCB3aGljaCBleGlzdHMgaW4gYnJvd3NlcnMgYW5kIG5vZGUuanMgMTYrLlxuLy8gU2VlIHV0aWxzLnRzIGZvciBkZXRhaWxzLlxuLy8gVGhlIGZpbGUgd2lsbCB0aHJvdyBvbiBub2RlLmpzIDE0IGFuZCBlYXJsaWVyLlxuLy8gQHRzLWlnbm9yZVxuaW1wb3J0ICogYXMgbmMgZnJvbSAnbm9kZTpjcnlwdG8nO1xuZXhwb3J0IGNvbnN0IGNyeXB0byA9IG5jICYmIHR5cGVvZiBuYyA9PT0gJ29iamVjdCcgJiYgJ3dlYmNyeXB0bycgaW4gbmMgPyBuYy53ZWJjcnlwdG8gOiB1bmRlZmluZWQ7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcnlwdG9Ob2RlLmpzLm1hcCJdLCJuYW1lcyI6WyJuYyIsImNyeXB0byIsIndlYmNyeXB0byIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/hmac.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/hmac.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HMAC: () => (/* binding */ HMAC),\n/* harmony export */   hmac: () => (/* binding */ hmac)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n\n\n// HMAC (RFC 2104)\nclass HMAC extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(hash, _key){\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.hash)(hash);\n        const key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== \"function\") throw new Error(\"Expected instance of class which extends utils.Hash\");\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for(let i = 0; i < pad.length; i++)pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for(let i = 0; i < pad.length; i++)pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        pad.fill(0);\n    }\n    update(buf) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.exists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.exists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n */ const hmac = (hash, key, message)=>new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key)=>new HMAC(hash, key); //# sourceMappingURL=hmac.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/hmac.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/sha256.js":
/*!**************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/sha256.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sha224: () => (/* binding */ sha224),\n/* harmony export */   sha256: () => (/* binding */ sha256)\n/* harmony export */ });\n/* harmony import */ var _sha2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_sha2.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_sha2.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n\n\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n// Choice: a ? b : c\nconst Chi = (a, b, c)=>a & b ^ ~a & c;\n// Majority function, true if any two inpust is true\nconst Maj = (a, b, c)=>a & b ^ a & c ^ b & c;\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98,\n    0x71374491,\n    0xb5c0fbcf,\n    0xe9b5dba5,\n    0x3956c25b,\n    0x59f111f1,\n    0x923f82a4,\n    0xab1c5ed5,\n    0xd807aa98,\n    0x12835b01,\n    0x243185be,\n    0x550c7dc3,\n    0x72be5d74,\n    0x80deb1fe,\n    0x9bdc06a7,\n    0xc19bf174,\n    0xe49b69c1,\n    0xefbe4786,\n    0x0fc19dc6,\n    0x240ca1cc,\n    0x2de92c6f,\n    0x4a7484aa,\n    0x5cb0a9dc,\n    0x76f988da,\n    0x983e5152,\n    0xa831c66d,\n    0xb00327c8,\n    0xbf597fc7,\n    0xc6e00bf3,\n    0xd5a79147,\n    0x06ca6351,\n    0x14292967,\n    0x27b70a85,\n    0x2e1b2138,\n    0x4d2c6dfc,\n    0x53380d13,\n    0x650a7354,\n    0x766a0abb,\n    0x81c2c92e,\n    0x92722c85,\n    0xa2bfe8a1,\n    0xa81a664b,\n    0xc24b8b70,\n    0xc76c51a3,\n    0xd192e819,\n    0xd6990624,\n    0xf40e3585,\n    0x106aa070,\n    0x19a4c116,\n    0x1e376c08,\n    0x2748774c,\n    0x34b0bcb5,\n    0x391c0cb3,\n    0x4ed8aa4a,\n    0x5b9cca4f,\n    0x682e6ff3,\n    0x748f82ee,\n    0x78a5636f,\n    0x84c87814,\n    0x8cc70208,\n    0x90befffa,\n    0xa4506ceb,\n    0xbef9a3f7,\n    0xc67178f2\n]);\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667,\n    0xbb67ae85,\n    0x3c6ef372,\n    0xa54ff53a,\n    0x510e527f,\n    0x9b05688c,\n    0x1f83d9ab,\n    0x5be0cd19\n]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends _sha2_js__WEBPACK_IMPORTED_MODULE_0__.SHA2 {\n    constructor(){\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = IV[0] | 0;\n        this.B = IV[1] | 0;\n        this.C = IV[2] | 0;\n        this.D = IV[3] | 0;\n        this.E = IV[4] | 0;\n        this.F = IV[5] | 0;\n        this.G = IV[6] | 0;\n        this.H = IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [\n            A,\n            B,\n            C,\n            D,\n            E,\n            F,\n            G,\n            H\n        ];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for(let i = 0; i < 16; i++, offset += 4)SHA256_W[i] = view.getUint32(offset, false);\n        for(let i = 16; i < 64; i++){\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 7) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 18) ^ W15 >>> 3;\n            const s1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 17) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 19) ^ W2 >>> 10;\n            SHA256_W[i] = s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16] | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for(let i = 0; i < 64; i++){\n            const sigma1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 6) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 11) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 25);\n            const T1 = H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i] | 0;\n            const sigma0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 2) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 13) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 22);\n            const T2 = sigma0 + Maj(A, B, C) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = D + T1 | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = T1 + T2 | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = A + this.A | 0;\n        B = B + this.B | 0;\n        C = C + this.C | 0;\n        D = D + this.D | 0;\n        E = E + this.E | 0;\n        F = F + this.F | 0;\n        G = G + this.G | 0;\n        H = H + this.H | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n// Constants from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\nclass SHA224 extends SHA256 {\n    constructor(){\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */ const sha256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(()=>new SHA256());\nconst sha224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(()=>new SHA224()); //# sourceMappingURL=sha256.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/sha256.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/sha3.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/sha3.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Keccak: () => (/* binding */ Keccak),\n/* harmony export */   keccakP: () => (/* binding */ keccakP),\n/* harmony export */   keccak_224: () => (/* binding */ keccak_224),\n/* harmony export */   keccak_256: () => (/* binding */ keccak_256),\n/* harmony export */   keccak_384: () => (/* binding */ keccak_384),\n/* harmony export */   keccak_512: () => (/* binding */ keccak_512),\n/* harmony export */   sha3_224: () => (/* binding */ sha3_224),\n/* harmony export */   sha3_256: () => (/* binding */ sha3_256),\n/* harmony export */   sha3_384: () => (/* binding */ sha3_384),\n/* harmony export */   sha3_512: () => (/* binding */ sha3_512),\n/* harmony export */   shake128: () => (/* binding */ shake128),\n/* harmony export */   shake256: () => (/* binding */ shake256)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./_assert.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_assert.js\");\n/* harmony import */ var _u64_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_u64.js */ \"(ssr)/./node_modules/@noble/hashes/esm/_u64.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/esm/utils.js\");\n\n\n\n// SHA3 (keccak) is based on a new design: basically, the internal state is bigger than output size.\n// It's called a sponge function.\n// Various per round constants calculations\nconst [SHA3_PI, SHA3_ROTL, _SHA3_IOTA] = [\n    [],\n    [],\n    []\n];\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nconst _7n = /* @__PURE__ */ BigInt(7);\nconst _256n = /* @__PURE__ */ BigInt(256);\nconst _0x71n = /* @__PURE__ */ BigInt(0x71);\nfor(let round = 0, R = _1n, x = 1, y = 0; round < 24; round++){\n    // Pi\n    [x, y] = [\n        y,\n        (2 * x + 3 * y) % 5\n    ];\n    SHA3_PI.push(2 * (5 * y + x));\n    // Rotational\n    SHA3_ROTL.push((round + 1) * (round + 2) / 2 % 64);\n    // Iota\n    let t = _0n;\n    for(let j = 0; j < 7; j++){\n        R = (R << _1n ^ (R >> _7n) * _0x71n) % _256n;\n        if (R & _2n) t ^= _1n << (_1n << /* @__PURE__ */ BigInt(j)) - _1n;\n    }\n    _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */ (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.split)(_SHA3_IOTA, true);\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s)=>s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBH)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSH)(h, l, s);\nconst rotlL = (h, l, s)=>s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBL)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSL)(h, l, s);\n// Same as keccakf1600, but allows to skip some rounds\nfunction keccakP(s, rounds = 24) {\n    const B = new Uint32Array(5 * 2);\n    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n    for(let round = 24 - rounds; round < 24; round++){\n        // Theta θ\n        for(let x = 0; x < 10; x++)B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n        for(let x = 0; x < 10; x += 2){\n            const idx1 = (x + 8) % 10;\n            const idx0 = (x + 2) % 10;\n            const B0 = B[idx0];\n            const B1 = B[idx0 + 1];\n            const Th = rotlH(B0, B1, 1) ^ B[idx1];\n            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n            for(let y = 0; y < 50; y += 10){\n                s[x + y] ^= Th;\n                s[x + y + 1] ^= Tl;\n            }\n        }\n        // Rho (ρ) and Pi (π)\n        let curH = s[2];\n        let curL = s[3];\n        for(let t = 0; t < 24; t++){\n            const shift = SHA3_ROTL[t];\n            const Th = rotlH(curH, curL, shift);\n            const Tl = rotlL(curH, curL, shift);\n            const PI = SHA3_PI[t];\n            curH = s[PI];\n            curL = s[PI + 1];\n            s[PI] = Th;\n            s[PI + 1] = Tl;\n        }\n        // Chi (χ)\n        for(let y = 0; y < 50; y += 10){\n            for(let x = 0; x < 10; x++)B[x] = s[y + x];\n            for(let x = 0; x < 10; x++)s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n        }\n        // Iota (ι)\n        s[0] ^= SHA3_IOTA_H[round];\n        s[1] ^= SHA3_IOTA_L[round];\n    }\n    B.fill(0);\n}\nclass Keccak extends _utils_js__WEBPACK_IMPORTED_MODULE_1__.Hash {\n    // NOTE: we accept arguments in bytes instead of bits here.\n    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24){\n        super();\n        this.blockLen = blockLen;\n        this.suffix = suffix;\n        this.outputLen = outputLen;\n        this.enableXOF = enableXOF;\n        this.rounds = rounds;\n        this.pos = 0;\n        this.posOut = 0;\n        this.finished = false;\n        this.destroyed = false;\n        // Can be passed from user as dkLen\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.number)(outputLen);\n        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n        if (0 >= this.blockLen || this.blockLen >= 200) throw new Error(\"Sha3 supports only keccak-f1600 function\");\n        this.state = new Uint8Array(200);\n        this.state32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(this.state);\n    }\n    keccak() {\n        keccakP(this.state32, this.rounds);\n        this.posOut = 0;\n        this.pos = 0;\n    }\n    update(data) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.exists)(this);\n        const { blockLen, state } = this;\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(data);\n        const len = data.length;\n        for(let pos = 0; pos < len;){\n            const take = Math.min(blockLen - this.pos, len - pos);\n            for(let i = 0; i < take; i++)state[this.pos++] ^= data[pos++];\n            if (this.pos === blockLen) this.keccak();\n        }\n        return this;\n    }\n    finish() {\n        if (this.finished) return;\n        this.finished = true;\n        const { state, suffix, pos, blockLen } = this;\n        // Do the padding\n        state[pos] ^= suffix;\n        if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n        state[blockLen - 1] ^= 0x80;\n        this.keccak();\n    }\n    writeInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.exists)(this, false);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.bytes)(out);\n        this.finish();\n        const bufferOut = this.state;\n        const { blockLen } = this;\n        for(let pos = 0, len = out.length; pos < len;){\n            if (this.posOut >= blockLen) this.keccak();\n            const take = Math.min(blockLen - this.posOut, len - pos);\n            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n            this.posOut += take;\n            pos += take;\n        }\n        return out;\n    }\n    xofInto(out) {\n        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n        if (!this.enableXOF) throw new Error(\"XOF is not possible for this instance\");\n        return this.writeInto(out);\n    }\n    xof(bytes) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.number)(bytes);\n        return this.xofInto(new Uint8Array(bytes));\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.output)(out, this);\n        if (this.finished) throw new Error(\"digest() was already called\");\n        this.writeInto(out);\n        this.destroy();\n        return out;\n    }\n    digest() {\n        return this.digestInto(new Uint8Array(this.outputLen));\n    }\n    destroy() {\n        this.destroyed = true;\n        this.state.fill(0);\n    }\n    _cloneInto(to) {\n        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n        to.state32.set(this.state32);\n        to.pos = this.pos;\n        to.posOut = this.posOut;\n        to.finished = this.finished;\n        to.rounds = rounds;\n        // Suffix can change in cSHAKE\n        to.suffix = suffix;\n        to.outputLen = outputLen;\n        to.enableXOF = enableXOF;\n        to.destroyed = this.destroyed;\n        return to;\n    }\n}\nconst gen = (suffix, blockLen, outputLen)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(()=>new Keccak(blockLen, suffix, outputLen));\nconst sha3_224 = /* @__PURE__ */ gen(0x06, 144, 224 / 8);\n/**\n * SHA3-256 hash function\n * @param message - that would be hashed\n */ const sha3_256 = /* @__PURE__ */ gen(0x06, 136, 256 / 8);\nconst sha3_384 = /* @__PURE__ */ gen(0x06, 104, 384 / 8);\nconst sha3_512 = /* @__PURE__ */ gen(0x06, 72, 512 / 8);\nconst keccak_224 = /* @__PURE__ */ gen(0x01, 144, 224 / 8);\n/**\n * keccak-256 hash function. Different from SHA3-256.\n * @param message - that would be hashed\n */ const keccak_256 = /* @__PURE__ */ gen(0x01, 136, 256 / 8);\nconst keccak_384 = /* @__PURE__ */ gen(0x01, 104, 384 / 8);\nconst keccak_512 = /* @__PURE__ */ gen(0x01, 72, 512 / 8);\nconst genShake = (suffix, blockLen, outputLen)=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapXOFConstructorWithOpts)((opts = {})=>new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\nconst shake128 = /* @__PURE__ */ genShake(0x1f, 168, 128 / 8);\nconst shake256 = /* @__PURE__ */ genShake(0x1f, 136, 256 / 8); //# sourceMappingURL=sha3.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vc2hhMy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNkQ7QUFDSztBQUM0QjtBQUM5RixvR0FBb0c7QUFDcEcsaUNBQWlDO0FBQ2pDLDJDQUEyQztBQUMzQyxNQUFNLENBQUNjLFNBQVNDLFdBQVdDLFdBQVcsR0FBRztJQUFDLEVBQUU7SUFBRSxFQUFFO0lBQUUsRUFBRTtDQUFDO0FBQ3JELE1BQU1DLE1BQU0sYUFBYSxHQUFHQyxPQUFPO0FBQ25DLE1BQU1DLE1BQU0sYUFBYSxHQUFHRCxPQUFPO0FBQ25DLE1BQU1FLE1BQU0sYUFBYSxHQUFHRixPQUFPO0FBQ25DLE1BQU1HLE1BQU0sYUFBYSxHQUFHSCxPQUFPO0FBQ25DLE1BQU1JLFFBQVEsYUFBYSxHQUFHSixPQUFPO0FBQ3JDLE1BQU1LLFNBQVMsYUFBYSxHQUFHTCxPQUFPO0FBQ3RDLElBQUssSUFBSU0sUUFBUSxHQUFHQyxJQUFJTixLQUFLTyxJQUFJLEdBQUdDLElBQUksR0FBR0gsUUFBUSxJQUFJQSxRQUFTO0lBQzVELEtBQUs7SUFDTCxDQUFDRSxHQUFHQyxFQUFFLEdBQUc7UUFBQ0E7UUFBSSxLQUFJRCxJQUFJLElBQUlDLENBQUFBLElBQUs7S0FBRTtJQUNqQ2IsUUFBUWMsSUFBSSxDQUFDLElBQUssS0FBSUQsSUFBSUQsQ0FBQUE7SUFDMUIsYUFBYTtJQUNiWCxVQUFVYSxJQUFJLENBQUMsQ0FBR0osUUFBUSxLQUFNQSxDQUFBQSxRQUFRLEtBQU0sSUFBSztJQUNuRCxPQUFPO0lBQ1AsSUFBSUssSUFBSVo7SUFDUixJQUFLLElBQUlhLElBQUksR0FBR0EsSUFBSSxHQUFHQSxJQUFLO1FBQ3hCTCxJQUFJLENBQUMsS0FBTU4sTUFBUSxDQUFDTSxLQUFLSixHQUFFLElBQUtFLE1BQU0sSUFBS0Q7UUFDM0MsSUFBSUcsSUFBSUwsS0FDSlMsS0FBS1YsT0FBUSxDQUFDQSxPQUFPLGFBQWEsR0FBR0QsT0FBT1ksRUFBQyxJQUFLWDtJQUMxRDtJQUNBSCxXQUFXWSxJQUFJLENBQUNDO0FBQ3BCO0FBQ0EsTUFBTSxDQUFDRSxhQUFhQyxZQUFZLEdBQUcsYUFBYSxHQUFHeEIsOENBQUtBLENBQUNRLFlBQVk7QUFDckUsb0NBQW9DO0FBQ3BDLE1BQU1pQixRQUFRLENBQUNDLEdBQUdDLEdBQUdDLElBQU9BLElBQUksS0FBS2hDLCtDQUFNQSxDQUFDOEIsR0FBR0MsR0FBR0MsS0FBSzlCLCtDQUFNQSxDQUFDNEIsR0FBR0MsR0FBR0M7QUFDcEUsTUFBTUMsUUFBUSxDQUFDSCxHQUFHQyxHQUFHQyxJQUFPQSxJQUFJLEtBQUsvQiwrQ0FBTUEsQ0FBQzZCLEdBQUdDLEdBQUdDLEtBQUs3QiwrQ0FBTUEsQ0FBQzJCLEdBQUdDLEdBQUdDO0FBQ3BFLHNEQUFzRDtBQUMvQyxTQUFTRSxRQUFRRixDQUFDLEVBQUVHLFNBQVMsRUFBRTtJQUNsQyxNQUFNQyxJQUFJLElBQUlDLFlBQVksSUFBSTtJQUM5Qiw4RkFBOEY7SUFDOUYsSUFBSyxJQUFJakIsUUFBUSxLQUFLZSxRQUFRZixRQUFRLElBQUlBLFFBQVM7UUFDL0MsVUFBVTtRQUNWLElBQUssSUFBSUUsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQ3BCYyxDQUFDLENBQUNkLEVBQUUsR0FBR1UsQ0FBQyxDQUFDVixFQUFFLEdBQUdVLENBQUMsQ0FBQ1YsSUFBSSxHQUFHLEdBQUdVLENBQUMsQ0FBQ1YsSUFBSSxHQUFHLEdBQUdVLENBQUMsQ0FBQ1YsSUFBSSxHQUFHLEdBQUdVLENBQUMsQ0FBQ1YsSUFBSSxHQUFHO1FBQy9ELElBQUssSUFBSUEsSUFBSSxHQUFHQSxJQUFJLElBQUlBLEtBQUssRUFBRztZQUM1QixNQUFNZ0IsT0FBTyxDQUFDaEIsSUFBSSxLQUFLO1lBQ3ZCLE1BQU1pQixPQUFPLENBQUNqQixJQUFJLEtBQUs7WUFDdkIsTUFBTWtCLEtBQUtKLENBQUMsQ0FBQ0csS0FBSztZQUNsQixNQUFNRSxLQUFLTCxDQUFDLENBQUNHLE9BQU8sRUFBRTtZQUN0QixNQUFNRyxLQUFLYixNQUFNVyxJQUFJQyxJQUFJLEtBQUtMLENBQUMsQ0FBQ0UsS0FBSztZQUNyQyxNQUFNSyxLQUFLVixNQUFNTyxJQUFJQyxJQUFJLEtBQUtMLENBQUMsQ0FBQ0UsT0FBTyxFQUFFO1lBQ3pDLElBQUssSUFBSWYsSUFBSSxHQUFHQSxJQUFJLElBQUlBLEtBQUssR0FBSTtnQkFDN0JTLENBQUMsQ0FBQ1YsSUFBSUMsRUFBRSxJQUFJbUI7Z0JBQ1pWLENBQUMsQ0FBQ1YsSUFBSUMsSUFBSSxFQUFFLElBQUlvQjtZQUNwQjtRQUNKO1FBQ0EscUJBQXFCO1FBQ3JCLElBQUlDLE9BQU9aLENBQUMsQ0FBQyxFQUFFO1FBQ2YsSUFBSWEsT0FBT2IsQ0FBQyxDQUFDLEVBQUU7UUFDZixJQUFLLElBQUlQLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUFLO1lBQ3pCLE1BQU1xQixRQUFRbkMsU0FBUyxDQUFDYyxFQUFFO1lBQzFCLE1BQU1pQixLQUFLYixNQUFNZSxNQUFNQyxNQUFNQztZQUM3QixNQUFNSCxLQUFLVixNQUFNVyxNQUFNQyxNQUFNQztZQUM3QixNQUFNQyxLQUFLckMsT0FBTyxDQUFDZSxFQUFFO1lBQ3JCbUIsT0FBT1osQ0FBQyxDQUFDZSxHQUFHO1lBQ1pGLE9BQU9iLENBQUMsQ0FBQ2UsS0FBSyxFQUFFO1lBQ2hCZixDQUFDLENBQUNlLEdBQUcsR0FBR0w7WUFDUlYsQ0FBQyxDQUFDZSxLQUFLLEVBQUUsR0FBR0o7UUFDaEI7UUFDQSxVQUFVO1FBQ1YsSUFBSyxJQUFJcEIsSUFBSSxHQUFHQSxJQUFJLElBQUlBLEtBQUssR0FBSTtZQUM3QixJQUFLLElBQUlELElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUNwQmMsQ0FBQyxDQUFDZCxFQUFFLEdBQUdVLENBQUMsQ0FBQ1QsSUFBSUQsRUFBRTtZQUNuQixJQUFLLElBQUlBLElBQUksR0FBR0EsSUFBSSxJQUFJQSxJQUNwQlUsQ0FBQyxDQUFDVCxJQUFJRCxFQUFFLElBQUksQ0FBQ2MsQ0FBQyxDQUFDLENBQUNkLElBQUksS0FBSyxHQUFHLEdBQUdjLENBQUMsQ0FBQyxDQUFDZCxJQUFJLEtBQUssR0FBRztRQUN0RDtRQUNBLFdBQVc7UUFDWFUsQ0FBQyxDQUFDLEVBQUUsSUFBSUwsV0FBVyxDQUFDUCxNQUFNO1FBQzFCWSxDQUFDLENBQUMsRUFBRSxJQUFJSixXQUFXLENBQUNSLE1BQU07SUFDOUI7SUFDQWdCLEVBQUVZLElBQUksQ0FBQztBQUNYO0FBQ08sTUFBTUMsZUFBZTVDLDJDQUFJQTtJQUM1QiwyREFBMkQ7SUFDM0Q2QyxZQUFZQyxRQUFRLEVBQUVDLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxZQUFZLEtBQUssRUFBRW5CLFNBQVMsRUFBRSxDQUFFO1FBQ3JFLEtBQUs7UUFDTCxJQUFJLENBQUNnQixRQUFRLEdBQUdBO1FBQ2hCLElBQUksQ0FBQ0MsTUFBTSxHQUFHQTtRQUNkLElBQUksQ0FBQ0MsU0FBUyxHQUFHQTtRQUNqQixJQUFJLENBQUNDLFNBQVMsR0FBR0E7UUFDakIsSUFBSSxDQUFDbkIsTUFBTSxHQUFHQTtRQUNkLElBQUksQ0FBQ29CLEdBQUcsR0FBRztRQUNYLElBQUksQ0FBQ0MsTUFBTSxHQUFHO1FBQ2QsSUFBSSxDQUFDQyxRQUFRLEdBQUc7UUFDaEIsSUFBSSxDQUFDQyxTQUFTLEdBQUc7UUFDakIsbUNBQW1DO1FBQ25DNUQsa0RBQU1BLENBQUN1RDtRQUNQLHVEQUF1RDtRQUN2RCxJQUFJLEtBQUssSUFBSSxDQUFDRixRQUFRLElBQUksSUFBSSxDQUFDQSxRQUFRLElBQUksS0FDdkMsTUFBTSxJQUFJUSxNQUFNO1FBQ3BCLElBQUksQ0FBQ0MsS0FBSyxHQUFHLElBQUlDLFdBQVc7UUFDNUIsSUFBSSxDQUFDQyxPQUFPLEdBQUd4RCw4Q0FBR0EsQ0FBQyxJQUFJLENBQUNzRCxLQUFLO0lBQ2pDO0lBQ0FHLFNBQVM7UUFDTDdCLFFBQVEsSUFBSSxDQUFDNEIsT0FBTyxFQUFFLElBQUksQ0FBQzNCLE1BQU07UUFDakMsSUFBSSxDQUFDcUIsTUFBTSxHQUFHO1FBQ2QsSUFBSSxDQUFDRCxHQUFHLEdBQUc7SUFDZjtJQUNBUyxPQUFPQyxJQUFJLEVBQUU7UUFDVHBFLGtEQUFNQSxDQUFDLElBQUk7UUFDWCxNQUFNLEVBQUVzRCxRQUFRLEVBQUVTLEtBQUssRUFBRSxHQUFHLElBQUk7UUFDaENLLE9BQU8xRCxrREFBT0EsQ0FBQzBEO1FBQ2YsTUFBTUMsTUFBTUQsS0FBS0UsTUFBTTtRQUN2QixJQUFLLElBQUlaLE1BQU0sR0FBR0EsTUFBTVcsS0FBTTtZQUMxQixNQUFNRSxPQUFPQyxLQUFLQyxHQUFHLENBQUNuQixXQUFXLElBQUksQ0FBQ0ksR0FBRyxFQUFFVyxNQUFNWDtZQUNqRCxJQUFLLElBQUlnQixJQUFJLEdBQUdBLElBQUlILE1BQU1HLElBQ3RCWCxLQUFLLENBQUMsSUFBSSxDQUFDTCxHQUFHLEdBQUcsSUFBSVUsSUFBSSxDQUFDVixNQUFNO1lBQ3BDLElBQUksSUFBSSxDQUFDQSxHQUFHLEtBQUtKLFVBQ2IsSUFBSSxDQUFDWSxNQUFNO1FBQ25CO1FBQ0EsT0FBTyxJQUFJO0lBQ2Y7SUFDQVMsU0FBUztRQUNMLElBQUksSUFBSSxDQUFDZixRQUFRLEVBQ2I7UUFDSixJQUFJLENBQUNBLFFBQVEsR0FBRztRQUNoQixNQUFNLEVBQUVHLEtBQUssRUFBRVIsTUFBTSxFQUFFRyxHQUFHLEVBQUVKLFFBQVEsRUFBRSxHQUFHLElBQUk7UUFDN0MsaUJBQWlCO1FBQ2pCUyxLQUFLLENBQUNMLElBQUksSUFBSUg7UUFDZCxJQUFJLENBQUNBLFNBQVMsSUFBRyxNQUFPLEtBQUtHLFFBQVFKLFdBQVcsR0FDNUMsSUFBSSxDQUFDWSxNQUFNO1FBQ2ZILEtBQUssQ0FBQ1QsV0FBVyxFQUFFLElBQUk7UUFDdkIsSUFBSSxDQUFDWSxNQUFNO0lBQ2Y7SUFDQVUsVUFBVUMsR0FBRyxFQUFFO1FBQ1g3RSxrREFBTUEsQ0FBQyxJQUFJLEVBQUU7UUFDYkQsaURBQUtBLENBQUM4RTtRQUNOLElBQUksQ0FBQ0YsTUFBTTtRQUNYLE1BQU1HLFlBQVksSUFBSSxDQUFDZixLQUFLO1FBQzVCLE1BQU0sRUFBRVQsUUFBUSxFQUFFLEdBQUcsSUFBSTtRQUN6QixJQUFLLElBQUlJLE1BQU0sR0FBR1csTUFBTVEsSUFBSVAsTUFBTSxFQUFFWixNQUFNVyxLQUFNO1lBQzVDLElBQUksSUFBSSxDQUFDVixNQUFNLElBQUlMLFVBQ2YsSUFBSSxDQUFDWSxNQUFNO1lBQ2YsTUFBTUssT0FBT0MsS0FBS0MsR0FBRyxDQUFDbkIsV0FBVyxJQUFJLENBQUNLLE1BQU0sRUFBRVUsTUFBTVg7WUFDcERtQixJQUFJRSxHQUFHLENBQUNELFVBQVVFLFFBQVEsQ0FBQyxJQUFJLENBQUNyQixNQUFNLEVBQUUsSUFBSSxDQUFDQSxNQUFNLEdBQUdZLE9BQU9iO1lBQzdELElBQUksQ0FBQ0MsTUFBTSxJQUFJWTtZQUNmYixPQUFPYTtRQUNYO1FBQ0EsT0FBT007SUFDWDtJQUNBSSxRQUFRSixHQUFHLEVBQUU7UUFDVCxrRkFBa0Y7UUFDbEYsSUFBSSxDQUFDLElBQUksQ0FBQ3BCLFNBQVMsRUFDZixNQUFNLElBQUlLLE1BQU07UUFDcEIsT0FBTyxJQUFJLENBQUNjLFNBQVMsQ0FBQ0M7SUFDMUI7SUFDQUssSUFBSW5GLEtBQUssRUFBRTtRQUNQRSxrREFBTUEsQ0FBQ0Y7UUFDUCxPQUFPLElBQUksQ0FBQ2tGLE9BQU8sQ0FBQyxJQUFJakIsV0FBV2pFO0lBQ3ZDO0lBQ0FvRixXQUFXTixHQUFHLEVBQUU7UUFDWjNFLGtEQUFNQSxDQUFDMkUsS0FBSyxJQUFJO1FBQ2hCLElBQUksSUFBSSxDQUFDakIsUUFBUSxFQUNiLE1BQU0sSUFBSUUsTUFBTTtRQUNwQixJQUFJLENBQUNjLFNBQVMsQ0FBQ0M7UUFDZixJQUFJLENBQUNPLE9BQU87UUFDWixPQUFPUDtJQUNYO0lBQ0FRLFNBQVM7UUFDTCxPQUFPLElBQUksQ0FBQ0YsVUFBVSxDQUFDLElBQUluQixXQUFXLElBQUksQ0FBQ1IsU0FBUztJQUN4RDtJQUNBNEIsVUFBVTtRQUNOLElBQUksQ0FBQ3ZCLFNBQVMsR0FBRztRQUNqQixJQUFJLENBQUNFLEtBQUssQ0FBQ1osSUFBSSxDQUFDO0lBQ3BCO0lBQ0FtQyxXQUFXQyxFQUFFLEVBQUU7UUFDWCxNQUFNLEVBQUVqQyxRQUFRLEVBQUVDLE1BQU0sRUFBRUMsU0FBUyxFQUFFbEIsTUFBTSxFQUFFbUIsU0FBUyxFQUFFLEdBQUcsSUFBSTtRQUMvRDhCLE1BQU9BLENBQUFBLEtBQUssSUFBSW5DLE9BQU9FLFVBQVVDLFFBQVFDLFdBQVdDLFdBQVduQixPQUFNO1FBQ3JFaUQsR0FBR3RCLE9BQU8sQ0FBQ2MsR0FBRyxDQUFDLElBQUksQ0FBQ2QsT0FBTztRQUMzQnNCLEdBQUc3QixHQUFHLEdBQUcsSUFBSSxDQUFDQSxHQUFHO1FBQ2pCNkIsR0FBRzVCLE1BQU0sR0FBRyxJQUFJLENBQUNBLE1BQU07UUFDdkI0QixHQUFHM0IsUUFBUSxHQUFHLElBQUksQ0FBQ0EsUUFBUTtRQUMzQjJCLEdBQUdqRCxNQUFNLEdBQUdBO1FBQ1osOEJBQThCO1FBQzlCaUQsR0FBR2hDLE1BQU0sR0FBR0E7UUFDWmdDLEdBQUcvQixTQUFTLEdBQUdBO1FBQ2YrQixHQUFHOUIsU0FBUyxHQUFHQTtRQUNmOEIsR0FBRzFCLFNBQVMsR0FBRyxJQUFJLENBQUNBLFNBQVM7UUFDN0IsT0FBTzBCO0lBQ1g7QUFDSjtBQUNBLE1BQU1DLE1BQU0sQ0FBQ2pDLFFBQVFELFVBQVVFLFlBQWM3QywwREFBZUEsQ0FBQyxJQUFNLElBQUl5QyxPQUFPRSxVQUFVQyxRQUFRQztBQUN6RixNQUFNaUMsV0FBVyxhQUFhLEdBQUdELElBQUksTUFBTSxLQUFLLE1BQU0sR0FBRztBQUNoRTs7O0NBR0MsR0FDTSxNQUFNRSxXQUFXLGFBQWEsR0FBR0YsSUFBSSxNQUFNLEtBQUssTUFBTSxHQUFHO0FBQ3pELE1BQU1HLFdBQVcsYUFBYSxHQUFHSCxJQUFJLE1BQU0sS0FBSyxNQUFNLEdBQUc7QUFDekQsTUFBTUksV0FBVyxhQUFhLEdBQUdKLElBQUksTUFBTSxJQUFJLE1BQU0sR0FBRztBQUN4RCxNQUFNSyxhQUFhLGFBQWEsR0FBR0wsSUFBSSxNQUFNLEtBQUssTUFBTSxHQUFHO0FBQ2xFOzs7Q0FHQyxHQUNNLE1BQU1NLGFBQWEsYUFBYSxHQUFHTixJQUFJLE1BQU0sS0FBSyxNQUFNLEdBQUc7QUFDM0QsTUFBTU8sYUFBYSxhQUFhLEdBQUdQLElBQUksTUFBTSxLQUFLLE1BQU0sR0FBRztBQUMzRCxNQUFNUSxhQUFhLGFBQWEsR0FBR1IsSUFBSSxNQUFNLElBQUksTUFBTSxHQUFHO0FBQ2pFLE1BQU1TLFdBQVcsQ0FBQzFDLFFBQVFELFVBQVVFLFlBQWM1QyxxRUFBMEJBLENBQUMsQ0FBQ3NGLE9BQU8sQ0FBQyxDQUFDLEdBQUssSUFBSTlDLE9BQU9FLFVBQVVDLFFBQVEyQyxLQUFLQyxLQUFLLEtBQUtDLFlBQVk1QyxZQUFZMEMsS0FBS0MsS0FBSyxFQUFFO0FBQ3JLLE1BQU1FLFdBQVcsYUFBYSxHQUFHSixTQUFTLE1BQU0sS0FBSyxNQUFNLEdBQUc7QUFDOUQsTUFBTUssV0FBVyxhQUFhLEdBQUdMLFNBQVMsTUFBTSxLQUFLLE1BQU0sR0FBRyxDQUNyRSxnQ0FBZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh1cy1zd2FwLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0Bub2JsZS9oYXNoZXMvZXNtL3NoYTMuanM/YjQyZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBieXRlcywgZXhpc3RzLCBudW1iZXIsIG91dHB1dCB9IGZyb20gJy4vX2Fzc2VydC5qcyc7XG5pbXBvcnQgeyByb3RsQkgsIHJvdGxCTCwgcm90bFNILCByb3RsU0wsIHNwbGl0IH0gZnJvbSAnLi9fdTY0LmpzJztcbmltcG9ydCB7IEhhc2gsIHUzMiwgdG9CeXRlcywgd3JhcENvbnN0cnVjdG9yLCB3cmFwWE9GQ29uc3RydWN0b3JXaXRoT3B0cywgfSBmcm9tICcuL3V0aWxzLmpzJztcbi8vIFNIQTMgKGtlY2NhaykgaXMgYmFzZWQgb24gYSBuZXcgZGVzaWduOiBiYXNpY2FsbHksIHRoZSBpbnRlcm5hbCBzdGF0ZSBpcyBiaWdnZXIgdGhhbiBvdXRwdXQgc2l6ZS5cbi8vIEl0J3MgY2FsbGVkIGEgc3BvbmdlIGZ1bmN0aW9uLlxuLy8gVmFyaW91cyBwZXIgcm91bmQgY29uc3RhbnRzIGNhbGN1bGF0aW9uc1xuY29uc3QgW1NIQTNfUEksIFNIQTNfUk9UTCwgX1NIQTNfSU9UQV0gPSBbW10sIFtdLCBbXV07XG5jb25zdCBfMG4gPSAvKiBAX19QVVJFX18gKi8gQmlnSW50KDApO1xuY29uc3QgXzFuID0gLyogQF9fUFVSRV9fICovIEJpZ0ludCgxKTtcbmNvbnN0IF8ybiA9IC8qIEBfX1BVUkVfXyAqLyBCaWdJbnQoMik7XG5jb25zdCBfN24gPSAvKiBAX19QVVJFX18gKi8gQmlnSW50KDcpO1xuY29uc3QgXzI1Nm4gPSAvKiBAX19QVVJFX18gKi8gQmlnSW50KDI1Nik7XG5jb25zdCBfMHg3MW4gPSAvKiBAX19QVVJFX18gKi8gQmlnSW50KDB4NzEpO1xuZm9yIChsZXQgcm91bmQgPSAwLCBSID0gXzFuLCB4ID0gMSwgeSA9IDA7IHJvdW5kIDwgMjQ7IHJvdW5kKyspIHtcbiAgICAvLyBQaVxuICAgIFt4LCB5XSA9IFt5LCAoMiAqIHggKyAzICogeSkgJSA1XTtcbiAgICBTSEEzX1BJLnB1c2goMiAqICg1ICogeSArIHgpKTtcbiAgICAvLyBSb3RhdGlvbmFsXG4gICAgU0hBM19ST1RMLnB1c2goKCgocm91bmQgKyAxKSAqIChyb3VuZCArIDIpKSAvIDIpICUgNjQpO1xuICAgIC8vIElvdGFcbiAgICBsZXQgdCA9IF8wbjtcbiAgICBmb3IgKGxldCBqID0gMDsgaiA8IDc7IGorKykge1xuICAgICAgICBSID0gKChSIDw8IF8xbikgXiAoKFIgPj4gXzduKSAqIF8weDcxbikpICUgXzI1Nm47XG4gICAgICAgIGlmIChSICYgXzJuKVxuICAgICAgICAgICAgdCBePSBfMW4gPDwgKChfMW4gPDwgLyogQF9fUFVSRV9fICovIEJpZ0ludChqKSkgLSBfMW4pO1xuICAgIH1cbiAgICBfU0hBM19JT1RBLnB1c2godCk7XG59XG5jb25zdCBbU0hBM19JT1RBX0gsIFNIQTNfSU9UQV9MXSA9IC8qIEBfX1BVUkVfXyAqLyBzcGxpdChfU0hBM19JT1RBLCB0cnVlKTtcbi8vIExlZnQgcm90YXRpb24gKHdpdGhvdXQgMCwgMzIsIDY0KVxuY29uc3Qgcm90bEggPSAoaCwgbCwgcykgPT4gKHMgPiAzMiA/IHJvdGxCSChoLCBsLCBzKSA6IHJvdGxTSChoLCBsLCBzKSk7XG5jb25zdCByb3RsTCA9IChoLCBsLCBzKSA9PiAocyA+IDMyID8gcm90bEJMKGgsIGwsIHMpIDogcm90bFNMKGgsIGwsIHMpKTtcbi8vIFNhbWUgYXMga2VjY2FrZjE2MDAsIGJ1dCBhbGxvd3MgdG8gc2tpcCBzb21lIHJvdW5kc1xuZXhwb3J0IGZ1bmN0aW9uIGtlY2Nha1Aocywgcm91bmRzID0gMjQpIHtcbiAgICBjb25zdCBCID0gbmV3IFVpbnQzMkFycmF5KDUgKiAyKTtcbiAgICAvLyBOT1RFOiBhbGwgaW5kaWNlcyBhcmUgeDIgc2luY2Ugd2Ugc3RvcmUgc3RhdGUgYXMgdTMyIGluc3RlYWQgb2YgdTY0IChiaWdpbnRzIHRvIHNsb3cgaW4ganMpXG4gICAgZm9yIChsZXQgcm91bmQgPSAyNCAtIHJvdW5kczsgcm91bmQgPCAyNDsgcm91bmQrKykge1xuICAgICAgICAvLyBUaGV0YSDOuFxuICAgICAgICBmb3IgKGxldCB4ID0gMDsgeCA8IDEwOyB4KyspXG4gICAgICAgICAgICBCW3hdID0gc1t4XSBeIHNbeCArIDEwXSBeIHNbeCArIDIwXSBeIHNbeCArIDMwXSBeIHNbeCArIDQwXTtcbiAgICAgICAgZm9yIChsZXQgeCA9IDA7IHggPCAxMDsgeCArPSAyKSB7XG4gICAgICAgICAgICBjb25zdCBpZHgxID0gKHggKyA4KSAlIDEwO1xuICAgICAgICAgICAgY29uc3QgaWR4MCA9ICh4ICsgMikgJSAxMDtcbiAgICAgICAgICAgIGNvbnN0IEIwID0gQltpZHgwXTtcbiAgICAgICAgICAgIGNvbnN0IEIxID0gQltpZHgwICsgMV07XG4gICAgICAgICAgICBjb25zdCBUaCA9IHJvdGxIKEIwLCBCMSwgMSkgXiBCW2lkeDFdO1xuICAgICAgICAgICAgY29uc3QgVGwgPSByb3RsTChCMCwgQjEsIDEpIF4gQltpZHgxICsgMV07XG4gICAgICAgICAgICBmb3IgKGxldCB5ID0gMDsgeSA8IDUwOyB5ICs9IDEwKSB7XG4gICAgICAgICAgICAgICAgc1t4ICsgeV0gXj0gVGg7XG4gICAgICAgICAgICAgICAgc1t4ICsgeSArIDFdIF49IFRsO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIC8vIFJobyAoz4EpIGFuZCBQaSAoz4ApXG4gICAgICAgIGxldCBjdXJIID0gc1syXTtcbiAgICAgICAgbGV0IGN1ckwgPSBzWzNdO1xuICAgICAgICBmb3IgKGxldCB0ID0gMDsgdCA8IDI0OyB0KyspIHtcbiAgICAgICAgICAgIGNvbnN0IHNoaWZ0ID0gU0hBM19ST1RMW3RdO1xuICAgICAgICAgICAgY29uc3QgVGggPSByb3RsSChjdXJILCBjdXJMLCBzaGlmdCk7XG4gICAgICAgICAgICBjb25zdCBUbCA9IHJvdGxMKGN1ckgsIGN1ckwsIHNoaWZ0KTtcbiAgICAgICAgICAgIGNvbnN0IFBJID0gU0hBM19QSVt0XTtcbiAgICAgICAgICAgIGN1ckggPSBzW1BJXTtcbiAgICAgICAgICAgIGN1ckwgPSBzW1BJICsgMV07XG4gICAgICAgICAgICBzW1BJXSA9IFRoO1xuICAgICAgICAgICAgc1tQSSArIDFdID0gVGw7XG4gICAgICAgIH1cbiAgICAgICAgLy8gQ2hpICjPhylcbiAgICAgICAgZm9yIChsZXQgeSA9IDA7IHkgPCA1MDsgeSArPSAxMCkge1xuICAgICAgICAgICAgZm9yIChsZXQgeCA9IDA7IHggPCAxMDsgeCsrKVxuICAgICAgICAgICAgICAgIEJbeF0gPSBzW3kgKyB4XTtcbiAgICAgICAgICAgIGZvciAobGV0IHggPSAwOyB4IDwgMTA7IHgrKylcbiAgICAgICAgICAgICAgICBzW3kgKyB4XSBePSB+QlsoeCArIDIpICUgMTBdICYgQlsoeCArIDQpICUgMTBdO1xuICAgICAgICB9XG4gICAgICAgIC8vIElvdGEgKM65KVxuICAgICAgICBzWzBdIF49IFNIQTNfSU9UQV9IW3JvdW5kXTtcbiAgICAgICAgc1sxXSBePSBTSEEzX0lPVEFfTFtyb3VuZF07XG4gICAgfVxuICAgIEIuZmlsbCgwKTtcbn1cbmV4cG9ydCBjbGFzcyBLZWNjYWsgZXh0ZW5kcyBIYXNoIHtcbiAgICAvLyBOT1RFOiB3ZSBhY2NlcHQgYXJndW1lbnRzIGluIGJ5dGVzIGluc3RlYWQgb2YgYml0cyBoZXJlLlxuICAgIGNvbnN0cnVjdG9yKGJsb2NrTGVuLCBzdWZmaXgsIG91dHB1dExlbiwgZW5hYmxlWE9GID0gZmFsc2UsIHJvdW5kcyA9IDI0KSB7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIHRoaXMuYmxvY2tMZW4gPSBibG9ja0xlbjtcbiAgICAgICAgdGhpcy5zdWZmaXggPSBzdWZmaXg7XG4gICAgICAgIHRoaXMub3V0cHV0TGVuID0gb3V0cHV0TGVuO1xuICAgICAgICB0aGlzLmVuYWJsZVhPRiA9IGVuYWJsZVhPRjtcbiAgICAgICAgdGhpcy5yb3VuZHMgPSByb3VuZHM7XG4gICAgICAgIHRoaXMucG9zID0gMDtcbiAgICAgICAgdGhpcy5wb3NPdXQgPSAwO1xuICAgICAgICB0aGlzLmZpbmlzaGVkID0gZmFsc2U7XG4gICAgICAgIHRoaXMuZGVzdHJveWVkID0gZmFsc2U7XG4gICAgICAgIC8vIENhbiBiZSBwYXNzZWQgZnJvbSB1c2VyIGFzIGRrTGVuXG4gICAgICAgIG51bWJlcihvdXRwdXRMZW4pO1xuICAgICAgICAvLyAxNjAwID0gNXg1IG1hdHJpeCBvZiA2NGJpdC4gIDE2MDAgYml0cyA9PT0gMjAwIGJ5dGVzXG4gICAgICAgIGlmICgwID49IHRoaXMuYmxvY2tMZW4gfHwgdGhpcy5ibG9ja0xlbiA+PSAyMDApXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NoYTMgc3VwcG9ydHMgb25seSBrZWNjYWstZjE2MDAgZnVuY3Rpb24nKTtcbiAgICAgICAgdGhpcy5zdGF0ZSA9IG5ldyBVaW50OEFycmF5KDIwMCk7XG4gICAgICAgIHRoaXMuc3RhdGUzMiA9IHUzMih0aGlzLnN0YXRlKTtcbiAgICB9XG4gICAga2VjY2FrKCkge1xuICAgICAgICBrZWNjYWtQKHRoaXMuc3RhdGUzMiwgdGhpcy5yb3VuZHMpO1xuICAgICAgICB0aGlzLnBvc091dCA9IDA7XG4gICAgICAgIHRoaXMucG9zID0gMDtcbiAgICB9XG4gICAgdXBkYXRlKGRhdGEpIHtcbiAgICAgICAgZXhpc3RzKHRoaXMpO1xuICAgICAgICBjb25zdCB7IGJsb2NrTGVuLCBzdGF0ZSB9ID0gdGhpcztcbiAgICAgICAgZGF0YSA9IHRvQnl0ZXMoZGF0YSk7XG4gICAgICAgIGNvbnN0IGxlbiA9IGRhdGEubGVuZ3RoO1xuICAgICAgICBmb3IgKGxldCBwb3MgPSAwOyBwb3MgPCBsZW47KSB7XG4gICAgICAgICAgICBjb25zdCB0YWtlID0gTWF0aC5taW4oYmxvY2tMZW4gLSB0aGlzLnBvcywgbGVuIC0gcG9zKTtcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGFrZTsgaSsrKVxuICAgICAgICAgICAgICAgIHN0YXRlW3RoaXMucG9zKytdIF49IGRhdGFbcG9zKytdO1xuICAgICAgICAgICAgaWYgKHRoaXMucG9zID09PSBibG9ja0xlbilcbiAgICAgICAgICAgICAgICB0aGlzLmtlY2NhaygpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBmaW5pc2goKSB7XG4gICAgICAgIGlmICh0aGlzLmZpbmlzaGVkKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB0aGlzLmZpbmlzaGVkID0gdHJ1ZTtcbiAgICAgICAgY29uc3QgeyBzdGF0ZSwgc3VmZml4LCBwb3MsIGJsb2NrTGVuIH0gPSB0aGlzO1xuICAgICAgICAvLyBEbyB0aGUgcGFkZGluZ1xuICAgICAgICBzdGF0ZVtwb3NdIF49IHN1ZmZpeDtcbiAgICAgICAgaWYgKChzdWZmaXggJiAweDgwKSAhPT0gMCAmJiBwb3MgPT09IGJsb2NrTGVuIC0gMSlcbiAgICAgICAgICAgIHRoaXMua2VjY2FrKCk7XG4gICAgICAgIHN0YXRlW2Jsb2NrTGVuIC0gMV0gXj0gMHg4MDtcbiAgICAgICAgdGhpcy5rZWNjYWsoKTtcbiAgICB9XG4gICAgd3JpdGVJbnRvKG91dCkge1xuICAgICAgICBleGlzdHModGhpcywgZmFsc2UpO1xuICAgICAgICBieXRlcyhvdXQpO1xuICAgICAgICB0aGlzLmZpbmlzaCgpO1xuICAgICAgICBjb25zdCBidWZmZXJPdXQgPSB0aGlzLnN0YXRlO1xuICAgICAgICBjb25zdCB7IGJsb2NrTGVuIH0gPSB0aGlzO1xuICAgICAgICBmb3IgKGxldCBwb3MgPSAwLCBsZW4gPSBvdXQubGVuZ3RoOyBwb3MgPCBsZW47KSB7XG4gICAgICAgICAgICBpZiAodGhpcy5wb3NPdXQgPj0gYmxvY2tMZW4pXG4gICAgICAgICAgICAgICAgdGhpcy5rZWNjYWsoKTtcbiAgICAgICAgICAgIGNvbnN0IHRha2UgPSBNYXRoLm1pbihibG9ja0xlbiAtIHRoaXMucG9zT3V0LCBsZW4gLSBwb3MpO1xuICAgICAgICAgICAgb3V0LnNldChidWZmZXJPdXQuc3ViYXJyYXkodGhpcy5wb3NPdXQsIHRoaXMucG9zT3V0ICsgdGFrZSksIHBvcyk7XG4gICAgICAgICAgICB0aGlzLnBvc091dCArPSB0YWtlO1xuICAgICAgICAgICAgcG9zICs9IHRha2U7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG91dDtcbiAgICB9XG4gICAgeG9mSW50byhvdXQpIHtcbiAgICAgICAgLy8gU2hhMy9LZWNjYWsgdXNhZ2Ugd2l0aCBYT0YgaXMgcHJvYmFibHkgbWlzdGFrZSwgb25seSBTSEFLRSBpbnN0YW5jZXMgY2FuIGRvIFhPRlxuICAgICAgICBpZiAoIXRoaXMuZW5hYmxlWE9GKVxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdYT0YgaXMgbm90IHBvc3NpYmxlIGZvciB0aGlzIGluc3RhbmNlJyk7XG4gICAgICAgIHJldHVybiB0aGlzLndyaXRlSW50byhvdXQpO1xuICAgIH1cbiAgICB4b2YoYnl0ZXMpIHtcbiAgICAgICAgbnVtYmVyKGJ5dGVzKTtcbiAgICAgICAgcmV0dXJuIHRoaXMueG9mSW50byhuZXcgVWludDhBcnJheShieXRlcykpO1xuICAgIH1cbiAgICBkaWdlc3RJbnRvKG91dCkge1xuICAgICAgICBvdXRwdXQob3V0LCB0aGlzKTtcbiAgICAgICAgaWYgKHRoaXMuZmluaXNoZWQpXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2RpZ2VzdCgpIHdhcyBhbHJlYWR5IGNhbGxlZCcpO1xuICAgICAgICB0aGlzLndyaXRlSW50byhvdXQpO1xuICAgICAgICB0aGlzLmRlc3Ryb3koKTtcbiAgICAgICAgcmV0dXJuIG91dDtcbiAgICB9XG4gICAgZGlnZXN0KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5kaWdlc3RJbnRvKG5ldyBVaW50OEFycmF5KHRoaXMub3V0cHV0TGVuKSk7XG4gICAgfVxuICAgIGRlc3Ryb3koKSB7XG4gICAgICAgIHRoaXMuZGVzdHJveWVkID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5zdGF0ZS5maWxsKDApO1xuICAgIH1cbiAgICBfY2xvbmVJbnRvKHRvKSB7XG4gICAgICAgIGNvbnN0IHsgYmxvY2tMZW4sIHN1ZmZpeCwgb3V0cHV0TGVuLCByb3VuZHMsIGVuYWJsZVhPRiB9ID0gdGhpcztcbiAgICAgICAgdG8gfHwgKHRvID0gbmV3IEtlY2NhayhibG9ja0xlbiwgc3VmZml4LCBvdXRwdXRMZW4sIGVuYWJsZVhPRiwgcm91bmRzKSk7XG4gICAgICAgIHRvLnN0YXRlMzIuc2V0KHRoaXMuc3RhdGUzMik7XG4gICAgICAgIHRvLnBvcyA9IHRoaXMucG9zO1xuICAgICAgICB0by5wb3NPdXQgPSB0aGlzLnBvc091dDtcbiAgICAgICAgdG8uZmluaXNoZWQgPSB0aGlzLmZpbmlzaGVkO1xuICAgICAgICB0by5yb3VuZHMgPSByb3VuZHM7XG4gICAgICAgIC8vIFN1ZmZpeCBjYW4gY2hhbmdlIGluIGNTSEFLRVxuICAgICAgICB0by5zdWZmaXggPSBzdWZmaXg7XG4gICAgICAgIHRvLm91dHB1dExlbiA9IG91dHB1dExlbjtcbiAgICAgICAgdG8uZW5hYmxlWE9GID0gZW5hYmxlWE9GO1xuICAgICAgICB0by5kZXN0cm95ZWQgPSB0aGlzLmRlc3Ryb3llZDtcbiAgICAgICAgcmV0dXJuIHRvO1xuICAgIH1cbn1cbmNvbnN0IGdlbiA9IChzdWZmaXgsIGJsb2NrTGVuLCBvdXRwdXRMZW4pID0+IHdyYXBDb25zdHJ1Y3RvcigoKSA9PiBuZXcgS2VjY2FrKGJsb2NrTGVuLCBzdWZmaXgsIG91dHB1dExlbikpO1xuZXhwb3J0IGNvbnN0IHNoYTNfMjI0ID0gLyogQF9fUFVSRV9fICovIGdlbigweDA2LCAxNDQsIDIyNCAvIDgpO1xuLyoqXG4gKiBTSEEzLTI1NiBoYXNoIGZ1bmN0aW9uXG4gKiBAcGFyYW0gbWVzc2FnZSAtIHRoYXQgd291bGQgYmUgaGFzaGVkXG4gKi9cbmV4cG9ydCBjb25zdCBzaGEzXzI1NiA9IC8qIEBfX1BVUkVfXyAqLyBnZW4oMHgwNiwgMTM2LCAyNTYgLyA4KTtcbmV4cG9ydCBjb25zdCBzaGEzXzM4NCA9IC8qIEBfX1BVUkVfXyAqLyBnZW4oMHgwNiwgMTA0LCAzODQgLyA4KTtcbmV4cG9ydCBjb25zdCBzaGEzXzUxMiA9IC8qIEBfX1BVUkVfXyAqLyBnZW4oMHgwNiwgNzIsIDUxMiAvIDgpO1xuZXhwb3J0IGNvbnN0IGtlY2Nha18yMjQgPSAvKiBAX19QVVJFX18gKi8gZ2VuKDB4MDEsIDE0NCwgMjI0IC8gOCk7XG4vKipcbiAqIGtlY2Nhay0yNTYgaGFzaCBmdW5jdGlvbi4gRGlmZmVyZW50IGZyb20gU0hBMy0yNTYuXG4gKiBAcGFyYW0gbWVzc2FnZSAtIHRoYXQgd291bGQgYmUgaGFzaGVkXG4gKi9cbmV4cG9ydCBjb25zdCBrZWNjYWtfMjU2ID0gLyogQF9fUFVSRV9fICovIGdlbigweDAxLCAxMzYsIDI1NiAvIDgpO1xuZXhwb3J0IGNvbnN0IGtlY2Nha18zODQgPSAvKiBAX19QVVJFX18gKi8gZ2VuKDB4MDEsIDEwNCwgMzg0IC8gOCk7XG5leHBvcnQgY29uc3Qga2VjY2FrXzUxMiA9IC8qIEBfX1BVUkVfXyAqLyBnZW4oMHgwMSwgNzIsIDUxMiAvIDgpO1xuY29uc3QgZ2VuU2hha2UgPSAoc3VmZml4LCBibG9ja0xlbiwgb3V0cHV0TGVuKSA9PiB3cmFwWE9GQ29uc3RydWN0b3JXaXRoT3B0cygob3B0cyA9IHt9KSA9PiBuZXcgS2VjY2FrKGJsb2NrTGVuLCBzdWZmaXgsIG9wdHMuZGtMZW4gPT09IHVuZGVmaW5lZCA/IG91dHB1dExlbiA6IG9wdHMuZGtMZW4sIHRydWUpKTtcbmV4cG9ydCBjb25zdCBzaGFrZTEyOCA9IC8qIEBfX1BVUkVfXyAqLyBnZW5TaGFrZSgweDFmLCAxNjgsIDEyOCAvIDgpO1xuZXhwb3J0IGNvbnN0IHNoYWtlMjU2ID0gLyogQF9fUFVSRV9fICovIGdlblNoYWtlKDB4MWYsIDEzNiwgMjU2IC8gOCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zaGEzLmpzLm1hcCJdLCJuYW1lcyI6WyJieXRlcyIsImV4aXN0cyIsIm51bWJlciIsIm91dHB1dCIsInJvdGxCSCIsInJvdGxCTCIsInJvdGxTSCIsInJvdGxTTCIsInNwbGl0IiwiSGFzaCIsInUzMiIsInRvQnl0ZXMiLCJ3cmFwQ29uc3RydWN0b3IiLCJ3cmFwWE9GQ29uc3RydWN0b3JXaXRoT3B0cyIsIlNIQTNfUEkiLCJTSEEzX1JPVEwiLCJfU0hBM19JT1RBIiwiXzBuIiwiQmlnSW50IiwiXzFuIiwiXzJuIiwiXzduIiwiXzI1Nm4iLCJfMHg3MW4iLCJyb3VuZCIsIlIiLCJ4IiwieSIsInB1c2giLCJ0IiwiaiIsIlNIQTNfSU9UQV9IIiwiU0hBM19JT1RBX0wiLCJyb3RsSCIsImgiLCJsIiwicyIsInJvdGxMIiwia2VjY2FrUCIsInJvdW5kcyIsIkIiLCJVaW50MzJBcnJheSIsImlkeDEiLCJpZHgwIiwiQjAiLCJCMSIsIlRoIiwiVGwiLCJjdXJIIiwiY3VyTCIsInNoaWZ0IiwiUEkiLCJmaWxsIiwiS2VjY2FrIiwiY29uc3RydWN0b3IiLCJibG9ja0xlbiIsInN1ZmZpeCIsIm91dHB1dExlbiIsImVuYWJsZVhPRiIsInBvcyIsInBvc091dCIsImZpbmlzaGVkIiwiZGVzdHJveWVkIiwiRXJyb3IiLCJzdGF0ZSIsIlVpbnQ4QXJyYXkiLCJzdGF0ZTMyIiwia2VjY2FrIiwidXBkYXRlIiwiZGF0YSIsImxlbiIsImxlbmd0aCIsInRha2UiLCJNYXRoIiwibWluIiwiaSIsImZpbmlzaCIsIndyaXRlSW50byIsIm91dCIsImJ1ZmZlck91dCIsInNldCIsInN1YmFycmF5IiwieG9mSW50byIsInhvZiIsImRpZ2VzdEludG8iLCJkZXN0cm95IiwiZGlnZXN0IiwiX2Nsb25lSW50byIsInRvIiwiZ2VuIiwic2hhM18yMjQiLCJzaGEzXzI1NiIsInNoYTNfMzg0Iiwic2hhM181MTIiLCJrZWNjYWtfMjI0Iiwia2VjY2FrXzI1NiIsImtlY2Nha18zODQiLCJrZWNjYWtfNTEyIiwiZ2VuU2hha2UiLCJvcHRzIiwiZGtMZW4iLCJ1bmRlZmluZWQiLCJzaGFrZTEyOCIsInNoYWtlMjU2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/sha3.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/esm/utils.js":
/*!*************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hash: () => (/* binding */ Hash),\n/* harmony export */   asyncLoop: () => (/* binding */ asyncLoop),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   checkOpts: () => (/* binding */ checkOpts),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createView: () => (/* binding */ createView),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   isLE: () => (/* binding */ isLE),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   randomBytes: () => (/* binding */ randomBytes),\n/* harmony export */   rotr: () => (/* binding */ rotr),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   u32: () => (/* binding */ u32),\n/* harmony export */   u8: () => (/* binding */ u8),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   wrapConstructor: () => (/* binding */ wrapConstructor),\n/* harmony export */   wrapConstructorWithOpts: () => (/* binding */ wrapConstructorWithOpts),\n/* harmony export */   wrapXOFConstructorWithOpts: () => (/* binding */ wrapXOFConstructorWithOpts)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/crypto */ \"(ssr)/./node_modules/@noble/hashes/esm/cryptoNode.js\");\n/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */ // We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\n\nconst u8a = (a)=>a instanceof Uint8Array;\n// Cast array to different type\nconst u8 = (arr)=>new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nconst u32 = (arr)=>new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nconst createView = (arr)=>new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nconst rotr = (word, shift)=>word << 32 - shift | word >>> shift;\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\nconst isLE = new Uint8Array(new Uint32Array([\n    0x11223344\n]).buffer)[0] === 0x44;\nif (!isLE) throw new Error(\"Non little-endian hardware is not supported\");\nconst hexes = /* @__PURE__ */ Array.from({\n    length: 256\n}, (_, i)=>i.toString(16).padStart(2, \"0\"));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */ function bytesToHex(bytes) {\n    if (!u8a(bytes)) throw new Error(\"Uint8Array expected\");\n    // pre-caching improves the speed 6x\n    let hex = \"\";\n    for(let i = 0; i < bytes.length; i++){\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */ function hexToBytes(hex) {\n    if (typeof hex !== \"string\") throw new Error(\"hex string expected, got \" + typeof hex);\n    const len = hex.length;\n    if (len % 2) throw new Error(\"padded hex string expected, got unpadded hex of length \" + len);\n    const array = new Uint8Array(len / 2);\n    for(let i = 0; i < array.length; i++){\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0) throw new Error(\"Invalid byte sequence\");\n        array[i] = byte;\n    }\n    return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nconst nextTick = async ()=>{};\n// Returns control to thread each 'tick' ms to avoid blocking\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for(let i = 0; i < iters; i++){\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick) continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */ function utf8ToBytes(str) {\n    if (typeof str !== \"string\") throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */ function toBytes(data) {\n    if (typeof data === \"string\") data = utf8ToBytes(data);\n    if (!u8a(data)) throw new Error(`expected Uint8Array, got ${typeof data}`);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */ function concatBytes(...arrays) {\n    const r = new Uint8Array(arrays.reduce((sum, a)=>sum + a.length, 0));\n    let pad = 0; // walk through each item, ensure they have proper type\n    arrays.forEach((a)=>{\n        if (!u8a(a)) throw new Error(\"Uint8Array expected\");\n        r.set(a, pad);\n        pad += a.length;\n    });\n    return r;\n}\n// For runtime check if class implements interface\nclass Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nconst toStr = {}.toString;\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && toStr.call(opts) !== \"[object Object]\") throw new Error(\"Options should be object or undefined\");\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\nfunction wrapConstructor(hashCons) {\n    const hashC = (msg)=>hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = ()=>hashCons();\n    return hashC;\n}\nfunction wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts)=>hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts)=>hashCons(opts);\n    return hashC;\n}\nfunction wrapXOFConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts)=>hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts)=>hashCons(opts);\n    return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */ function randomBytes(bytesLength = 32) {\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues === \"function\") {\n        return _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    throw new Error(\"crypto.getRandomValues must be defined\");\n} //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/esm/utils.js\n");

/***/ })

};
;