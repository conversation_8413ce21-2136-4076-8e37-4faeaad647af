{"version": 3, "sources": ["../../../../src/experimental/testmode/proxy/server.ts"], "names": ["createProxyServer", "readBody", "req", "acc", "chunk", "push", "<PERSON><PERSON><PERSON>", "concat", "onFetch", "server", "http", "createServer", "res", "url", "writeHead", "end", "json", "JSON", "parse", "toString", "e", "api", "response", "handleFetch", "UNHANDLED", "write", "stringify", "Promise", "resolve", "listen", "undefined", "address", "close", "Error", "port", "fetchWith", "input", "init", "testData", "request", "Request", "headers", "set", "String", "fetch"], "mappings": ";;;;+BAiBsBA;;;eAAAA;;;6DAjBL;uBAGS;0BAEE;;;;;;AAE5B,eAAeC,SAASC,GAAoB;IAC1C,MAAMC,MAAgB,EAAE;IAExB,WAAW,MAAMC,SAASF,IAAK;QAC7BC,IAAIE,IAAI,CAACD;IACX;IAEA,OAAOE,OAAOC,MAAM,CAACJ;AACvB;AAEO,eAAeH,kBAAkB,EACtCQ,OAAO,EAGR;IACC,MAAMC,SAASC,aAAI,CAACC,YAAY,CAAC,OAAOT,KAAKU;QAC3C,IAAIV,IAAIW,GAAG,KAAK,KAAK;YACnBD,IAAIE,SAAS,CAAC;YACdF,IAAIG,GAAG;YACP;QACF;QAEA,IAAIC;QACJ,IAAI;YACFA,OAAOC,KAAKC,KAAK,CAAC,AAAC,CAAA,MAAMjB,SAASC,IAAG,EAAGiB,QAAQ,CAAC;QACnD,EAAE,OAAOC,GAAG;YACVR,IAAIE,SAAS,CAAC;YACdF,IAAIG,GAAG;YACP;QACF;QAEA,MAAM,EAAEM,GAAG,EAAE,GAAGL;QAEhB,IAAIM;QACJ,OAAQD;YACN,KAAK;gBACH,IAAIb,SAAS;oBACXc,WAAW,MAAMC,IAAAA,qBAAW,EAACP,MAAMR;gBACrC;gBACA;YACF;gBACE;QACJ;QACA,IAAI,CAACc,UAAU;YACbA,WAAWE,gBAAS;QACtB;QAEAZ,IAAIE,SAAS,CAAC,KAAK;YAAE,gBAAgB;QAAmB;QACxDF,IAAIa,KAAK,CAACR,KAAKS,SAAS,CAACJ;QACzBV,IAAIG,GAAG;IACT;IAEA,MAAM,IAAIY,QAAQ,CAACC;QACjBnB,OAAOoB,MAAM,CAAC,GAAG,aAAa;YAC5BD,QAAQE;QACV;IACF;IAEA,MAAMC,UAAUtB,OAAOsB,OAAO;IAC9B,IAAI,CAACA,WAAW,OAAOA,YAAY,UAAU;QAC3CtB,OAAOuB,KAAK;QACZ,MAAM,IAAIC,MAAM;IAClB;IACA,MAAMC,OAAOH,QAAQG,IAAI;IAEzB,MAAMC,YAAsC,CAACC,OAAOC,MAAMC;QACxD,MAAMC,UAAU,IAAIC,QAAQJ,OAAOC;QACnCE,QAAQE,OAAO,CAACC,GAAG,CAAC,wBAAwBC,OAAOT;QACnDK,QAAQE,OAAO,CAACC,GAAG,CAAC,kBAAkBJ,YAAY;QAClD,OAAOM,MAAML;IACf;IAEA,OAAO;QAAEL;QAAMF,OAAO,IAAMvB,OAAOuB,KAAK;QAAIG;IAAU;AACxD"}