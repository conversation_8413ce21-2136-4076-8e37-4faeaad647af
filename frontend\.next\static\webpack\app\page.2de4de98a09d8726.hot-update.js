"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Contract ABIs - Complete for all DeFi operations\nconst TokenA_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) external returns (bool)\",\n    \"function allowance(address owner, address spender) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) external returns (bool)\",\n    \"function transferFrom(address from, address to, uint256 amount) external returns (bool)\"\n];\nconst TokenB_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\"\n];\nconst TokenSwap_ABI = [\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getTokenBLiquidity() external view returns (uint256)\",\n    \"function getTokenABalance() external view returns (uint256)\",\n    \"function tokenA() external view returns (address)\",\n    \"function tokenB() external view returns (address)\",\n    \"event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut)\"\n];\nconst LiquidityPool_ABI = [\n    \"function addLiquidity(uint256 amountA, uint256 amountB) external returns (uint256)\",\n    \"function removeLiquidity(uint256 liquidity) external returns (uint256, uint256)\",\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getReserves() external view returns (uint256, uint256)\",\n    \"function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256)\",\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function name() external view returns (string)\"\n];\nconst Staking_ABI = [\n    \"function stake(uint256 amount) external\",\n    \"function withdraw(uint256 amount) external\",\n    \"function claimReward() external\",\n    \"function exit() external\",\n    \"function compound() external\",\n    \"function multiStake(uint256[] amounts) external\",\n    \"function multiWithdraw(uint256[] amounts) external\",\n    \"function multiClaim(uint256 times) external\",\n    \"function earned(address account) external view returns (uint256)\",\n    \"function balances(address account) external view returns (uint256)\",\n    \"function getStakingInfo(address user) external view returns (uint256, uint256, uint256, uint256)\"\n];\n// Nexus network configuration\nconst NEXUS_NETWORK = {\n    chainId: \"0xF64\",\n    chainName: \"Nexus Testnet III\",\n    nativeCurrency: {\n        name: \"NEX\",\n        symbol: \"NEX\",\n        decimals: 18\n    },\n    rpcUrls: [\n        \"https://testnet3.rpc.nexus.xyz\"\n    ],\n    blockExplorerUrls: [\n        \"https://explorer.nexus.xyz\"\n    ]\n};\n// Default contract addresses (will be replaced with deployed addresses)\nconst DEFAULT_ADDRESSES = {\n    TokenA: \"0x0000000000000000000000000000000000000000\",\n    TokenB: \"0x0000000000000000000000000000000000000000\",\n    TokenSwap: \"0x0000000000000000000000000000000000000000\"\n};\nfunction Home() {\n    var _contractAddresses_deployer;\n    _s();\n    // Basic states\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenABalance, setTokenABalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [tokenBBalance, setTokenBBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [lpTokenBalance, setLpTokenBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [stakedBalance, setStakedBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [earnedRewards, setEarnedRewards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_ADDRESSES);\n    const [liquidity, setLiquidity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [isCorrectNetwork, setIsCorrectNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab and form states\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"swap\");\n    const [swapAmount, setSwapAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountA, setLiquidityAmountA] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountB, setLiquidityAmountB] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [stakeAmount, setStakeAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Token selection states\n    const [selectedTokenIn, setSelectedTokenIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenA\");\n    const [selectedTokenOut, setSelectedTokenOut] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenB\");\n    const [availableTokens, setAvailableTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkIfWalletIsConnected();\n        loadContractAddresses();\n        setupEventListeners();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (account && isCorrectNetwork) {\n            updateBalances();\n            updateLiquidity();\n            loadAvailableTokens();\n        }\n    }, [\n        account,\n        contractAddresses,\n        isCorrectNetwork\n    ]);\n    async function loadAvailableTokens() {\n        if (!account || !isCorrectNetwork) return;\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const tokens = [\n                {\n                    symbol: \"TKNA\",\n                    name: \"Token A\",\n                    address: contractAddresses.TokenA,\n                    balance: tokenABalance,\n                    decimals: 18\n                },\n                {\n                    symbol: \"TKNB\",\n                    name: \"Token B\",\n                    address: contractAddresses.TokenB,\n                    balance: tokenBBalance,\n                    decimals: 18\n                }\n            ];\n            // Add LP token if available\n            if (contractAddresses.LiquidityPool) {\n                tokens.push({\n                    symbol: \"NLP\",\n                    name: \"Nexus LP Token\",\n                    address: contractAddresses.LiquidityPool,\n                    balance: lpTokenBalance,\n                    decimals: 18\n                });\n            }\n            setAvailableTokens(tokens);\n        } catch (error) {\n            console.error(\"Error loading available tokens:\", error);\n        }\n    }\n    function setupEventListeners() {\n        const { ethereum } = window;\n        if (!ethereum) return;\n        // Listen for account changes\n        ethereum.on(\"accountsChanged\", (accounts)=>{\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                checkNetwork();\n            } else {\n                setAccount(\"\");\n                setIsCorrectNetwork(false);\n            }\n        });\n        // Listen for network changes\n        ethereum.on(\"chainChanged\", (chainId)=>{\n            console.log(\"Network changed to:\", chainId);\n            checkNetwork();\n            // Reload page to reset state\n            window.location.reload();\n        });\n        // Cleanup function\n        return ()=>{\n            if (ethereum.removeListener) {\n                ethereum.removeListener(\"accountsChanged\", ()=>{});\n                ethereum.removeListener(\"chainChanged\", ()=>{});\n            }\n        };\n    }\n    async function loadContractAddresses() {\n        try {\n            // Try to load deployed addresses from public folder\n            const response = await fetch(\"/deployedAddresses.json\");\n            if (response.ok) {\n                const addresses = await response.json();\n                setContractAddresses(addresses);\n                console.log(\"Loaded contract addresses:\", addresses);\n            } else {\n                console.warn(\"Could not load deployed addresses, using defaults\");\n            }\n        } catch (error) {\n            console.warn(\"Could not load deployed addresses:\", error);\n        }\n    }\n    async function checkIfWalletIsConnected() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_accounts\"\n            });\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                await checkNetwork();\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setError(\"Error saat mengecek koneksi wallet\");\n        }\n    }\n    async function checkNetwork() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setIsCorrectNetwork(false);\n                return;\n            }\n            const chainId = await ethereum.request({\n                method: \"eth_chainId\"\n            });\n            console.log(\"Current chainId:\", chainId, \"Expected:\", NEXUS_NETWORK.chainId);\n            // Convert both to same format for comparison\n            const currentChainId = parseInt(chainId, 16);\n            const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);\n            if (currentChainId === expectedChainId) {\n                setIsCorrectNetwork(true);\n                setError(\"\");\n                console.log(\"✅ Connected to correct network\");\n            } else {\n                setIsCorrectNetwork(false);\n                setError(\"Wrong network. Current: \".concat(currentChainId, \", Expected: \").concat(expectedChainId, \" (Nexus Testnet III)\"));\n                console.log(\"❌ Wrong network detected\");\n            }\n        } catch (error) {\n            console.error(\"Error checking network:\", error);\n            setIsCorrectNetwork(false);\n            setError(\"Error checking network\");\n        }\n    }\n    async function switchToNexusNetwork() {\n        try {\n            const { ethereum } = window;\n            setError(\"\");\n            setSuccess(\"Switching to Nexus network...\");\n            try {\n                await ethereum.request({\n                    method: \"wallet_switchEthereumChain\",\n                    params: [\n                        {\n                            chainId: NEXUS_NETWORK.chainId\n                        }\n                    ]\n                });\n                console.log(\"✅ Network switch requested\");\n            } catch (switchError) {\n                console.log(\"Switch error code:\", switchError.code);\n                // Network belum ditambahkan, tambahkan dulu\n                if (switchError.code === 4902) {\n                    console.log(\"Adding Nexus network...\");\n                    await ethereum.request({\n                        method: \"wallet_addEthereumChain\",\n                        params: [\n                            NEXUS_NETWORK\n                        ]\n                    });\n                    console.log(\"✅ Network added\");\n                } else {\n                    throw switchError;\n                }\n            }\n            // Wait a bit for network to switch\n            setTimeout(async ()=>{\n                await checkNetwork();\n                setSuccess(\"\");\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error switching network:\", error);\n            setError(\"Gagal switch ke Nexus network: \" + (error.message || \"Unknown error\"));\n            setSuccess(\"\");\n        }\n    }\n    async function connectWallet() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            setAccount(accounts[0]);\n            await checkNetwork();\n            setError(\"\");\n            setSuccess(\"Wallet berhasil terhubung!\");\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n            setError(\"Gagal menghubungkan wallet\");\n        }\n    }\n    async function updateBalances() {\n        if (!account || !isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {\n            console.log(\"Contract addresses not loaded yet, skipping balance update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating all balances for:\", account);\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, provider);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, provider);\n            // Get basic token balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            setTokenABalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceA));\n            setTokenBBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceB));\n            // Get LP token balance if LiquidityPool exists\n            if (contractAddresses.LiquidityPool) {\n                const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, provider);\n                const lpBalance = await lpContract.balanceOf(account);\n                setLpTokenBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(lpBalance));\n            }\n            // Get staking info if Staking exists\n            if (contractAddresses.Staking) {\n                const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, provider);\n                const [stakedBal, earned] = await Promise.all([\n                    stakingContract.balances(account),\n                    stakingContract.earned(account)\n                ]);\n                setStakedBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(stakedBal));\n                setEarnedRewards(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(earned));\n            }\n            console.log(\"All balances updated successfully\");\n        } catch (error) {\n            console.error(\"Error updating balances:\", error);\n            setError(\"Error connecting to contracts\");\n        }\n    }\n    async function updateLiquidity() {\n        if (!isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {\n            console.log(\"TokenSwap address not loaded yet, skipping liquidity update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating liquidity for TokenSwap:\", contractAddresses.TokenSwap);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);\n            try {\n                const liquidityAmount = await swapContract.getTokenBLiquidity();\n                console.log(\"Raw liquidity:\", liquidityAmount.toString());\n                setLiquidity(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(liquidityAmount));\n                console.log(\"Liquidity updated successfully\");\n            } catch (contractError) {\n                console.error(\"TokenSwap contract call error:\", contractError);\n                setError(\"Error reading liquidity. TokenSwap contract may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating liquidity:\", error);\n            setError(\"Error connecting to TokenSwap contract\");\n        }\n    }\n    async function handleSwap() {\n        if (!swapAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(swapAmount);\n            // Check balance\n            const balance = await tokenAContract.balanceOf(account);\n            if (balance < amount) {\n                setError(\"Saldo Token A tidak mencukupi\");\n                return;\n            }\n            // Check allowance\n            const allowance = await tokenAContract.allowance(account, contractAddresses.TokenSwap);\n            if (allowance < amount) {\n                setSuccess(\"Menyetujui penggunaan Token A...\");\n                const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, amount);\n                await approveTx.wait();\n                setSuccess(\"Approval berhasil! Melakukan swap...\");\n            } else {\n                setSuccess(\"Melakukan swap...\");\n            }\n            // Perform swap (updated for new contract)\n            const swapTx = await swapContract.swap(contractAddresses.TokenA, amount, 0);\n            await swapTx.wait();\n            setSuccess(\"Swap berhasil! \\uD83C\\uDF89\");\n            setSwapAmount(\"\");\n            // Update balances\n            await updateBalances();\n            await updateLiquidity();\n        } catch (error) {\n            console.error(\"Error during swap:\", error);\n            if (error.code === \"ACTION_REJECTED\") {\n                setError(\"Transaksi dibatalkan oleh user\");\n            } else if (error.message.includes(\"insufficient funds\")) {\n                setError(\"Saldo NEX tidak mencukupi untuk gas fee\");\n            } else {\n                setError(\"Swap gagal: \" + (error.reason || error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleAddLiquidity() {\n        if (!liquidityAmountA || !liquidityAmountB || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const amountA = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountA);\n            const amountB = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountB);\n            // Check balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            if (balanceA < amountA) {\n                setError(\"Saldo Token A tidak mencukupi\");\n                return;\n            }\n            if (balanceB < amountB) {\n                setError(\"Saldo Token B tidak mencukupi\");\n                return;\n            }\n            // Approve tokens\n            setSuccess(\"Menyetujui Token A...\");\n            const approveATx = await tokenAContract.approve(contractAddresses.LiquidityPool, amountA);\n            await approveATx.wait();\n            setSuccess(\"Menyetujui Token B...\");\n            const approveBTx = await tokenBContract.approve(contractAddresses.LiquidityPool, amountB);\n            await approveBTx.wait();\n            // Add liquidity\n            setSuccess(\"Menambahkan likuiditas...\");\n            const addLiquidityTx = await lpContract.addLiquidity(amountA, amountB);\n            await addLiquidityTx.wait();\n            setSuccess(\"Likuiditas berhasil ditambahkan! \\uD83C\\uDF89\");\n            setLiquidityAmountA(\"\");\n            setLiquidityAmountB(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error adding liquidity:\", error);\n            setError(\"Gagal menambahkan likuiditas: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleStake() {\n        if (!stakeAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(stakeAmount);\n            // Check LP balance\n            const lpBalance = await lpContract.balanceOf(account);\n            if (lpBalance < amount) {\n                setError(\"Saldo LP Token tidak mencukupi\");\n                return;\n            }\n            // Approve LP tokens\n            setSuccess(\"Menyetujui LP Tokens...\");\n            const approveTx = await lpContract.approve(contractAddresses.Staking, amount);\n            await approveTx.wait();\n            // Stake\n            setSuccess(\"Melakukan stake...\");\n            const stakeTx = await stakingContract.stake(amount);\n            await stakeTx.wait();\n            setSuccess(\"Stake berhasil! \\uD83C\\uDF89\");\n            setStakeAmount(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error staking:\", error);\n            setError(\"Gagal melakukan stake: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleClaim() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            setSuccess(\"Mengklaim rewards...\");\n            const claimTx = await stakingContract.claimReward();\n            await claimTx.wait();\n            setSuccess(\"Rewards berhasil diklaim! \\uD83C\\uDF89\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error claiming:\", error);\n            setError(\"Gagal mengklaim rewards: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const clearMessages = ()=>{\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const isDeployerAccount = account && contractAddresses.deployer && account.toLowerCase() === contractAddresses.deployer.toLowerCase();\n    async function requestTestTokens() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"Requesting test tokens...\");\n        try {\n            // Check if user is the deployer\n            if (isDeployerAccount) {\n                setSuccess(\"You are the deployer! You already have all tokens \\uD83C\\uDF89\");\n                await updateBalances();\n                return;\n            }\n            // For non-deployer users, show instructions\n            setError(\"\");\n            setSuccess(\"\");\n            alert(\"To get test tokens:\\n\\n1. Switch to deployer account: \".concat(contractAddresses.deployer, \"\\n2. Or ask the deployer to send you tokens\\n3. Or use a faucet if available\\n\\nYour current address: \").concat(account, \"\\nDeployer address: \").concat(contractAddresses.deployer, \"\\n\\nThe deployer has 1,000,000 Token A available for distribution.\"));\n        } catch (error) {\n            console.error(\"Error requesting test tokens:\", error);\n            setError(\"Failed to get test tokens: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Batch Operations untuk Maximum Transactions\n    async function handleBatchSwaps() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            // Multiple small swaps untuk generate banyak transaksi\n            const amounts = [\n                \"10\",\n                \"20\",\n                \"30\",\n                \"40\",\n                \"50\"\n            ]; // 5 transaksi swap\n            let totalAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"0\");\n            for (const amount of amounts){\n                totalAmount += ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amount);\n            }\n            // Check balance\n            const balance = await tokenAContract.balanceOf(account);\n            if (balance < totalAmount) {\n                setError(\"Saldo Token A tidak mencukupi untuk batch swaps\");\n                return;\n            }\n            // Approve total amount\n            setSuccess(\"Menyetujui total amount untuk batch swaps...\");\n            const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, totalAmount);\n            await approveTx.wait();\n            // Execute multiple swaps\n            for(let i = 0; i < amounts.length; i++){\n                setSuccess(\"Melakukan swap \".concat(i + 1, \"/\").concat(amounts.length, \"...\"));\n                const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amounts[i]);\n                const swapTx = await swapContract.swap(contractAddresses.TokenA, amount, 0);\n                await swapTx.wait();\n                // Small delay between transactions\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            setSuccess(\"Batch swaps berhasil! \".concat(amounts.length, \" transaksi completed \\uD83C\\uDF89\"));\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error during batch swaps:\", error);\n            setError(\"Batch swaps gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleTransactionSpam() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            // Generate many approval transactions\n            const spamAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"1\");\n            const contracts = [\n                contractAddresses.TokenSwap,\n                contractAddresses.LiquidityPool,\n                contractAddresses.Staking\n            ];\n            let transactionCount = 0;\n            for (const contractAddr of contracts){\n                // Multiple approvals for each contract\n                for(let i = 0; i < 3; i++){\n                    setSuccess(\"Generating approval transaction \".concat(transactionCount + 1, \"...\"));\n                    const approveTx = await tokenAContract.approve(contractAddr, spamAmount);\n                    await approveTx.wait();\n                    transactionCount++;\n                    // Also approve TokenB\n                    const approveBTx = await tokenBContract.approve(contractAddr, spamAmount);\n                    await approveBTx.wait();\n                    transactionCount++;\n                    // Small delay\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                }\n            }\n            setSuccess(\"Transaction spam completed! \".concat(transactionCount, \" transactions generated \\uD83D\\uDE80\"));\n        } catch (error) {\n            console.error(\"Error during transaction spam:\", error);\n            setError(\"Transaction spam gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-6 right-6 flex flex-col items-end space-y-2\",\n                children: [\n                    account && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Connected:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 787,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            formatAddress(account)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full \".concat(isCorrectNetwork ? \"bg-green-500\" : \"bg-red-500\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 791,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: isCorrectNetwork ? \"Nexus Testnet III\" : \"Wrong Network\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 790,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 max-w-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Expected Chain: 3940 (0xF64)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 798,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Contracts: \",\n                                    contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? \"✅\" : \"❌\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 799,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Deployer: \",\n                                    contractAddresses.deployer ? contractAddresses.deployer.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 800,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Your Address: \",\n                                    account ? account.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 801,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 784,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-screen px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"Nexus Swap\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 808,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Decentralized token exchange on Nexus blockchain. Swap Token A for Token B at a fixed 1:1 ratio.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 811,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 807,\n                        columnNumber: 9\n                    }, this),\n                    (error || success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mb-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 821,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-red-500 hover:text-red-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 820,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-green-500 hover:text-green-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 830,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 828,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 818,\n                        columnNumber: 11\n                    }, this),\n                    !account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: connectWallet,\n                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 840,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Connect your MetaMask wallet to start swapping tokens\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 839,\n                        columnNumber: 11\n                    }, this) : !isCorrectNetwork ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: switchToNexusNetwork,\n                                        className: \"bg-orange-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                        children: \"Switch to Nexus Network\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: checkNetwork,\n                                            className: \"text-sm text-blue-600 hover:text-blue-800 underline\",\n                                            children: \"Already switched? Click to refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 853,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Please switch to Nexus Testnet III to continue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 852,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl shadow-xl p-6 border border-gray-200 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-center\",\n                                        children: \"Portfolio Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-blue-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-blue-600\",\n                                                        children: parseFloat(tokenABalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-purple-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-purple-600\",\n                                                        children: parseFloat(tokenBBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 890,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 888,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-green-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-green-600\",\n                                                        children: parseFloat(lpTokenBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"NLP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 895,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-orange-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Staked\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 903,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-orange-600\",\n                                                        children: parseFloat(stakedBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"NLP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 902,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(earnedRewards) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center p-3 bg-yellow-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Pending Rewards\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-mono text-xl font-bold text-yellow-600\",\n                                                children: [\n                                                    parseFloat(earnedRewards).toFixed(4),\n                                                    \" TKNB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 911,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 878,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl shadow-xl border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex border-b border-gray-200\",\n                                        children: [\n                                            {\n                                                id: \"swap\",\n                                                label: \"Swap\",\n                                                icon: \"\\uD83D\\uDD04\"\n                                            },\n                                            {\n                                                id: \"liquidity\",\n                                                label: \"Add Liquidity\",\n                                                icon: \"\\uD83D\\uDCA7\"\n                                            },\n                                            {\n                                                id: \"stake\",\n                                                label: \"Stake\",\n                                                icon: \"\\uD83C\\uDFE6\"\n                                            },\n                                            {\n                                                id: \"claim\",\n                                                label: \"Claim\",\n                                                icon: \"\\uD83C\\uDF81\"\n                                            },\n                                            {\n                                                id: \"batch\",\n                                                label: \"Batch Ops\",\n                                                icon: \"⚡\"\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"flex-1 px-4 py-3 text-sm font-medium transition-colors \".concat(activeTab === tab.id ? \"text-blue-600 border-b-2 border-blue-600 bg-blue-50\" : \"text-gray-500 hover:text-gray-700 hover:bg-gray-50\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: tab.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    tab.label\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Swap Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Available Liquidity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-mono text-lg font-semibold text-gray-800\",\n                                                                children: [\n                                                                    parseFloat(liquidity).toFixed(2),\n                                                                    \" Token B\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Swap Amount (Token A → Token B)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: swapAmount,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Input changed:\", e.target.value);\n                                                                            setSwapAmount(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-4 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400 shadow-sm\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 966,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                        children: \"TKNA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 980,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 965,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"Exchange rate: 1 TKNA ≈ 1 TKNB\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 985,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(\"10\"),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading,\n                                                                                children: \"10\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 989,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(\"100\"),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading,\n                                                                                children: \"100\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 997,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(tokenABalance),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading || parseFloat(tokenABalance) === 0,\n                                                                                children: \"Max\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1005,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 988,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 984,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleSwap,\n                                                        disabled: loading || !swapAmount || parseFloat(swapAmount) <= 0,\n                                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1026,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Processing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1025,\n                                                            columnNumber: 25\n                                                        }, this) : \"Swap Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"liquidity\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1038,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token A Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1042,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: liquidityAmountA,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Liquidity A input changed:\", e.target.value);\n                                                                            setLiquidityAmountA(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1045,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Available: \",\n                                                                            parseFloat(tokenABalance).toFixed(4),\n                                                                            ' TKNA | Current: \"',\n                                                                            liquidityAmountA,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1059,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1041,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token B Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1065,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: liquidityAmountB,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Liquidity B input changed:\", e.target.value);\n                                                                            setLiquidityAmountB(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1068,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Available: \",\n                                                                            parseFloat(tokenBBalance).toFixed(4),\n                                                                            ' TKNB | Current: \"',\n                                                                            liquidityAmountB,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1082,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1064,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleAddLiquidity,\n                                                        disabled: loading || !liquidityAmountA || !liquidityAmountB,\n                                                        className: \"w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"stake\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1101,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Stake Amount (LP Tokens)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1104,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: stakeAmount,\n                                                                onChange: (e)=>{\n                                                                    console.log(\"Stake input changed:\", e.target.value);\n                                                                    setStakeAmount(e.target.value);\n                                                                },\n                                                                placeholder: \"0.0\",\n                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                disabled: loading,\n                                                                min: \"0\",\n                                                                step: \"0.01\",\n                                                                autoComplete: \"off\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1107,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    \"Available: \",\n                                                                    parseFloat(lpTokenBalance).toFixed(4),\n                                                                    ' NLP | Current: \"',\n                                                                    stakeAmount,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1121,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1103,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleStake,\n                                                        disabled: loading || !stakeAmount || parseFloat(lpTokenBalance) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-orange-600 to-red-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1126,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1100,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"claim\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1139,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6 p-4 bg-yellow-50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mb-2\",\n                                                                children: \"Pending Rewards\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1142,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-mono text-3xl font-bold text-yellow-600\",\n                                                                children: parseFloat(earnedRewards).toFixed(6)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1143,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"TKNB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1146,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1141,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Staked\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1151,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold\",\n                                                                        children: parseFloat(stakedBalance).toFixed(4)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1152,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1155,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1150,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"APY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1158,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold text-green-600\",\n                                                                        children: \"~50%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1159,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"Estimated\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1162,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1157,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1149,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleClaim,\n                                                        disabled: loading || parseFloat(earnedRewards) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-yellow-600 to-orange-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-yellow-700 hover:to-orange-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1166,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1138,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"batch\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Batch Operations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1179,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-6\",\n                                                        children: \"Generate multiple transactions for maximum Nexus testnet interaction!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-blue-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-blue-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDD04 Batch Swaps\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1187,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-blue-700 mb-3\",\n                                                                        children: \"Execute 5 separate swap transactions (10, 20, 30, 40, 50 TKNA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1188,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleBatchSwaps,\n                                                                        disabled: loading || parseFloat(tokenABalance) < 150,\n                                                                        className: \"w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Execute Batch Swaps (5 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1191,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 mt-1\",\n                                                                        children: \"Requires: 150 TKNA minimum\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1199,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1186,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-purple-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-purple-800 mb-2\",\n                                                                        children: \"⚡ Transaction Spam\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1206,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-purple-700 mb-3\",\n                                                                        children: \"Generate 18 approval transactions across all contracts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1207,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleTransactionSpam,\n                                                                        disabled: loading,\n                                                                        className: \"w-full bg-purple-600 text-white py-3 rounded-lg font-medium hover:bg-purple-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Generate Transaction Spam (18 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1210,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-purple-600 mt-1\",\n                                                                        children: \"Generates many approval transactions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1218,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1205,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-green-50 rounded-xl text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-green-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCCA Transaction Counter\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1225,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-700 mb-2\",\n                                                                        children: \"Estimated transactions per full cycle:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1226,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"Basic Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1231,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"8-10 TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1232,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1230,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"With Batches\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1235,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"25+ TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1236,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1234,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1229,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1224,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-yellow-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCA1 Pro Tips\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1243,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-sm text-yellow-700 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Use batch operations to generate many transactions quickly\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1245,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Each operation creates multiple blockchain interactions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1246,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Perfect for maximizing Nexus testnet contribution\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1247,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Monitor your transaction count in MetaMask\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1248,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1244,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1242,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1184,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1178,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 947,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(tokenABalance) === 0 && activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-6 mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-yellow-800 mb-2\",\n                                                children: \"Need Test Tokens?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 19\n                                            }, this),\n                                            isDeployerAccount ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"You are the deployer! You have 1,000,000 Token A available.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1262,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: updateBalances,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200\",\n                                                        children: \"Refresh Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1265,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1261,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"To get test tokens, switch to the deployer account or ask for a transfer:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1275,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-yellow-600 mb-3 font-mono bg-yellow-100 p-2 rounded\",\n                                                        children: [\n                                                            \"Deployer: \",\n                                                            (_contractAddresses_deployer = contractAddresses.deployer) === null || _contractAddresses_deployer === void 0 ? void 0 : _contractAddresses_deployer.slice(0, 20),\n                                                            \"...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1278,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: requestTestTokens,\n                                                        disabled: loading,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                        children: \"Show Instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1258,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"DeFi Flow Guide:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1297,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"1. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Swap\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1299,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Trade Token A ⇄ Token B\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"2. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1300,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Provide both tokens → Get LP tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"3. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Stake\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1301,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Stake LP tokens → Earn rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"4. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Claim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1302,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Collect your earned rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"5. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Repeat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1303,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": More transactions = More Nexus interaction!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1303,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1298,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1296,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 876,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 806,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 782,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Xn9OxmB2iOgPw0us/9Bh41mAbgc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});