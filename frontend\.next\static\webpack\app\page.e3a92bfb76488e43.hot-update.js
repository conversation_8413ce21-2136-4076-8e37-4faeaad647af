"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Contract ABIs - Complete for all DeFi operations\nconst TokenA_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) external returns (bool)\",\n    \"function allowance(address owner, address spender) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) external returns (bool)\",\n    \"function transferFrom(address from, address to, uint256 amount) external returns (bool)\"\n];\nconst TokenB_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\"\n];\nconst TokenSwap_ABI = [\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getTokenBLiquidity() external view returns (uint256)\",\n    \"function getTokenABalance() external view returns (uint256)\",\n    \"function tokenA() external view returns (address)\",\n    \"function tokenB() external view returns (address)\",\n    \"event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut)\"\n];\nconst LiquidityPool_ABI = [\n    \"function addLiquidity(uint256 amountA, uint256 amountB) external returns (uint256)\",\n    \"function removeLiquidity(uint256 liquidity) external returns (uint256, uint256)\",\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getReserves() external view returns (uint256, uint256)\",\n    \"function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256)\",\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function name() external view returns (string)\"\n];\nconst Staking_ABI = [\n    \"function stake(uint256 amount) external\",\n    \"function withdraw(uint256 amount) external\",\n    \"function claimReward() external\",\n    \"function exit() external\",\n    \"function compound() external\",\n    \"function multiStake(uint256[] amounts) external\",\n    \"function multiWithdraw(uint256[] amounts) external\",\n    \"function multiClaim(uint256 times) external\",\n    \"function earned(address account) external view returns (uint256)\",\n    \"function balances(address account) external view returns (uint256)\",\n    \"function getStakingInfo(address user) external view returns (uint256, uint256, uint256, uint256)\"\n];\n// Nexus network configuration\nconst NEXUS_NETWORK = {\n    chainId: \"0xF64\",\n    chainName: \"Nexus Testnet III\",\n    nativeCurrency: {\n        name: \"NEX\",\n        symbol: \"NEX\",\n        decimals: 18\n    },\n    rpcUrls: [\n        \"https://testnet3.rpc.nexus.xyz\"\n    ],\n    blockExplorerUrls: [\n        \"https://explorer.nexus.xyz\"\n    ]\n};\n// Default contract addresses (will be replaced with deployed addresses)\nconst DEFAULT_ADDRESSES = {\n    TokenA: \"0x0000000000000000000000000000000000000000\",\n    TokenB: \"0x0000000000000000000000000000000000000000\",\n    TokenSwap: \"0x0000000000000000000000000000000000000000\"\n};\nfunction Home() {\n    var _contractAddresses_deployer;\n    _s();\n    // Basic states\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenABalance, setTokenABalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [tokenBBalance, setTokenBBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [lpTokenBalance, setLpTokenBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [stakedBalance, setStakedBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [earnedRewards, setEarnedRewards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_ADDRESSES);\n    const [liquidity, setLiquidity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [isCorrectNetwork, setIsCorrectNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab and form states\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"swap\");\n    const [swapAmount, setSwapAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountA, setLiquidityAmountA] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountB, setLiquidityAmountB] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [stakeAmount, setStakeAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Token selection states\n    const [selectedTokenIn, setSelectedTokenIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenA\");\n    const [selectedTokenOut, setSelectedTokenOut] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenB\");\n    const [availableTokens, setAvailableTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkIfWalletIsConnected();\n        loadContractAddresses();\n        setupEventListeners();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (account && isCorrectNetwork) {\n            updateBalances();\n            updateLiquidity();\n            loadAvailableTokens();\n        }\n    }, [\n        account,\n        contractAddresses,\n        isCorrectNetwork\n    ]);\n    async function loadAvailableTokens() {\n        if (!account || !isCorrectNetwork) return;\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const tokens = [\n                {\n                    symbol: \"TKNA\",\n                    name: \"Token A\",\n                    address: contractAddresses.TokenA,\n                    balance: tokenABalance,\n                    decimals: 18\n                },\n                {\n                    symbol: \"TKNB\",\n                    name: \"Token B\",\n                    address: contractAddresses.TokenB,\n                    balance: tokenBBalance,\n                    decimals: 18\n                }\n            ];\n            // Add LP token if available\n            if (contractAddresses.LiquidityPool) {\n                tokens.push({\n                    symbol: \"NLP\",\n                    name: \"Nexus LP Token\",\n                    address: contractAddresses.LiquidityPool,\n                    balance: lpTokenBalance,\n                    decimals: 18\n                });\n            }\n            setAvailableTokens(tokens);\n        } catch (error) {\n            console.error(\"Error loading available tokens:\", error);\n        }\n    }\n    function setupEventListeners() {\n        const { ethereum } = window;\n        if (!ethereum) return;\n        // Listen for account changes\n        ethereum.on(\"accountsChanged\", (accounts)=>{\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                checkNetwork();\n            } else {\n                setAccount(\"\");\n                setIsCorrectNetwork(false);\n            }\n        });\n        // Listen for network changes\n        ethereum.on(\"chainChanged\", (chainId)=>{\n            console.log(\"Network changed to:\", chainId);\n            checkNetwork();\n            // Reload page to reset state\n            window.location.reload();\n        });\n        // Cleanup function\n        return ()=>{\n            if (ethereum.removeListener) {\n                ethereum.removeListener(\"accountsChanged\", ()=>{});\n                ethereum.removeListener(\"chainChanged\", ()=>{});\n            }\n        };\n    }\n    async function loadContractAddresses() {\n        try {\n            // Try to load deployed addresses from public folder\n            const response = await fetch(\"/deployedAddresses.json\");\n            if (response.ok) {\n                const addresses = await response.json();\n                setContractAddresses(addresses);\n                console.log(\"Loaded contract addresses:\", addresses);\n            } else {\n                console.warn(\"Could not load deployed addresses, using defaults\");\n            }\n        } catch (error) {\n            console.warn(\"Could not load deployed addresses:\", error);\n        }\n    }\n    async function checkIfWalletIsConnected() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_accounts\"\n            });\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                await checkNetwork();\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setError(\"Error saat mengecek koneksi wallet\");\n        }\n    }\n    async function checkNetwork() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setIsCorrectNetwork(false);\n                return;\n            }\n            const chainId = await ethereum.request({\n                method: \"eth_chainId\"\n            });\n            console.log(\"Current chainId:\", chainId, \"Expected:\", NEXUS_NETWORK.chainId);\n            // Convert both to same format for comparison\n            const currentChainId = parseInt(chainId, 16);\n            const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);\n            if (currentChainId === expectedChainId) {\n                setIsCorrectNetwork(true);\n                setError(\"\");\n                console.log(\"✅ Connected to correct network\");\n            } else {\n                setIsCorrectNetwork(false);\n                setError(\"Wrong network. Current: \".concat(currentChainId, \", Expected: \").concat(expectedChainId, \" (Nexus Testnet III)\"));\n                console.log(\"❌ Wrong network detected\");\n            }\n        } catch (error) {\n            console.error(\"Error checking network:\", error);\n            setIsCorrectNetwork(false);\n            setError(\"Error checking network\");\n        }\n    }\n    async function switchToNexusNetwork() {\n        try {\n            const { ethereum } = window;\n            setError(\"\");\n            setSuccess(\"Switching to Nexus network...\");\n            try {\n                await ethereum.request({\n                    method: \"wallet_switchEthereumChain\",\n                    params: [\n                        {\n                            chainId: NEXUS_NETWORK.chainId\n                        }\n                    ]\n                });\n                console.log(\"✅ Network switch requested\");\n            } catch (switchError) {\n                console.log(\"Switch error code:\", switchError.code);\n                // Network belum ditambahkan, tambahkan dulu\n                if (switchError.code === 4902) {\n                    console.log(\"Adding Nexus network...\");\n                    await ethereum.request({\n                        method: \"wallet_addEthereumChain\",\n                        params: [\n                            NEXUS_NETWORK\n                        ]\n                    });\n                    console.log(\"✅ Network added\");\n                } else {\n                    throw switchError;\n                }\n            }\n            // Wait a bit for network to switch\n            setTimeout(async ()=>{\n                await checkNetwork();\n                setSuccess(\"\");\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error switching network:\", error);\n            setError(\"Gagal switch ke Nexus network: \" + (error.message || \"Unknown error\"));\n            setSuccess(\"\");\n        }\n    }\n    async function connectWallet() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            setAccount(accounts[0]);\n            await checkNetwork();\n            setError(\"\");\n            setSuccess(\"Wallet berhasil terhubung!\");\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n            setError(\"Gagal menghubungkan wallet\");\n        }\n    }\n    async function updateBalances() {\n        if (!account || !isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {\n            console.log(\"Contract addresses not loaded yet, skipping balance update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating all balances for:\", account);\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, provider);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, provider);\n            // Get basic token balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            setTokenABalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceA));\n            setTokenBBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceB));\n            // Get LP token balance if LiquidityPool exists\n            if (contractAddresses.LiquidityPool) {\n                const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, provider);\n                const lpBalance = await lpContract.balanceOf(account);\n                setLpTokenBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(lpBalance));\n            }\n            // Get staking info if Staking exists\n            if (contractAddresses.Staking) {\n                const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, provider);\n                const [stakedBal, earned] = await Promise.all([\n                    stakingContract.balances(account),\n                    stakingContract.earned(account)\n                ]);\n                setStakedBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(stakedBal));\n                setEarnedRewards(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(earned));\n            }\n            console.log(\"All balances updated successfully\");\n        } catch (error) {\n            console.error(\"Error updating balances:\", error);\n            setError(\"Error connecting to contracts\");\n        }\n    }\n    async function updateLiquidity() {\n        if (!isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {\n            console.log(\"TokenSwap address not loaded yet, skipping liquidity update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating liquidity for TokenSwap:\", contractAddresses.TokenSwap);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);\n            try {\n                const liquidityAmount = await swapContract.getTokenBLiquidity();\n                console.log(\"Raw liquidity:\", liquidityAmount.toString());\n                setLiquidity(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(liquidityAmount));\n                console.log(\"Liquidity updated successfully\");\n            } catch (contractError) {\n                console.error(\"TokenSwap contract call error:\", contractError);\n                setError(\"Error reading liquidity. TokenSwap contract may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating liquidity:\", error);\n            setError(\"Error connecting to TokenSwap contract\");\n        }\n    }\n    async function handleSwap() {\n        if (!swapAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            // Determine which tokens to use\n            const isTokenAToB = selectedTokenIn === \"TokenA\";\n            const inputTokenAddress = isTokenAToB ? contractAddresses.TokenA : contractAddresses.TokenB;\n            const inputTokenContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(inputTokenAddress, TokenA_ABI, signer);\n            // Use LiquidityPool for swapping (more advanced than TokenSwap)\n            const swapContract = contractAddresses.LiquidityPool ? new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer) : new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(swapAmount);\n            // Check balance\n            const balance = await inputTokenContract.balanceOf(account);\n            if (balance < amount) {\n                setError(\"Saldo \".concat(selectedTokenIn === \"TokenA\" ? \"Token A\" : \"Token B\", \" tidak mencukupi\"));\n                return;\n            }\n            // Check allowance\n            const swapAddress = contractAddresses.LiquidityPool || contractAddresses.TokenSwap;\n            const allowance = await inputTokenContract.allowance(account, swapAddress);\n            if (allowance < amount) {\n                setSuccess(\"Menyetujui penggunaan \".concat(selectedTokenIn === \"TokenA\" ? \"Token A\" : \"Token B\", \"...\"));\n                const approveTx = await inputTokenContract.approve(swapAddress, amount);\n                await approveTx.wait();\n                setSuccess(\"Approval berhasil! Melakukan swap...\");\n            } else {\n                setSuccess(\"Melakukan swap...\");\n            }\n            // Perform swap\n            if (contractAddresses.LiquidityPool) {\n                // Use LiquidityPool swap function\n                const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);\n                await swapTx.wait();\n            } else {\n                // Use old TokenSwap (only supports TokenA -> TokenB)\n                if (!isTokenAToB) {\n                    setError(\"TokenSwap hanya mendukung Token A → Token B\");\n                    return;\n                }\n                const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);\n                await swapTx.wait();\n            }\n            setSuccess(\"Swap \".concat(selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" → \").concat(selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" berhasil! \\uD83C\\uDF89\"));\n            setSwapAmount(\"\");\n            // Update balances\n            await updateBalances();\n            await updateLiquidity();\n        } catch (error) {\n            console.error(\"Error during swap:\", error);\n            if (error.code === \"ACTION_REJECTED\") {\n                setError(\"Transaksi dibatalkan oleh user\");\n            } else if (error.message.includes(\"insufficient funds\")) {\n                setError(\"Saldo NEX tidak mencukupi untuk gas fee\");\n            } else {\n                setError(\"Swap gagal: \" + (error.reason || error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleAddLiquidity() {\n        if (!liquidityAmountA || !liquidityAmountB || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const amountA = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountA);\n            const amountB = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountB);\n            // Check balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            if (balanceA < amountA) {\n                setError(\"Saldo Token A tidak mencukupi\");\n                return;\n            }\n            if (balanceB < amountB) {\n                setError(\"Saldo Token B tidak mencukupi\");\n                return;\n            }\n            // Approve tokens\n            setSuccess(\"Menyetujui Token A...\");\n            const approveATx = await tokenAContract.approve(contractAddresses.LiquidityPool, amountA);\n            await approveATx.wait();\n            setSuccess(\"Menyetujui Token B...\");\n            const approveBTx = await tokenBContract.approve(contractAddresses.LiquidityPool, amountB);\n            await approveBTx.wait();\n            // Add liquidity\n            setSuccess(\"Menambahkan likuiditas...\");\n            const addLiquidityTx = await lpContract.addLiquidity(amountA, amountB);\n            await addLiquidityTx.wait();\n            setSuccess(\"Likuiditas berhasil ditambahkan! \\uD83C\\uDF89\");\n            setLiquidityAmountA(\"\");\n            setLiquidityAmountB(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error adding liquidity:\", error);\n            setError(\"Gagal menambahkan likuiditas: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleStake() {\n        if (!stakeAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(stakeAmount);\n            // Check LP balance\n            const lpBalance = await lpContract.balanceOf(account);\n            if (lpBalance < amount) {\n                setError(\"Saldo LP Token tidak mencukupi\");\n                return;\n            }\n            // Approve LP tokens\n            setSuccess(\"Menyetujui LP Tokens...\");\n            const approveTx = await lpContract.approve(contractAddresses.Staking, amount);\n            await approveTx.wait();\n            // Stake\n            setSuccess(\"Melakukan stake...\");\n            const stakeTx = await stakingContract.stake(amount);\n            await stakeTx.wait();\n            setSuccess(\"Stake berhasil! \\uD83C\\uDF89\");\n            setStakeAmount(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error staking:\", error);\n            setError(\"Gagal melakukan stake: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleClaim() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            setSuccess(\"Mengklaim rewards...\");\n            const claimTx = await stakingContract.claimReward();\n            await claimTx.wait();\n            setSuccess(\"Rewards berhasil diklaim! \\uD83C\\uDF89\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error claiming:\", error);\n            setError(\"Gagal mengklaim rewards: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const clearMessages = ()=>{\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const isDeployerAccount = account && contractAddresses.deployer && account.toLowerCase() === contractAddresses.deployer.toLowerCase();\n    async function requestTestTokens() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"Requesting test tokens...\");\n        try {\n            // Check if user is the deployer\n            if (isDeployerAccount) {\n                setSuccess(\"You are the deployer! You already have all tokens \\uD83C\\uDF89\");\n                await updateBalances();\n                return;\n            }\n            // For non-deployer users, show instructions\n            setError(\"\");\n            setSuccess(\"\");\n            alert(\"To get test tokens:\\n\\n1. Switch to deployer account: \".concat(contractAddresses.deployer, \"\\n2. Or ask the deployer to send you tokens\\n3. Or use a faucet if available\\n\\nYour current address: \").concat(account, \"\\nDeployer address: \").concat(contractAddresses.deployer, \"\\n\\nThe deployer has 1,000,000 Token A available for distribution.\"));\n        } catch (error) {\n            console.error(\"Error requesting test tokens:\", error);\n            setError(\"Failed to get test tokens: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Batch Operations untuk Maximum Transactions\n    async function handleBatchSwaps() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            // Multiple small swaps untuk generate banyak transaksi\n            const amounts = [\n                \"10\",\n                \"20\",\n                \"30\",\n                \"40\",\n                \"50\"\n            ]; // 5 transaksi swap\n            let totalAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"0\");\n            for (const amount of amounts){\n                totalAmount += ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amount);\n            }\n            // Check balance\n            const balance = await tokenAContract.balanceOf(account);\n            if (balance < totalAmount) {\n                setError(\"Saldo Token A tidak mencukupi untuk batch swaps\");\n                return;\n            }\n            // Approve total amount\n            setSuccess(\"Menyetujui total amount untuk batch swaps...\");\n            const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, totalAmount);\n            await approveTx.wait();\n            // Execute multiple swaps\n            for(let i = 0; i < amounts.length; i++){\n                setSuccess(\"Melakukan swap \".concat(i + 1, \"/\").concat(amounts.length, \"...\"));\n                const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amounts[i]);\n                const swapTx = await swapContract.swap(contractAddresses.TokenA, amount, 0);\n                await swapTx.wait();\n                // Small delay between transactions\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            setSuccess(\"Batch swaps berhasil! \".concat(amounts.length, \" transaksi completed \\uD83C\\uDF89\"));\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error during batch swaps:\", error);\n            setError(\"Batch swaps gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleTransactionSpam() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            // Generate many approval transactions\n            const spamAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"1\");\n            const contracts = [\n                contractAddresses.TokenSwap,\n                contractAddresses.LiquidityPool,\n                contractAddresses.Staking\n            ];\n            let transactionCount = 0;\n            for (const contractAddr of contracts){\n                // Multiple approvals for each contract\n                for(let i = 0; i < 3; i++){\n                    setSuccess(\"Generating approval transaction \".concat(transactionCount + 1, \"...\"));\n                    const approveTx = await tokenAContract.approve(contractAddr, spamAmount);\n                    await approveTx.wait();\n                    transactionCount++;\n                    // Also approve TokenB\n                    const approveBTx = await tokenBContract.approve(contractAddr, spamAmount);\n                    await approveBTx.wait();\n                    transactionCount++;\n                    // Small delay\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                }\n            }\n            setSuccess(\"Transaction spam completed! \".concat(transactionCount, \" transactions generated \\uD83D\\uDE80\"));\n        } catch (error) {\n            console.error(\"Error during transaction spam:\", error);\n            setError(\"Transaction spam gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-6 right-6 flex flex-col items-end space-y-2\",\n                children: [\n                    account && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Connected:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 806,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            formatAddress(account)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 805,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full \".concat(isCorrectNetwork ? \"bg-green-500\" : \"bg-red-500\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: isCorrectNetwork ? \"Nexus Testnet III\" : \"Wrong Network\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 811,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 809,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 max-w-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Expected Chain: 3940 (0xF64)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 817,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Contracts: \",\n                                    contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? \"✅\" : \"❌\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Deployer: \",\n                                    contractAddresses.deployer ? contractAddresses.deployer.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 819,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Your Address: \",\n                                    account ? account.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 820,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 803,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-screen px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"Nexus Swap\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Decentralized token exchange on Nexus blockchain. Swap Token A for Token B at a fixed 1:1 ratio.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 830,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 826,\n                        columnNumber: 9\n                    }, this),\n                    (error || success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mb-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 840,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-red-500 hover:text-red-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-green-500 hover:text-green-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 849,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 837,\n                        columnNumber: 11\n                    }, this),\n                    !account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: connectWallet,\n                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Connect your MetaMask wallet to start swapping tokens\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 858,\n                        columnNumber: 11\n                    }, this) : !isCorrectNetwork ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: switchToNexusNetwork,\n                                        className: \"bg-orange-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                        children: \"Switch to Nexus Network\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: checkNetwork,\n                                            className: \"text-sm text-blue-600 hover:text-blue-800 underline\",\n                                            children: \"Already switched? Click to refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 872,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Please switch to Nexus Testnet III to continue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 890,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 871,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl shadow-xl p-6 border border-gray-200 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-center\",\n                                        children: \"Portfolio Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-blue-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-blue-600\",\n                                                        children: parseFloat(tokenABalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-purple-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-purple-600\",\n                                                        children: parseFloat(tokenBBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-green-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-green-600\",\n                                                        children: parseFloat(lpTokenBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"NLP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-orange-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Staked\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-orange-600\",\n                                                        children: parseFloat(stakedBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"NLP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 899,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(earnedRewards) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center p-3 bg-yellow-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Pending Rewards\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-mono text-xl font-bold text-yellow-600\",\n                                                children: [\n                                                    parseFloat(earnedRewards).toFixed(4),\n                                                    \" TKNB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 897,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl shadow-xl border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex border-b border-gray-200\",\n                                        children: [\n                                            {\n                                                id: \"swap\",\n                                                label: \"Swap\",\n                                                icon: \"\\uD83D\\uDD04\"\n                                            },\n                                            {\n                                                id: \"liquidity\",\n                                                label: \"Add Liquidity\",\n                                                icon: \"\\uD83D\\uDCA7\"\n                                            },\n                                            {\n                                                id: \"stake\",\n                                                label: \"Stake\",\n                                                icon: \"\\uD83C\\uDFE6\"\n                                            },\n                                            {\n                                                id: \"claim\",\n                                                label: \"Claim\",\n                                                icon: \"\\uD83C\\uDF81\"\n                                            },\n                                            {\n                                                id: \"batch\",\n                                                label: \"Batch Ops\",\n                                                icon: \"⚡\"\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"flex-1 px-4 py-3 text-sm font-medium transition-colors \".concat(activeTab === tab.id ? \"text-blue-600 border-b-2 border-blue-600 bg-blue-50\" : \"text-gray-500 hover:text-gray-700 hover:bg-gray-50\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: tab.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    tab.label\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Swap Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 969,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"From\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 975,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: selectedTokenIn,\n                                                                                onChange: (e)=>setSelectedTokenIn(e.target.value),\n                                                                                className: \"flex-1 bg-white border-2 border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                                                disabled: loading,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenA\",\n                                                                                        children: \"TKNA - Token A\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 983,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenB\",\n                                                                                        children: \"TKNB - Token B\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 984,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 977,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"px-3 py-2 bg-gray-100 rounded-lg text-sm\",\n                                                                                children: [\n                                                                                    \"Balance: \",\n                                                                                    selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 986,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 976,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>{\n                                                                        const temp = selectedTokenIn;\n                                                                        setSelectedTokenIn(selectedTokenOut);\n                                                                        setSelectedTokenOut(temp);\n                                                                    },\n                                                                    className: \"p-2 bg-blue-100 hover:bg-blue-200 rounded-full transition-colors\",\n                                                                    disabled: loading,\n                                                                    children: \"\\uD83D\\uDD04\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 994,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"To\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1010,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: selectedTokenOut,\n                                                                                onChange: (e)=>setSelectedTokenOut(e.target.value),\n                                                                                className: \"flex-1 bg-white border-2 border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                                                disabled: loading,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenB\",\n                                                                                        children: \"TKNB - Token B\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1018,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenA\",\n                                                                                        children: \"TKNA - Token A\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1019,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1012,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"px-3 py-2 bg-gray-100 rounded-lg text-sm\",\n                                                                                children: [\n                                                                                    \"Balance: \",\n                                                                                    selectedTokenOut === \"TokenA\" ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1021,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1011,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1009,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 972,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Amount to Swap\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: swapAmount,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Swap input changed:\", e.target.value);\n                                                                            setSwapAmount(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-4 pr-20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400 shadow-sm\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1034,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                        children: selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1048,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1033,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Exchange rate: 1 \",\n                                                                            selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\",\n                                                                            \" ≈ 1 \",\n                                                                            selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1053,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(\"10\"),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading,\n                                                                                children: \"10\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1057,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(\"100\"),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading,\n                                                                                children: \"100\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1065,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(selectedTokenIn === \"TokenA\" ? tokenABalance : tokenBBalance),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading || (selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance) === 0 : parseFloat(tokenBBalance) === 0),\n                                                                                children: \"Max\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1073,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1056,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1052,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    \"Available: \",\n                                                                    selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4),\n                                                                    \" \",\n                                                                    selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\",\n                                                                    ' | Input: \"',\n                                                                    swapAmount,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1083,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Pool Liquidity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1090,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-mono text-lg font-semibold text-gray-800\",\n                                                                children: [\n                                                                    parseFloat(liquidity).toFixed(2),\n                                                                    \" Token B\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1091,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1089,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleSwap,\n                                                        disabled: loading || !swapAmount || parseFloat(swapAmount) <= 0,\n                                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1105,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Processing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1104,\n                                                            columnNumber: 25\n                                                        }, this) : \"Swap \".concat(selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" → \").concat(selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1097,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"liquidity\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1117,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token A Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1121,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: liquidityAmountA,\n                                                                                onChange: (e)=>{\n                                                                                    console.log(\"Liquidity A input changed:\", e.target.value);\n                                                                                    setLiquidityAmountA(e.target.value);\n                                                                                },\n                                                                                placeholder: \"0.0\",\n                                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                                disabled: loading,\n                                                                                min: \"0\",\n                                                                                step: \"0.01\",\n                                                                                autoComplete: \"off\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1125,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                                children: \"TKNA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1139,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1124,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"Available: \",\n                                                                                    parseFloat(tokenABalance).toFixed(4),\n                                                                                    \" TKNA\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1144,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(\"10\"),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"10\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1148,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(\"50\"),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"50\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1156,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(tokenABalance),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading || parseFloat(tokenABalance) === 0,\n                                                                                        children: \"Max\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1164,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1147,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1143,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            'Input: \"',\n                                                                            liquidityAmountA,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1174,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1120,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token B Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1180,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: liquidityAmountB,\n                                                                                onChange: (e)=>{\n                                                                                    console.log(\"Liquidity B input changed:\", e.target.value);\n                                                                                    setLiquidityAmountB(e.target.value);\n                                                                                },\n                                                                                placeholder: \"0.0\",\n                                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                                disabled: loading,\n                                                                                min: \"0\",\n                                                                                step: \"0.01\",\n                                                                                autoComplete: \"off\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1184,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                                children: \"TKNB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1198,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1183,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"Available: \",\n                                                                                    parseFloat(tokenBBalance).toFixed(4),\n                                                                                    \" TKNB\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1203,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(\"10\"),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"10\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1207,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(\"50\"),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"50\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1215,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(tokenBBalance),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading || parseFloat(tokenBBalance) === 0,\n                                                                                        children: \"Max\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1223,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1206,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1202,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            'Input: \"',\n                                                                            liquidityAmountB,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1233,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1179,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1119,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleAddLiquidity,\n                                                        disabled: loading || !liquidityAmountA || !liquidityAmountB,\n                                                        className: \"w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1239,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1116,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"stake\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1252,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Stake Amount (LP Tokens)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1255,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: stakeAmount,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Stake input changed:\", e.target.value);\n                                                                            setStakeAmount(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1259,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1273,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1258,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    \"Available: \",\n                                                                    parseFloat(lpTokenBalance).toFixed(4),\n                                                                    ' NLP | Current: \"',\n                                                                    stakeAmount,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1277,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1254,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleStake,\n                                                        disabled: loading || !stakeAmount || parseFloat(lpTokenBalance) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-orange-600 to-red-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1282,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1251,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"claim\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1295,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6 p-4 bg-yellow-50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mb-2\",\n                                                                children: \"Pending Rewards\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1298,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-mono text-3xl font-bold text-yellow-600\",\n                                                                children: parseFloat(earnedRewards).toFixed(6)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1299,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"TKNB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1302,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1297,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Staked\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1307,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold\",\n                                                                        children: parseFloat(stakedBalance).toFixed(4)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1308,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1311,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1306,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"APY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1314,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold text-green-600\",\n                                                                        children: \"~50%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1315,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"Estimated\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1318,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1313,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1305,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleClaim,\n                                                        disabled: loading || parseFloat(earnedRewards) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-yellow-600 to-orange-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-yellow-700 hover:to-orange-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1322,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1294,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"batch\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Batch Operations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1335,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-6\",\n                                                        children: \"Generate multiple transactions for maximum Nexus testnet interaction!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1336,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-blue-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-blue-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDD04 Batch Swaps\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1343,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-blue-700 mb-3\",\n                                                                        children: \"Execute 5 separate swap transactions (10, 20, 30, 40, 50 TKNA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1344,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleBatchSwaps,\n                                                                        disabled: loading || parseFloat(tokenABalance) < 150,\n                                                                        className: \"w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Execute Batch Swaps (5 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1347,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 mt-1\",\n                                                                        children: \"Requires: 150 TKNA minimum\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1355,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1342,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-purple-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-purple-800 mb-2\",\n                                                                        children: \"⚡ Transaction Spam\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1362,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-purple-700 mb-3\",\n                                                                        children: \"Generate 18 approval transactions across all contracts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1363,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleTransactionSpam,\n                                                                        disabled: loading,\n                                                                        className: \"w-full bg-purple-600 text-white py-3 rounded-lg font-medium hover:bg-purple-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Generate Transaction Spam (18 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1366,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-purple-600 mt-1\",\n                                                                        children: \"Generates many approval transactions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1374,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1361,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-green-50 rounded-xl text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-green-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCCA Transaction Counter\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1381,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-700 mb-2\",\n                                                                        children: \"Estimated transactions per full cycle:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1382,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"Basic Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1387,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"8-10 TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1388,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1386,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"With Batches\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1391,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"25+ TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1392,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1390,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1385,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1380,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-yellow-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCA1 Pro Tips\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1399,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-sm text-yellow-700 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Use batch operations to generate many transactions quickly\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1401,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Each operation creates multiple blockchain interactions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1402,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Perfect for maximizing Nexus testnet contribution\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1403,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Monitor your transaction count in MetaMask\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1404,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1400,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1398,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1340,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1334,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 966,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(tokenABalance) === 0 && activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-6 mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-yellow-800 mb-2\",\n                                                children: \"Need Test Tokens?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1415,\n                                                columnNumber: 19\n                                            }, this),\n                                            isDeployerAccount ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"You are the deployer! You have 1,000,000 Token A available.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1418,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: updateBalances,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200\",\n                                                        children: \"Refresh Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1421,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1417,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"To get test tokens, switch to the deployer account or ask for a transfer:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1431,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-yellow-600 mb-3 font-mono bg-yellow-100 p-2 rounded\",\n                                                        children: [\n                                                            \"Deployer: \",\n                                                            (_contractAddresses_deployer = contractAddresses.deployer) === null || _contractAddresses_deployer === void 0 ? void 0 : _contractAddresses_deployer.slice(0, 20),\n                                                            \"...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1434,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: requestTestTokens,\n                                                        disabled: loading,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                        children: \"Show Instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1437,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1430,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1414,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 940,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"DeFi Flow Guide:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1453,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"1. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Swap\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1455,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Trade Token A ⇄ Token B\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1455,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"2. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1456,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Provide both tokens → Get LP tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"3. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Stake\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1457,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Stake LP tokens → Earn rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1457,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"4. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Claim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1458,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Collect your earned rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1458,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"5. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Repeat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1459,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": More transactions = More Nexus interaction!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1459,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1454,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1452,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 895,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 825,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 801,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Xn9OxmB2iOgPw0us/9Bh41mAbgc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});