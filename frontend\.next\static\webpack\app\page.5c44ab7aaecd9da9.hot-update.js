"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Contract ABIs - Updated with complete function signatures\nconst TokenA_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) external returns (bool)\",\n    \"function allowance(address owner, address spender) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) external returns (bool)\",\n    \"function transferFrom(address from, address to, uint256 amount) external returns (bool)\"\n];\nconst TokenB_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\"\n];\nconst TokenSwap_ABI = [\n    \"function swap(uint256 amount) external returns (bool)\",\n    \"function getTokenBLiquidity() external view returns (uint256)\",\n    \"function getTokenABalance() external view returns (uint256)\",\n    \"function tokenA() external view returns (address)\",\n    \"function tokenB() external view returns (address)\",\n    \"event Swap(address indexed user, uint256 amount)\"\n];\n// Nexus network configuration\nconst NEXUS_NETWORK = {\n    chainId: \"0xF64\",\n    chainName: \"Nexus Testnet III\",\n    nativeCurrency: {\n        name: \"NEX\",\n        symbol: \"NEX\",\n        decimals: 18\n    },\n    rpcUrls: [\n        \"https://testnet3.rpc.nexus.xyz\"\n    ],\n    blockExplorerUrls: [\n        \"https://explorer.nexus.xyz\"\n    ]\n};\n// Default contract addresses (will be replaced with deployed addresses)\nconst DEFAULT_ADDRESSES = {\n    TokenA: \"0x0000000000000000000000000000000000000000\",\n    TokenB: \"0x0000000000000000000000000000000000000000\",\n    TokenSwap: \"0x0000000000000000000000000000000000000000\"\n};\nfunction Home() {\n    _s();\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenABalance, setTokenABalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [tokenBBalance, setTokenBBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [swapAmount, setSwapAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_ADDRESSES);\n    const [liquidity, setLiquidity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [isCorrectNetwork, setIsCorrectNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkIfWalletIsConnected();\n        loadContractAddresses();\n        setupEventListeners();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (account && isCorrectNetwork) {\n            updateBalances();\n            updateLiquidity();\n        }\n    }, [\n        account,\n        contractAddresses,\n        isCorrectNetwork\n    ]);\n    function setupEventListeners() {\n        const { ethereum } = window;\n        if (!ethereum) return;\n        // Listen for account changes\n        ethereum.on(\"accountsChanged\", (accounts)=>{\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                checkNetwork();\n            } else {\n                setAccount(\"\");\n                setIsCorrectNetwork(false);\n            }\n        });\n        // Listen for network changes\n        ethereum.on(\"chainChanged\", (chainId)=>{\n            console.log(\"Network changed to:\", chainId);\n            checkNetwork();\n            // Reload page to reset state\n            window.location.reload();\n        });\n        // Cleanup function\n        return ()=>{\n            if (ethereum.removeListener) {\n                ethereum.removeListener(\"accountsChanged\", ()=>{});\n                ethereum.removeListener(\"chainChanged\", ()=>{});\n            }\n        };\n    }\n    async function loadContractAddresses() {\n        try {\n            // Try to load deployed addresses from public folder\n            const response = await fetch(\"/deployedAddresses.json\");\n            if (response.ok) {\n                const addresses = await response.json();\n                setContractAddresses(addresses);\n                console.log(\"Loaded contract addresses:\", addresses);\n            } else {\n                console.warn(\"Could not load deployed addresses, using defaults\");\n            }\n        } catch (error) {\n            console.warn(\"Could not load deployed addresses:\", error);\n        }\n    }\n    async function checkIfWalletIsConnected() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_accounts\"\n            });\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                await checkNetwork();\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setError(\"Error saat mengecek koneksi wallet\");\n        }\n    }\n    async function checkNetwork() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setIsCorrectNetwork(false);\n                return;\n            }\n            const chainId = await ethereum.request({\n                method: \"eth_chainId\"\n            });\n            console.log(\"Current chainId:\", chainId, \"Expected:\", NEXUS_NETWORK.chainId);\n            // Convert both to same format for comparison\n            const currentChainId = parseInt(chainId, 16);\n            const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);\n            if (currentChainId === expectedChainId) {\n                setIsCorrectNetwork(true);\n                setError(\"\");\n                console.log(\"✅ Connected to correct network\");\n            } else {\n                setIsCorrectNetwork(false);\n                setError(\"Wrong network. Current: \".concat(currentChainId, \", Expected: \").concat(expectedChainId, \" (Nexus Testnet III)\"));\n                console.log(\"❌ Wrong network detected\");\n            }\n        } catch (error) {\n            console.error(\"Error checking network:\", error);\n            setIsCorrectNetwork(false);\n            setError(\"Error checking network\");\n        }\n    }\n    async function switchToNexusNetwork() {\n        try {\n            const { ethereum } = window;\n            setError(\"\");\n            setSuccess(\"Switching to Nexus network...\");\n            try {\n                await ethereum.request({\n                    method: \"wallet_switchEthereumChain\",\n                    params: [\n                        {\n                            chainId: NEXUS_NETWORK.chainId\n                        }\n                    ]\n                });\n                console.log(\"✅ Network switch requested\");\n            } catch (switchError) {\n                console.log(\"Switch error code:\", switchError.code);\n                // Network belum ditambahkan, tambahkan dulu\n                if (switchError.code === 4902) {\n                    console.log(\"Adding Nexus network...\");\n                    await ethereum.request({\n                        method: \"wallet_addEthereumChain\",\n                        params: [\n                            NEXUS_NETWORK\n                        ]\n                    });\n                    console.log(\"✅ Network added\");\n                } else {\n                    throw switchError;\n                }\n            }\n            // Wait a bit for network to switch\n            setTimeout(async ()=>{\n                await checkNetwork();\n                setSuccess(\"\");\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error switching network:\", error);\n            setError(\"Gagal switch ke Nexus network: \" + (error.message || \"Unknown error\"));\n            setSuccess(\"\");\n        }\n    }\n    async function connectWallet() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            setAccount(accounts[0]);\n            await checkNetwork();\n            setError(\"\");\n            setSuccess(\"Wallet berhasil terhubung!\");\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n            setError(\"Gagal menghubungkan wallet\");\n        }\n    }\n    async function updateBalances() {\n        if (!account || !isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {\n            console.log(\"Contract addresses not loaded yet, skipping balance update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating balances for:\", account);\n            console.log(\"TokenA address:\", contractAddresses.TokenA);\n            console.log(\"TokenB address:\", contractAddresses.TokenB);\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, provider);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, provider);\n            // Test if contracts exist\n            try {\n                const [balanceA, balanceB] = await Promise.all([\n                    tokenAContract.balanceOf(account),\n                    tokenBContract.balanceOf(account)\n                ]);\n                console.log(\"Raw balanceA:\", balanceA.toString());\n                console.log(\"Raw balanceB:\", balanceB.toString());\n                setTokenABalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceA));\n                setTokenBBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceB));\n                console.log(\"Balances updated successfully\");\n            } catch (contractError) {\n                console.error(\"Contract call error:\", contractError);\n                setError(\"Error reading token balances. Contracts may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating balances:\", error);\n            setError(\"Error connecting to contracts\");\n        }\n    }\n    async function updateLiquidity() {\n        if (!isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {\n            console.log(\"TokenSwap address not loaded yet, skipping liquidity update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating liquidity for TokenSwap:\", contractAddresses.TokenSwap);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);\n            try {\n                const liquidityAmount = await swapContract.getTokenBLiquidity();\n                console.log(\"Raw liquidity:\", liquidityAmount.toString());\n                setLiquidity(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(liquidityAmount));\n                console.log(\"Liquidity updated successfully\");\n            } catch (contractError) {\n                console.error(\"TokenSwap contract call error:\", contractError);\n                setError(\"Error reading liquidity. TokenSwap contract may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating liquidity:\", error);\n            setError(\"Error connecting to TokenSwap contract\");\n        }\n    }\n    async function handleSwap() {\n        if (!swapAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(swapAmount);\n            // Check balance\n            const balance = await tokenAContract.balanceOf(account);\n            if (balance < amount) {\n                setError(\"Saldo Token A tidak mencukupi\");\n                return;\n            }\n            // Check liquidity\n            const availableLiquidity = await swapContract.getTokenBLiquidity();\n            if (availableLiquidity < amount) {\n                setError(\"Likuiditas Token B tidak mencukupi\");\n                return;\n            }\n            // Check allowance\n            const allowance = await tokenAContract.allowance(account, contractAddresses.TokenSwap);\n            if (allowance < amount) {\n                setSuccess(\"Menyetujui penggunaan Token A...\");\n                const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, amount);\n                await approveTx.wait();\n                setSuccess(\"Approval berhasil! Melakukan swap...\");\n            } else {\n                setSuccess(\"Melakukan swap...\");\n            }\n            // Perform swap\n            const swapTx = await swapContract.swap(amount);\n            await swapTx.wait();\n            setSuccess(\"Swap berhasil! \\uD83C\\uDF89\");\n            setSwapAmount(\"\");\n            // Update balances and liquidity\n            await updateBalances();\n            await updateLiquidity();\n        } catch (error) {\n            console.error(\"Error during swap:\", error);\n            if (error.code === \"ACTION_REJECTED\") {\n                setError(\"Transaksi dibatalkan oleh user\");\n            } else if (error.message.includes(\"insufficient funds\")) {\n                setError(\"Saldo NEX tidak mencukupi untuk gas fee\");\n            } else {\n                setError(\"Swap gagal: \" + (error.reason || error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    }\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const clearMessages = ()=>{\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const isDeployerAccount = account && contractAddresses.deployer && account.toLowerCase() === contractAddresses.deployer.toLowerCase();\n    async function requestTestTokens() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"Requesting test tokens...\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            // Transfer 1000 Token A to user for testing\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"1000\");\n            const transferTx = await tokenAContract.transfer(account, amount);\n            await transferTx.wait();\n            setSuccess(\"Test tokens received! \\uD83C\\uDF89\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error requesting test tokens:\", error);\n            setError(\"Failed to get test tokens: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-6 right-6 flex flex-col items-end space-y-2\",\n                children: [\n                    account && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Connected:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            formatAddress(account)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full \".concat(isCorrectNetwork ? \"bg-green-500\" : \"bg-red-500\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: isCorrectNetwork ? \"Nexus Testnet III\" : \"Wrong Network\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 max-w-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Expected Chain: 3940 (0xF64)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Contracts: \",\n                                    contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? \"✅\" : \"❌\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Deployer: \",\n                                    contractAddresses.deployer ? contractAddresses.deployer.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Your Address: \",\n                                    account ? account.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-screen px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"Nexus Swap\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Decentralized token exchange on Nexus blockchain. Swap Token A for Token B at a fixed 1:1 ratio.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this),\n                    (error || success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mb-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-red-500 hover:text-red-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-green-500 hover:text-green-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 11\n                    }, this),\n                    !account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: connectWallet,\n                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Connect your MetaMask wallet to start swapping tokens\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 11\n                    }, this) : !isCorrectNetwork ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: switchToNexusNetwork,\n                                        className: \"bg-orange-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                        children: \"Switch to Nexus Network\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: checkNetwork,\n                                            className: \"text-sm text-blue-600 hover:text-blue-800 underline\",\n                                            children: \"Already switched? Click to refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Please switch to Nexus Testnet III to continue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl shadow-xl p-8 border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-6 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-blue-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token A Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-2xl font-bold text-blue-600\",\n                                                        children: parseFloat(tokenABalance).toFixed(4)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-purple-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token B Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-2xl font-bold text-purple-600\",\n                                                        children: parseFloat(tokenBBalance).toFixed(4)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-6 p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Available Liquidity\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-mono text-lg font-semibold text-gray-800\",\n                                                children: [\n                                                    parseFloat(liquidity).toFixed(2),\n                                                    \" Token B\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(tokenABalance) === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: requestTestTokens,\n                                                disabled: loading,\n                                                className: \"bg-green-600 text-white px-6 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                children: loading ? \"Requesting...\" : \"Get Test Tokens (1000 TKNA)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-2\",\n                                                children: \"Need tokens for testing? Click to get 1000 Token A\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Swap Amount (Token A → Token B)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: swapAmount,\n                                                        onChange: (e)=>setSwapAmount(e.target.value),\n                                                        placeholder: \"0.0\",\n                                                        className: \"w-full bg-gray-50 border border-gray-300 px-4 py-4 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-lg\",\n                                                        disabled: loading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium\",\n                                                        children: \"TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"Exchange rate: 1 TKNA = 1 TKNB\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleSwap,\n                                        disabled: loading || !swapAmount || parseFloat(swapAmount) <= 0,\n                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Processing...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 19\n                                        }, this) : \"Swap Tokens\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-6 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 text-center\",\n                                                children: \"Smart contracts deployed on Nexus Testnet III\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400 mt-2 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"TokenA: \",\n                                                            formatAddress(contractAddresses.TokenA)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"TokenB: \",\n                                                            formatAddress(contractAddresses.TokenB)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"Swap: \",\n                                                            formatAddress(contractAddresses.TokenSwap)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"How to use:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"1. Make sure you have Token A in your wallet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"2. Enter the amount you want to swap\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: '3. Click \"Swap Tokens\" and confirm the transaction'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"4. Wait for confirmation and see your new Token B balance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"EjoYfBLQTh/yMLgch/KCk+Cuf6Y=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});