{"version": 3, "sources": ["../../../src/client/components/static-generation-bailout.ts"], "names": ["staticGenerationBailout", "StaticGenBailoutError", "Error", "code", "formatErrorMessage", "reason", "opts", "dynamic", "link", "suffix", "staticGenerationStore", "staticGenerationAsyncStorage", "getStore", "forceStatic", "dynamicShouldError", "message", "postpone", "revalidate", "isStaticGeneration", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack"], "mappings": ";;;;+BAwBaA;;;eAAAA;;;oCAtBsB;sDACU;AAE7C,MAAMC,8BAA8BC;;;aAClCC,OAAO;;AACT;AASA,SAASC,mBAAmBC,MAAc,EAAEC,IAAkB;IAC5D,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE,GAAGF,QAAQ,CAAC;IACnC,MAAMG,SAASD,OAAO,AAAC,0BAAuBA,OAAS;IACvD,OAAO,AAAC,SACND,CAAAA,UAAU,AAAC,uBAAqBA,UAAQ,OAAO,EAAC,IACjD,uDAAqDF,SAAO,OAAKI;AACpE;AAEO,MAAMT,0BAAmD,CAC9DK;QACA,EAAEE,OAAO,EAAEC,IAAI,EAAE,sBAAG,CAAC;IAErB,MAAME,wBAAwBC,kEAA4B,CAACC,QAAQ;IACnE,IAAI,CAACF,uBAAuB,OAAO;IAEnC,IAAIA,sBAAsBG,WAAW,EAAE;QACrC,OAAO;IACT;IAEA,IAAIH,sBAAsBI,kBAAkB,EAAE;QAC5C,MAAM,IAAIb,sBACRG,mBAAmBC,QAAQ;YAAEG;YAAMD,SAASA,kBAAAA,UAAW;QAAQ;IAEnE;IAEA,MAAMQ,UAAUX,mBAAmBC,QAAQ;QACzCE;QACA,uEAAuE;QACvE,8EAA8E;QAC9EC,MAAM;IACR;IAEA,2DAA2D;IAC3DE,sBAAsBM,QAAQ,oBAA9BN,sBAAsBM,QAAQ,MAA9BN,uBAAiCL;IAEjC,2EAA2E;IAC3E,QAAQ;IACRK,sBAAsBO,UAAU,GAAG;IAEnC,IAAIP,sBAAsBQ,kBAAkB,EAAE;QAC5C,MAAMC,MAAM,IAAIC,sCAAkB,CAACL;QACnCL,sBAAsBW,uBAAuB,GAAGhB;QAChDK,sBAAsBY,iBAAiB,GAAGH,IAAII,KAAK;QAEnD,MAAMJ;IACR;IAEA,OAAO;AACT"}