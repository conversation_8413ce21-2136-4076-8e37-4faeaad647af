// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title NexusLiquidityPool
 * @dev Advanced liquidity pool for Nexus Layer 1 with enhanced features
 * Features:
 * - AMM with constant product formula (x * y = k)
 * - LP token generation and management
 * - 0.3% swap fees with fee collection
 * - Emergency pause functionality
 * - Owner controls for fee management
 * - Slippage protection
 * - Minimum liquidity lock
 */
contract NexusLiquidityPool is ERC20, ReentrancyGuard, Ownable, Pausable {
    IERC20 public immutable tokenA;
    IERC20 public immutable tokenB;

    uint256 public feeRate = 30; // 0.3% fee (adjustable by owner)
    uint256 public constant FEE_DENOMINATOR = 10000;
    uint256 public constant MINIMUM_LIQUIDITY = 1000; // Minimum liquidity lock

    // Fee collection
    uint256 public collectedFeesA;
    uint256 public collectedFeesB;
    address public feeRecipient;

    // Pool statistics
    uint256 public totalVolumeA;
    uint256 public totalVolumeB;
    uint256 public totalSwaps;

    event LiquidityAdded(address indexed provider, uint256 amountA, uint256 amountB, uint256 liquidity);
    event LiquidityRemoved(address indexed provider, uint256 amountA, uint256 amountB, uint256 liquidity);
    event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut, uint256 fee);
    event FeeRateUpdated(uint256 oldRate, uint256 newRate);
    event FeeRecipientUpdated(address indexed oldRecipient, address indexed newRecipient);
    event FeesCollected(address indexed recipient, uint256 amountA, uint256 amountB);
    
    constructor(
        address _tokenA,
        address _tokenB,
        address _feeRecipient
    ) ERC20("Nexus LP Token", "NLP") Ownable(msg.sender) {
        require(_tokenA != address(0), "TokenA cannot be zero address");
        require(_tokenB != address(0), "TokenB cannot be zero address");
        require(_tokenA != _tokenB, "Tokens must be different");
        require(_feeRecipient != address(0), "Fee recipient cannot be zero address");

        tokenA = IERC20(_tokenA);
        tokenB = IERC20(_tokenB);
        feeRecipient = _feeRecipient;
    }
    
    /**
     * @dev Add liquidity and get LP tokens with minimum liquidity lock
     * Transaction 1: Approve TokenA
     * Transaction 2: Approve TokenB
     * Transaction 3: Add Liquidity
     */
    function addLiquidity(
        uint256 amountA,
        uint256 amountB
    ) external nonReentrant whenNotPaused returns (uint256 liquidity) {
        require(amountA > 0 && amountB > 0, "Amounts must be greater than 0");

        // Get reserves (excluding collected fees)
        uint256 reserveA = tokenA.balanceOf(address(this)) - collectedFeesA;
        uint256 reserveB = tokenB.balanceOf(address(this)) - collectedFeesB;

        if (totalSupply() == 0) {
            // First liquidity provision - lock minimum liquidity
            liquidity = sqrt(amountA * amountB);
            require(liquidity > MINIMUM_LIQUIDITY, "Insufficient initial liquidity");

            // Transfer tokens to pool
            require(tokenA.transferFrom(msg.sender, address(this), amountA), "Transfer A failed");
            require(tokenB.transferFrom(msg.sender, address(this), amountB), "Transfer B failed");

            // Mint LP tokens (subtract minimum liquidity that gets locked)
            _mint(address(0), MINIMUM_LIQUIDITY); // Lock minimum liquidity forever
            _mint(msg.sender, liquidity - MINIMUM_LIQUIDITY);

            liquidity = liquidity - MINIMUM_LIQUIDITY; // Return actual liquidity minted to user
        } else {
            // Calculate liquidity based on existing ratio
            liquidity = min(
                (amountA * totalSupply()) / reserveA,
                (amountB * totalSupply()) / reserveB
            );

            require(liquidity > 0, "Insufficient liquidity minted");

            // Transfer tokens to pool
            require(tokenA.transferFrom(msg.sender, address(this), amountA), "Transfer A failed");
            require(tokenB.transferFrom(msg.sender, address(this), amountB), "Transfer B failed");

            // Mint LP tokens
            _mint(msg.sender, liquidity);
        }

        emit LiquidityAdded(msg.sender, amountA, amountB, liquidity);
    }
    
    /**
     * @dev Remove liquidity and burn LP tokens (excluding fees)
     * Transaction: Remove Liquidity
     */
    function removeLiquidity(
        uint256 liquidity
    ) external nonReentrant whenNotPaused returns (uint256 amountA, uint256 amountB) {
        require(liquidity > 0, "Insufficient liquidity");
        require(balanceOf(msg.sender) >= liquidity, "Insufficient LP tokens");

        // Get reserves (excluding collected fees)
        uint256 reserveA = tokenA.balanceOf(address(this)) - collectedFeesA;
        uint256 reserveB = tokenB.balanceOf(address(this)) - collectedFeesB;

        amountA = (liquidity * reserveA) / totalSupply();
        amountB = (liquidity * reserveB) / totalSupply();

        require(amountA > 0 && amountB > 0, "Insufficient liquidity burned");

        // Burn LP tokens
        _burn(msg.sender, liquidity);

        // Transfer tokens back
        require(tokenA.transfer(msg.sender, amountA), "Transfer A failed");
        require(tokenB.transfer(msg.sender, amountB), "Transfer B failed");

        emit LiquidityRemoved(msg.sender, amountA, amountB, liquidity);
    }
    
    /**
     * @dev Swap tokens with fee collection and statistics
     * Transaction: Swap
     */
    function swap(
        address tokenIn,
        uint256 amountIn,
        uint256 minAmountOut
    ) external nonReentrant whenNotPaused returns (uint256 amountOut) {
        require(amountIn > 0, "Amount must be greater than 0");
        require(tokenIn == address(tokenA) || tokenIn == address(tokenB), "Invalid token");

        bool isTokenA = tokenIn == address(tokenA);
        IERC20 inputToken = isTokenA ? tokenA : tokenB;
        IERC20 outputToken = isTokenA ? tokenB : tokenA;

        // Get reserves (excluding collected fees)
        uint256 reserveIn = inputToken.balanceOf(address(this));
        uint256 reserveOut = outputToken.balanceOf(address(this));

        if (isTokenA) {
            reserveIn -= collectedFeesA;
            reserveOut -= collectedFeesB;
        } else {
            reserveIn -= collectedFeesB;
            reserveOut -= collectedFeesA;
        }

        // Calculate fee and output
        uint256 fee = (amountIn * feeRate) / FEE_DENOMINATOR;
        uint256 amountInAfterFee = amountIn - fee;

        // AMM formula: amountOut = (amountInAfterFee * reserveOut) / (reserveIn + amountInAfterFee)
        amountOut = (amountInAfterFee * reserveOut) / (reserveIn + amountInAfterFee);

        require(amountOut >= minAmountOut, "Insufficient output amount");
        require(amountOut < reserveOut, "Insufficient liquidity");

        // Execute transfers
        require(inputToken.transferFrom(msg.sender, address(this), amountIn), "Transfer in failed");
        require(outputToken.transfer(msg.sender, amountOut), "Transfer out failed");

        // Update fee collection and statistics
        if (isTokenA) {
            collectedFeesA += fee;
            totalVolumeA += amountIn;
        } else {
            collectedFeesB += fee;
            totalVolumeB += amountIn;
        }
        totalSwaps++;

        emit Swap(msg.sender, tokenIn, address(outputToken), amountIn, amountOut, fee);
    }
    
    /**
     * @dev Get current reserves
     */
    function getReserves() external view returns (uint256 reserveA, uint256 reserveB) {
        reserveA = tokenA.balanceOf(address(this));
        reserveB = tokenB.balanceOf(address(this));
    }
    
    /**
     * @dev Calculate swap output amount
     */
    function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256 amountOut) {
        require(tokenIn == address(tokenA) || tokenIn == address(tokenB), "Invalid token");

        bool isTokenA = tokenIn == address(tokenA);

        // Get reserves (excluding collected fees)
        uint256 reserveIn = isTokenA ?
            tokenA.balanceOf(address(this)) - collectedFeesA :
            tokenB.balanceOf(address(this)) - collectedFeesB;
        uint256 reserveOut = isTokenA ?
            tokenB.balanceOf(address(this)) - collectedFeesB :
            tokenA.balanceOf(address(this)) - collectedFeesA;

        // Calculate fee and output using AMM formula
        uint256 fee = (amountIn * feeRate) / FEE_DENOMINATOR;
        uint256 amountInAfterFee = amountIn - fee;

        amountOut = (amountInAfterFee * reserveOut) / (reserveIn + amountInAfterFee);
    }
    
    // Helper functions
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }
    
    function min(uint256 a, uint256 b) internal pure returns (uint256) {
        return a < b ? a : b;
    }

    // Owner functions
    /**
     * @dev Update fee rate (only owner)
     * @param newFeeRate New fee rate (max 1% = 100)
     */
    function setFeeRate(uint256 newFeeRate) external onlyOwner {
        require(newFeeRate <= 100, "Fee rate too high"); // Max 1%
        uint256 oldRate = feeRate;
        feeRate = newFeeRate;
        emit FeeRateUpdated(oldRate, newFeeRate);
    }

    /**
     * @dev Update fee recipient (only owner)
     * @param newRecipient New fee recipient address
     */
    function setFeeRecipient(address newRecipient) external onlyOwner {
        require(newRecipient != address(0), "Cannot be zero address");
        address oldRecipient = feeRecipient;
        feeRecipient = newRecipient;
        emit FeeRecipientUpdated(oldRecipient, newRecipient);
    }

    /**
     * @dev Collect accumulated fees (only owner)
     */
    function collectFees() external onlyOwner {
        uint256 feesA = collectedFeesA;
        uint256 feesB = collectedFeesB;

        if (feesA > 0) {
            collectedFeesA = 0;
            require(tokenA.transfer(feeRecipient, feesA), "Fee transfer A failed");
        }

        if (feesB > 0) {
            collectedFeesB = 0;
            require(tokenB.transfer(feeRecipient, feesB), "Fee transfer B failed");
        }

        emit FeesCollected(feeRecipient, feesA, feesB);
    }

    /**
     * @dev Pause the contract (emergency function)
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause the contract
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev Get pool statistics
     */
    function getPoolStats() external view returns (
        uint256 reserveA,
        uint256 reserveB,
        uint256 totalLiquidity,
        uint256 volumeA,
        uint256 volumeB,
        uint256 swapCount,
        uint256 currentFeeRate
    ) {
        return (
            tokenA.balanceOf(address(this)) - collectedFeesA,
            tokenB.balanceOf(address(this)) - collectedFeesB,
            totalSupply(),
            totalVolumeA,
            totalVolumeB,
            totalSwaps,
            feeRate
        );
    }

    /**
     * @dev Override to add pause functionality
     */
    function _update(address from, address to, uint256 value) internal override whenNotPaused {
        super._update(from, to, value);
    }
}
