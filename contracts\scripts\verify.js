const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🔍 Starting contract verification on Nexus Explorer...");

  // Load deployed addresses
  const addressesPath = path.join(__dirname, "..", "deployedAddresses.json");
  
  if (!fs.existsSync(addressesPath)) {
    console.error("❌ deployedAddresses.json not found!");
    console.log("Please run deployment first: npm run deploy");
    process.exit(1);
  }

  const addresses = JSON.parse(fs.readFileSync(addressesPath, "utf8"));
  console.log("📄 Loaded addresses from:", addressesPath);

  const initialSupply = hre.ethers.parseEther("1000000");

  try {
    // Verify TokenA
    console.log("\n🔍 Verifying TokenA...");
    await hre.run("verify:verify", {
      address: addresses.TokenA,
      constructorArguments: [initialSupply],
      contract: "contracts/TokenA.sol:TokenA"
    });
    console.log("✅ TokenA verified successfully");

    // Verify TokenB
    console.log("\n🔍 Verifying TokenB...");
    await hre.run("verify:verify", {
      address: addresses.TokenB,
      constructorArguments: [initialSupply],
      contract: "contracts/TokenB.sol:TokenB"
    });
    console.log("✅ TokenB verified successfully");

    // Verify TokenSwap
    console.log("\n🔍 Verifying TokenSwap...");
    await hre.run("verify:verify", {
      address: addresses.TokenSwap,
      constructorArguments: [addresses.TokenA, addresses.TokenB],
      contract: "contracts/TokenSwap.sol:TokenSwap"
    });
    console.log("✅ TokenSwap verified successfully");

    console.log("\n🎉 All contracts verified successfully!");
    console.log("\n🔗 View verified contracts on Nexus Explorer:");
    console.log("├── TokenA: https://explorer.nexus.xyz/address/" + addresses.TokenA);
    console.log("├── TokenB: https://explorer.nexus.xyz/address/" + addresses.TokenB);
    console.log("└── TokenSwap: https://explorer.nexus.xyz/address/" + addresses.TokenSwap);

  } catch (error) {
    console.error("❌ Verification failed:", error.message);
    
    if (error.message.includes("Already Verified")) {
      console.log("ℹ️  Contracts may already be verified");
    } else {
      console.log("\n💡 Troubleshooting tips:");
      console.log("1. Make sure contracts are deployed correctly");
      console.log("2. Check if constructor arguments match");
      console.log("3. Wait a few minutes and try again");
      console.log("4. Verify manually on Nexus Explorer");
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Verification script failed:", error);
    process.exitCode = 1;
  });
