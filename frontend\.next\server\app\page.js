/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cnexus%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnexus%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cnexus%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnexus%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cnexus%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnexus%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5Cnexus%5Cfrontend%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cnexus%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5Cnexus%5Cfrontend%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cnexus%5Cfrontend%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cnexus%5Cfrontend%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q25leHVzJTVDZnJvbnRlbmQlNUNzcmMlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh1cy1zd2FwLWZyb250ZW5kLz8wNzYyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbmV4dXNcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Cnexus%5Cfrontend%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Contract ABIs - Complete for all DeFi operations\nconst TokenA_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) external returns (bool)\",\n    \"function allowance(address owner, address spender) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) external returns (bool)\",\n    \"function transferFrom(address from, address to, uint256 amount) external returns (bool)\"\n];\nconst TokenB_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\"\n];\nconst TokenSwap_ABI = [\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getTokenBLiquidity() external view returns (uint256)\",\n    \"function getTokenABalance() external view returns (uint256)\",\n    \"function tokenA() external view returns (address)\",\n    \"function tokenB() external view returns (address)\",\n    \"event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut)\"\n];\nconst LiquidityPool_ABI = [\n    \"function addLiquidity(uint256 amountA, uint256 amountB) external returns (uint256)\",\n    \"function removeLiquidity(uint256 liquidity) external returns (uint256, uint256)\",\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getReserves() external view returns (uint256, uint256)\",\n    \"function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256)\",\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function name() external view returns (string)\"\n];\nconst Staking_ABI = [\n    \"function stake(uint256 amount) external\",\n    \"function withdraw(uint256 amount) external\",\n    \"function claimReward() external\",\n    \"function exit() external\",\n    \"function compound() external\",\n    \"function multiStake(uint256[] amounts) external\",\n    \"function multiWithdraw(uint256[] amounts) external\",\n    \"function multiClaim(uint256 times) external\",\n    \"function earned(address account) external view returns (uint256)\",\n    \"function balances(address account) external view returns (uint256)\",\n    \"function getStakingInfo(address user) external view returns (uint256, uint256, uint256, uint256)\"\n];\n// Nexus network configuration\nconst NEXUS_NETWORK = {\n    chainId: \"0xF64\",\n    chainName: \"Nexus Testnet III\",\n    nativeCurrency: {\n        name: \"NEX\",\n        symbol: \"NEX\",\n        decimals: 18\n    },\n    rpcUrls: [\n        \"https://testnet3.rpc.nexus.xyz\"\n    ],\n    blockExplorerUrls: [\n        \"https://explorer.nexus.xyz\"\n    ]\n};\n// Default contract addresses (will be replaced with deployed addresses)\nconst DEFAULT_ADDRESSES = {\n    TokenA: \"0x0000000000000000000000000000000000000000\",\n    TokenB: \"0x0000000000000000000000000000000000000000\",\n    TokenSwap: \"0x0000000000000000000000000000000000000000\"\n};\nfunction Home() {\n    // Basic states\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenABalance, setTokenABalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [tokenBBalance, setTokenBBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [lpTokenBalance, setLpTokenBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [stakedBalance, setStakedBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [earnedRewards, setEarnedRewards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_ADDRESSES);\n    const [liquidity, setLiquidity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [isCorrectNetwork, setIsCorrectNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab and form states\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"swap\");\n    const [swapAmount, setSwapAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountA, setLiquidityAmountA] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountB, setLiquidityAmountB] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [stakeAmount, setStakeAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Token selection states\n    const [selectedTokenIn, setSelectedTokenIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenA\");\n    const [selectedTokenOut, setSelectedTokenOut] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenB\");\n    const [availableTokens, setAvailableTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkIfWalletIsConnected();\n        loadContractAddresses();\n        setupEventListeners();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (account && isCorrectNetwork) {\n            updateBalances();\n            updateLiquidity();\n            loadAvailableTokens();\n        }\n    }, [\n        account,\n        contractAddresses,\n        isCorrectNetwork\n    ]);\n    async function loadAvailableTokens() {\n        if (!account || !isCorrectNetwork) return;\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const tokens = [\n                {\n                    symbol: \"TKNA\",\n                    name: \"Token A\",\n                    address: contractAddresses.TokenA,\n                    balance: tokenABalance,\n                    decimals: 18\n                },\n                {\n                    symbol: \"TKNB\",\n                    name: \"Token B\",\n                    address: contractAddresses.TokenB,\n                    balance: tokenBBalance,\n                    decimals: 18\n                }\n            ];\n            // Add LP token if available\n            if (contractAddresses.LiquidityPool) {\n                tokens.push({\n                    symbol: \"NLP\",\n                    name: \"Nexus LP Token\",\n                    address: contractAddresses.LiquidityPool,\n                    balance: lpTokenBalance,\n                    decimals: 18\n                });\n            }\n            setAvailableTokens(tokens);\n        } catch (error) {\n            console.error(\"Error loading available tokens:\", error);\n        }\n    }\n    function setupEventListeners() {\n        const { ethereum } = window;\n        if (!ethereum) return;\n        // Listen for account changes\n        ethereum.on(\"accountsChanged\", (accounts)=>{\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                checkNetwork();\n            } else {\n                setAccount(\"\");\n                setIsCorrectNetwork(false);\n            }\n        });\n        // Listen for network changes\n        ethereum.on(\"chainChanged\", (chainId)=>{\n            console.log(\"Network changed to:\", chainId);\n            checkNetwork();\n            // Reload page to reset state\n            window.location.reload();\n        });\n        // Cleanup function\n        return ()=>{\n            if (ethereum.removeListener) {\n                ethereum.removeListener(\"accountsChanged\", ()=>{});\n                ethereum.removeListener(\"chainChanged\", ()=>{});\n            }\n        };\n    }\n    async function loadContractAddresses() {\n        try {\n            // Try to load deployed addresses from public folder\n            const response = await fetch(\"/deployedAddresses.json\");\n            if (response.ok) {\n                const addresses = await response.json();\n                setContractAddresses(addresses);\n                console.log(\"Loaded contract addresses:\", addresses);\n            } else {\n                console.warn(\"Could not load deployed addresses, using defaults\");\n            }\n        } catch (error) {\n            console.warn(\"Could not load deployed addresses:\", error);\n        }\n    }\n    async function checkIfWalletIsConnected() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_accounts\"\n            });\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                await checkNetwork();\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setError(\"Error saat mengecek koneksi wallet\");\n        }\n    }\n    async function checkNetwork() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setIsCorrectNetwork(false);\n                return;\n            }\n            const chainId = await ethereum.request({\n                method: \"eth_chainId\"\n            });\n            console.log(\"Current chainId:\", chainId, \"Expected:\", NEXUS_NETWORK.chainId);\n            // Convert both to same format for comparison\n            const currentChainId = parseInt(chainId, 16);\n            const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);\n            if (currentChainId === expectedChainId) {\n                setIsCorrectNetwork(true);\n                setError(\"\");\n                console.log(\"✅ Connected to correct network\");\n            } else {\n                setIsCorrectNetwork(false);\n                setError(`Wrong network. Current: ${currentChainId}, Expected: ${expectedChainId} (Nexus Testnet III)`);\n                console.log(\"❌ Wrong network detected\");\n            }\n        } catch (error) {\n            console.error(\"Error checking network:\", error);\n            setIsCorrectNetwork(false);\n            setError(\"Error checking network\");\n        }\n    }\n    async function switchToNexusNetwork() {\n        try {\n            const { ethereum } = window;\n            setError(\"\");\n            setSuccess(\"Switching to Nexus network...\");\n            try {\n                await ethereum.request({\n                    method: \"wallet_switchEthereumChain\",\n                    params: [\n                        {\n                            chainId: NEXUS_NETWORK.chainId\n                        }\n                    ]\n                });\n                console.log(\"✅ Network switch requested\");\n            } catch (switchError) {\n                console.log(\"Switch error code:\", switchError.code);\n                // Network belum ditambahkan, tambahkan dulu\n                if (switchError.code === 4902) {\n                    console.log(\"Adding Nexus network...\");\n                    await ethereum.request({\n                        method: \"wallet_addEthereumChain\",\n                        params: [\n                            NEXUS_NETWORK\n                        ]\n                    });\n                    console.log(\"✅ Network added\");\n                } else {\n                    throw switchError;\n                }\n            }\n            // Wait a bit for network to switch\n            setTimeout(async ()=>{\n                await checkNetwork();\n                setSuccess(\"\");\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error switching network:\", error);\n            setError(\"Gagal switch ke Nexus network: \" + (error.message || \"Unknown error\"));\n            setSuccess(\"\");\n        }\n    }\n    async function connectWallet() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            setAccount(accounts[0]);\n            await checkNetwork();\n            setError(\"\");\n            setSuccess(\"Wallet berhasil terhubung!\");\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n            setError(\"Gagal menghubungkan wallet\");\n        }\n    }\n    async function updateBalances() {\n        if (!account || !isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {\n            console.log(\"Contract addresses not loaded yet, skipping balance update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating all balances for:\", account);\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, provider);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, provider);\n            // Get basic token balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            setTokenABalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceA));\n            setTokenBBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceB));\n            // Get LP token balance if LiquidityPool exists\n            if (contractAddresses.LiquidityPool) {\n                const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, provider);\n                const lpBalance = await lpContract.balanceOf(account);\n                setLpTokenBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(lpBalance));\n            }\n            // Get staking info if Staking exists\n            if (contractAddresses.Staking) {\n                const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, provider);\n                const [stakedBal, earned] = await Promise.all([\n                    stakingContract.balances(account),\n                    stakingContract.earned(account)\n                ]);\n                setStakedBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(stakedBal));\n                setEarnedRewards(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(earned));\n            }\n            console.log(\"All balances updated successfully\");\n        } catch (error) {\n            console.error(\"Error updating balances:\", error);\n            setError(\"Error connecting to contracts\");\n        }\n    }\n    async function updateLiquidity() {\n        if (!isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {\n            console.log(\"TokenSwap address not loaded yet, skipping liquidity update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating liquidity for TokenSwap:\", contractAddresses.TokenSwap);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);\n            try {\n                const liquidityAmount = await swapContract.getTokenBLiquidity();\n                console.log(\"Raw liquidity:\", liquidityAmount.toString());\n                setLiquidity(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(liquidityAmount));\n                console.log(\"Liquidity updated successfully\");\n            } catch (contractError) {\n                console.error(\"TokenSwap contract call error:\", contractError);\n                setError(\"Error reading liquidity. TokenSwap contract may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating liquidity:\", error);\n            setError(\"Error connecting to TokenSwap contract\");\n        }\n    }\n    async function handleSwap() {\n        if (!swapAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            // Determine which tokens to use\n            const isTokenAToB = selectedTokenIn === \"TokenA\";\n            const inputTokenAddress = isTokenAToB ? contractAddresses.TokenA : contractAddresses.TokenB;\n            const inputTokenContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(inputTokenAddress, TokenA_ABI, signer);\n            // Use LiquidityPool for swapping (more advanced than TokenSwap)\n            const swapContract = contractAddresses.LiquidityPool ? new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer) : new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(swapAmount);\n            // Check balance\n            const balance = await inputTokenContract.balanceOf(account);\n            if (balance < amount) {\n                setError(`Saldo ${selectedTokenIn === \"TokenA\" ? \"Token A\" : \"Token B\"} tidak mencukupi`);\n                return;\n            }\n            // Check allowance\n            const swapAddress = contractAddresses.LiquidityPool || contractAddresses.TokenSwap;\n            const allowance = await inputTokenContract.allowance(account, swapAddress);\n            if (allowance < amount) {\n                setSuccess(`Menyetujui penggunaan ${selectedTokenIn === \"TokenA\" ? \"Token A\" : \"Token B\"}...`);\n                const approveTx = await inputTokenContract.approve(swapAddress, amount);\n                await approveTx.wait();\n                setSuccess(\"Approval berhasil! Melakukan swap...\");\n            } else {\n                setSuccess(\"Melakukan swap...\");\n            }\n            // Perform swap\n            if (contractAddresses.LiquidityPool) {\n                // Use LiquidityPool swap function\n                const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);\n                await swapTx.wait();\n            } else {\n                // Use old TokenSwap (only supports TokenA -> TokenB)\n                if (!isTokenAToB) {\n                    setError(\"TokenSwap hanya mendukung Token A → Token B\");\n                    return;\n                }\n                const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);\n                await swapTx.wait();\n            }\n            setSuccess(`Swap ${selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\"} → ${selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\"} berhasil! 🎉`);\n            setSwapAmount(\"\");\n            // Update balances\n            await updateBalances();\n            await updateLiquidity();\n        } catch (error) {\n            console.error(\"Error during swap:\", error);\n            if (error.code === \"ACTION_REJECTED\") {\n                setError(\"Transaksi dibatalkan oleh user\");\n            } else if (error.message.includes(\"insufficient funds\")) {\n                setError(\"Saldo NEX tidak mencukupi untuk gas fee\");\n            } else {\n                setError(\"Swap gagal: \" + (error.reason || error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleAddLiquidity() {\n        if (!liquidityAmountA || !liquidityAmountB || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const amountA = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountA);\n            const amountB = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountB);\n            // Check balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            if (balanceA < amountA) {\n                setError(\"Saldo Token A tidak mencukupi\");\n                return;\n            }\n            if (balanceB < amountB) {\n                setError(\"Saldo Token B tidak mencukupi\");\n                return;\n            }\n            // Approve tokens\n            setSuccess(\"Menyetujui Token A...\");\n            const approveATx = await tokenAContract.approve(contractAddresses.LiquidityPool, amountA);\n            await approveATx.wait();\n            setSuccess(\"Menyetujui Token B...\");\n            const approveBTx = await tokenBContract.approve(contractAddresses.LiquidityPool, amountB);\n            await approveBTx.wait();\n            // Add liquidity\n            setSuccess(\"Menambahkan likuiditas...\");\n            const addLiquidityTx = await lpContract.addLiquidity(amountA, amountB);\n            await addLiquidityTx.wait();\n            setSuccess(\"Likuiditas berhasil ditambahkan! \\uD83C\\uDF89\");\n            setLiquidityAmountA(\"\");\n            setLiquidityAmountB(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error adding liquidity:\", error);\n            setError(\"Gagal menambahkan likuiditas: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleStake() {\n        if (!stakeAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(stakeAmount);\n            // Check LP balance\n            const lpBalance = await lpContract.balanceOf(account);\n            if (lpBalance < amount) {\n                setError(\"Saldo LP Token tidak mencukupi\");\n                return;\n            }\n            // Approve LP tokens\n            setSuccess(\"Menyetujui LP Tokens...\");\n            const approveTx = await lpContract.approve(contractAddresses.Staking, amount);\n            await approveTx.wait();\n            // Stake\n            setSuccess(\"Melakukan stake...\");\n            const stakeTx = await stakingContract.stake(amount);\n            await stakeTx.wait();\n            setSuccess(\"Stake berhasil! \\uD83C\\uDF89\");\n            setStakeAmount(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error staking:\", error);\n            setError(\"Gagal melakukan stake: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleClaim() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            setSuccess(\"Mengklaim rewards...\");\n            const claimTx = await stakingContract.claimReward();\n            await claimTx.wait();\n            setSuccess(\"Rewards berhasil diklaim! \\uD83C\\uDF89\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error claiming:\", error);\n            setError(\"Gagal mengklaim rewards: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    const formatAddress = (address)=>{\n        return `${address.slice(0, 6)}...${address.slice(-4)}`;\n    };\n    const clearMessages = ()=>{\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const isDeployerAccount = account && contractAddresses.deployer && account.toLowerCase() === contractAddresses.deployer.toLowerCase();\n    async function requestTestTokens() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"Requesting test tokens...\");\n        try {\n            // Check if user is the deployer\n            if (isDeployerAccount) {\n                setSuccess(\"You are the deployer! You already have all tokens \\uD83C\\uDF89\");\n                await updateBalances();\n                return;\n            }\n            // For non-deployer users, show instructions\n            setError(\"\");\n            setSuccess(\"\");\n            alert(`To get test tokens:\n\n1. Switch to deployer account: ${contractAddresses.deployer}\n2. Or ask the deployer to send you tokens\n3. Or use a faucet if available\n\nYour current address: ${account}\nDeployer address: ${contractAddresses.deployer}\n\nThe deployer has 1,000,000 Token A available for distribution.`);\n        } catch (error) {\n            console.error(\"Error requesting test tokens:\", error);\n            setError(\"Failed to get test tokens: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Batch Operations untuk Maximum Transactions\n    async function handleBatchSwaps() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            // Multiple small swaps untuk generate banyak transaksi\n            const amounts = [\n                \"10\",\n                \"20\",\n                \"30\",\n                \"40\",\n                \"50\"\n            ]; // 5 transaksi swap\n            let totalAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"0\");\n            for (const amount of amounts){\n                totalAmount += ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amount);\n            }\n            // Check balance\n            const balance = await tokenAContract.balanceOf(account);\n            if (balance < totalAmount) {\n                setError(\"Saldo Token A tidak mencukupi untuk batch swaps\");\n                return;\n            }\n            // Approve total amount\n            setSuccess(\"Menyetujui total amount untuk batch swaps...\");\n            const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, totalAmount);\n            await approveTx.wait();\n            // Execute multiple swaps\n            for(let i = 0; i < amounts.length; i++){\n                setSuccess(`Melakukan swap ${i + 1}/${amounts.length}...`);\n                const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amounts[i]);\n                const swapTx = await swapContract.swap(contractAddresses.TokenA, amount, 0);\n                await swapTx.wait();\n                // Small delay between transactions\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            setSuccess(`Batch swaps berhasil! ${amounts.length} transaksi completed 🎉`);\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error during batch swaps:\", error);\n            setError(\"Batch swaps gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleTransactionSpam() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            // Generate many approval transactions\n            const spamAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"1\");\n            const contracts = [\n                contractAddresses.TokenSwap,\n                contractAddresses.LiquidityPool,\n                contractAddresses.Staking\n            ];\n            let transactionCount = 0;\n            for (const contractAddr of contracts){\n                // Multiple approvals for each contract\n                for(let i = 0; i < 3; i++){\n                    setSuccess(`Generating approval transaction ${transactionCount + 1}...`);\n                    const approveTx = await tokenAContract.approve(contractAddr, spamAmount);\n                    await approveTx.wait();\n                    transactionCount++;\n                    // Also approve TokenB\n                    const approveBTx = await tokenBContract.approve(contractAddr, spamAmount);\n                    await approveBTx.wait();\n                    transactionCount++;\n                    // Small delay\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                }\n            }\n            setSuccess(`Transaction spam completed! ${transactionCount} transactions generated 🚀`);\n        } catch (error) {\n            console.error(\"Error during transaction spam:\", error);\n            setError(\"Transaction spam gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 relative overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-10 w-32 h-32 bg-cyan-400 transform rotate-12\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 804,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-24 h-24 bg-pink-500 transform -rotate-12\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 805,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 left-1/4 w-28 h-28 bg-yellow-400 transform rotate-45\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 806,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-1/3 w-20 h-20 bg-green-400 transform -rotate-45\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 807,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 803,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10 flex flex-col items-end space-y-3\",\n                children: [\n                    account && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-cyan-400 text-black px-4 py-2 border-4 border-black font-black text-sm shadow-[4px_4px_0px_0px_theme(colors.pink.500)] transform -rotate-1\",\n                        children: [\n                            \"\\uD83D\\uDD17 \",\n                            formatAddress(account)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 812,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `px-4 py-2 border-4 border-black font-black text-sm shadow-[4px_4px_0px_0px_theme(colors.yellow.400)] transform rotate-1 ${isCorrectNetwork ? \"bg-green-400 text-black\" : \"bg-red-500 text-white animate-pulse\"}`,\n                        children: isCorrectNetwork ? \"✅ NEXUS TESTNET\" : \"⚠️ WRONG NETWORK!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 text-cyan-400 px-3 py-2 border-4 border-cyan-400 font-mono text-xs max-w-xs shadow-[4px_4px_0px_0px_theme(colors.gray.600)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"\\uD83D\\uDD17 Chain: 3940\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCC4 Contracts: \",\n                                    contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? \"✅\" : \"❌\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 825,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDC64 You: \",\n                                    account ? account.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 823,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 810,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-screen px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8 sm:mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative inline-block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl sm:text-6xl lg:text-7xl font-black mb-4 text-white transform -rotate-1\",\n                                        children: \"NEXUS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 bg-yellow-400 text-black px-3 py-1 border-4 border-black font-black text-lg sm:text-xl transform rotate-12 shadow-[4px_4px_0px_0px_theme(colors.pink.500)]\",\n                                        children: \"SWAP\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-cyan-400 text-black px-6 py-3 border-4 border-black font-black text-sm sm:text-lg max-w-2xl mx-auto mt-6 shadow-[8px_8px_0px_0px_theme(colors.purple.500)] transform rotate-1\",\n                                children: \"\\uD83D\\uDE80 DEFI ON NEXUS BLOCKCHAIN • MAXIMUM TRANSACTIONS • AIRDROP READY \\uD83C\\uDFAF\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 833,\n                        columnNumber: 9\n                    }, this),\n                    (error || success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mb-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-500 text-white px-6 py-4 border-4 border-black font-black text-sm mb-3 shadow-[8px_8px_0px_0px_theme(colors.yellow.400)] transform -rotate-1 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"❌ \",\n                                            error\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"bg-white text-red-500 px-2 py-1 border-2 border-black font-black hover:bg-yellow-400 hover:text-black transition-colors\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 853,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-400 text-black px-6 py-4 border-4 border-black font-black text-sm mb-3 shadow-[8px_8px_0px_0px_theme(colors.pink.500)] transform rotate-1 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"✅ \",\n                                            success\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"bg-white text-green-600 px-2 py-1 border-2 border-black font-black hover:bg-cyan-400 hover:text-black transition-colors\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 849,\n                        columnNumber: 11\n                    }, this),\n                    !account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: connectWallet,\n                                className: \"bg-yellow-400 text-black px-8 py-4 border-4 border-black font-black text-xl shadow-[8px_8px_0px_0px_theme(colors.cyan.400)] hover:shadow-[12px_12px_0px_0px_theme(colors.cyan.400)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200\",\n                                children: \"\\uD83D\\uDD17 CONNECT WALLET\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-pink-500 text-white px-6 py-3 border-4 border-black font-black text-sm mt-6 shadow-[4px_4px_0px_0px_theme(colors.purple.500)] transform -rotate-1 max-w-md mx-auto\",\n                                children: \"\\uD83D\\uDCAB CONNECT TO START DEFI JOURNEY ON NEXUS \\uD83D\\uDE80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 878,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 870,\n                        columnNumber: 11\n                    }, this) : !isCorrectNetwork ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: switchToNexusNetwork,\n                                        className: \"bg-orange-500 text-white px-8 py-4 border-4 border-black font-black text-xl shadow-[8px_8px_0px_0px_theme(colors.red.500)] hover:shadow-[12px_12px_0px_0px_theme(colors.red.500)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200 animate-pulse\",\n                                        children: \"\\uD83D\\uDD04 SWITCH TO NEXUS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: checkNetwork,\n                                            className: \"text-sm text-blue-600 hover:text-blue-800 underline\",\n                                            children: \"Already switched? Click to refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 893,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Please switch to Nexus Testnet III to continue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 883,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-6xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border-4 border-white p-6 mb-8 shadow-[12px_12px_0px_0px_theme(colors.cyan.400)] transform -rotate-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-400 text-black px-4 py-2 border-4 border-black font-black text-xl mb-6 shadow-[4px_4px_0px_0px_theme(colors.pink.500)] transform rotate-1 inline-block\",\n                                        children: \"\\uD83D\\uDCB0 PORTFOLIO OVERVIEW\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 910,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-cyan-400 text-black p-4 border-4 border-black shadow-[6px_6px_0px_0px_theme(colors.blue.600)] transform rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-sm mb-1\",\n                                                        children: \"\\uD83D\\uDD35 TOKEN A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-2xl font-black\",\n                                                        children: parseFloat(tokenABalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs\",\n                                                        children: \"TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-pink-400 text-black p-4 border-4 border-black shadow-[6px_6px_0px_0px_theme(colors.purple.600)] transform -rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-sm mb-1\",\n                                                        children: \"\\uD83D\\uDFE3 TOKEN B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-2xl font-black\",\n                                                        children: parseFloat(tokenBBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs\",\n                                                        children: \"TKNB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-400 text-black p-4 border-4 border-black shadow-[6px_6px_0px_0px_theme(colors.emerald.600)] transform rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-sm mb-1\",\n                                                        children: \"\\uD83D\\uDCA7 LP TOKENS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-2xl font-black\",\n                                                        children: parseFloat(lpTokenBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 930,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs\",\n                                                        children: \"NLP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 933,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-400 text-black p-4 border-4 border-black shadow-[6px_6px_0px_0px_theme(colors.red.600)] transform -rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-sm mb-1\",\n                                                        children: \"\\uD83C\\uDFE6 STAKED\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 936,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-2xl font-black\",\n                                                        children: parseFloat(stakedBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs\",\n                                                        children: \"NLP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 935,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(earnedRewards) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 bg-yellow-400 text-black p-4 border-4 border-black shadow-[6px_6px_0px_0px_theme(colors.orange.500)] transform rotate-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-black text-sm\",\n                                                children: \"\\uD83C\\uDF81 PENDING REWARDS\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-mono text-3xl font-black\",\n                                                children: [\n                                                    parseFloat(earnedRewards).toFixed(4),\n                                                    \" TKNB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 909,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border-4 border-white shadow-[12px_12px_0px_0px_theme(colors.pink.500)] transform rotate-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap border-b-4 border-white\",\n                                        children: [\n                                            {\n                                                id: \"swap\",\n                                                label: \"SWAP\",\n                                                icon: \"\\uD83D\\uDD04\",\n                                                color: \"bg-cyan-400\"\n                                            },\n                                            {\n                                                id: \"liquidity\",\n                                                label: \"LIQUIDITY\",\n                                                icon: \"\\uD83D\\uDCA7\",\n                                                color: \"bg-blue-400\"\n                                            },\n                                            {\n                                                id: \"stake\",\n                                                label: \"STAKE\",\n                                                icon: \"\\uD83C\\uDFE6\",\n                                                color: \"bg-green-400\"\n                                            },\n                                            {\n                                                id: \"claim\",\n                                                label: \"CLAIM\",\n                                                icon: \"\\uD83C\\uDF81\",\n                                                color: \"bg-yellow-400\"\n                                            },\n                                            {\n                                                id: \"batch\",\n                                                label: \"BATCH\",\n                                                icon: \"⚡\",\n                                                color: \"bg-pink-400\"\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: `flex-1 min-w-0 px-2 sm:px-4 py-3 border-r-4 border-white last:border-r-0 font-black text-xs sm:text-sm transition-all transform hover:scale-105 ${activeTab === tab.id ? `${tab.color} text-black shadow-[inset_4px_4px_0px_0px_theme(colors.gray.800)]` : \"bg-gray-700 text-white hover:bg-gray-600\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg sm:mr-2\",\n                                                            children: tab.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden sm:inline\",\n                                                            children: tab.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 973,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, tab.id, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 955,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Swap Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-black text-white mb-2\",\n                                                                        children: \"\\uD83D\\uDCE4 FROM TOKEN\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 991,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: selectedTokenIn,\n                                                                                onChange: (e)=>setSelectedTokenIn(e.target.value),\n                                                                                className: \"flex-1 bg-cyan-400 text-black border-4 border-black px-4 py-3 font-black focus:outline-none focus:shadow-[4px_4px_0px_0px_theme(colors.pink.500)] transition-shadow\",\n                                                                                disabled: loading,\n                                                                                title: \"Select token to swap from\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenA\",\n                                                                                        children: \"\\uD83D\\uDD35 TKNA - Token A\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1000,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenB\",\n                                                                                        children: \"\\uD83D\\uDFE3 TKNB - Token B\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1001,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 993,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-yellow-400 text-black px-4 py-3 border-4 border-black font-black text-sm shadow-[4px_4px_0px_0px_theme(colors.purple.500)]\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCB0 \",\n                                                                                    selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1003,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 992,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 990,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>{\n                                                                        const temp = selectedTokenIn;\n                                                                        setSelectedTokenIn(selectedTokenOut);\n                                                                        setSelectedTokenOut(temp);\n                                                                    },\n                                                                    className: \"bg-pink-500 text-white p-4 border-4 border-black font-black text-2xl shadow-[6px_6px_0px_0px_theme(colors.yellow.400)] hover:shadow-[8px_8px_0px_0px_theme(colors.yellow.400)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200 rotate-45 hover:rotate-0\",\n                                                                    disabled: loading,\n                                                                    children: \"\\uD83D\\uDD04\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1011,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1010,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-black text-white mb-2\",\n                                                                        children: \"\\uD83D\\uDCE5 TO TOKEN\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1027,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: selectedTokenOut,\n                                                                                onChange: (e)=>setSelectedTokenOut(e.target.value),\n                                                                                className: \"flex-1 bg-pink-400 text-black border-4 border-black px-4 py-3 font-black focus:outline-none focus:shadow-[4px_4px_0px_0px_theme(colors.cyan.500)] transition-shadow\",\n                                                                                disabled: loading,\n                                                                                title: \"Select token to swap to\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenB\",\n                                                                                        children: \"\\uD83D\\uDFE3 TKNB - Token B\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1036,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenA\",\n                                                                                        children: \"\\uD83D\\uDD35 TKNA - Token A\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1037,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1029,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-green-400 text-black px-4 py-3 border-4 border-black font-black text-sm shadow-[4px_4px_0px_0px_theme(colors.blue.500)]\",\n                                                                                children: [\n                                                                                    \"\\uD83D\\uDCB0 \",\n                                                                                    selectedTokenOut === \"TokenA\" ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1039,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1028,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1026,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Amount to Swap\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1048,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: swapAmount,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Swap input changed:\", e.target.value);\n                                                                            setSwapAmount(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-4 pr-20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400 shadow-sm\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1052,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                        children: selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1066,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1051,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Exchange rate: 1 \",\n                                                                            selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\",\n                                                                            \" ≈ 1 \",\n                                                                            selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1071,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(\"10\"),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading,\n                                                                                children: \"10\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1075,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(\"100\"),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading,\n                                                                                children: \"100\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1083,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(selectedTokenIn === \"TokenA\" ? tokenABalance : tokenBBalance),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading || (selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance) === 0 : parseFloat(tokenBBalance) === 0),\n                                                                                children: \"Max\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1091,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1074,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1070,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    \"Available: \",\n                                                                    selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4),\n                                                                    \" \",\n                                                                    selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\",\n                                                                    ' | Input: \"',\n                                                                    swapAmount,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1101,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Pool Liquidity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1108,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-mono text-lg font-semibold text-gray-800\",\n                                                                children: [\n                                                                    parseFloat(liquidity).toFixed(2),\n                                                                    \" Token B\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1109,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1107,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleSwap,\n                                                        disabled: loading || !swapAmount || parseFloat(swapAmount) <= 0,\n                                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1123,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Processing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1122,\n                                                            columnNumber: 25\n                                                        }, this) : `Swap ${selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\"} → ${selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1115,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 984,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"liquidity\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token A Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1139,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: liquidityAmountA,\n                                                                                onChange: (e)=>{\n                                                                                    console.log(\"Liquidity A input changed:\", e.target.value);\n                                                                                    setLiquidityAmountA(e.target.value);\n                                                                                },\n                                                                                placeholder: \"0.0\",\n                                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                                disabled: loading,\n                                                                                min: \"0\",\n                                                                                step: \"0.01\",\n                                                                                autoComplete: \"off\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1143,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                                children: \"TKNA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1157,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1142,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"Available: \",\n                                                                                    parseFloat(tokenABalance).toFixed(4),\n                                                                                    \" TKNA\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1162,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(\"10\"),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"10\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1166,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(\"50\"),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"50\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1174,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(tokenABalance),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading || parseFloat(tokenABalance) === 0,\n                                                                                        children: \"Max\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1182,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1165,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1161,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            'Input: \"',\n                                                                            liquidityAmountA,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1192,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1138,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token B Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1198,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: liquidityAmountB,\n                                                                                onChange: (e)=>{\n                                                                                    console.log(\"Liquidity B input changed:\", e.target.value);\n                                                                                    setLiquidityAmountB(e.target.value);\n                                                                                },\n                                                                                placeholder: \"0.0\",\n                                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                                disabled: loading,\n                                                                                min: \"0\",\n                                                                                step: \"0.01\",\n                                                                                autoComplete: \"off\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1202,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                                children: \"TKNB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1216,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1201,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"Available: \",\n                                                                                    parseFloat(tokenBBalance).toFixed(4),\n                                                                                    \" TKNB\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1221,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(\"10\"),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"10\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1225,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(\"50\"),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"50\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1233,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(tokenBBalance),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading || parseFloat(tokenBBalance) === 0,\n                                                                                        children: \"Max\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1241,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1224,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1220,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            'Input: \"',\n                                                                            liquidityAmountB,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1251,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1197,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1137,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleAddLiquidity,\n                                                        disabled: loading || !liquidityAmountA || !liquidityAmountB,\n                                                        className: \"w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1257,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1134,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"stake\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1270,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Stake Amount (LP Tokens)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1273,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: stakeAmount,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Stake input changed:\", e.target.value);\n                                                                            setStakeAmount(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1277,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1291,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Available: \",\n                                                                            parseFloat(lpTokenBalance).toFixed(4),\n                                                                            \" NLP\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1296,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setStakeAmount(\"1\"),\n                                                                                className: \"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200\",\n                                                                                disabled: loading,\n                                                                                children: \"1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1300,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setStakeAmount(\"5\"),\n                                                                                className: \"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200\",\n                                                                                disabled: loading,\n                                                                                children: \"5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1308,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setStakeAmount(lpTokenBalance),\n                                                                                className: \"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200\",\n                                                                                disabled: loading || parseFloat(lpTokenBalance) === 0,\n                                                                                children: \"Max\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1316,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1299,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1295,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: [\n                                                                    'Input: \"',\n                                                                    stakeAmount,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1326,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1272,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleStake,\n                                                        disabled: loading || !stakeAmount || parseFloat(lpTokenBalance) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-orange-600 to-red-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1331,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1269,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"claim\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1344,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6 p-4 bg-yellow-50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mb-2\",\n                                                                children: \"Pending Rewards\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1347,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-mono text-3xl font-bold text-yellow-600\",\n                                                                children: parseFloat(earnedRewards).toFixed(6)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1348,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"TKNB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1351,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Staked\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1356,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold\",\n                                                                        children: parseFloat(stakedBalance).toFixed(4)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1357,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1360,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1355,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"APY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1363,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold text-green-600\",\n                                                                        children: \"~50%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1364,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"Estimated\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1367,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1362,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1354,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleClaim,\n                                                        disabled: loading || parseFloat(earnedRewards) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-yellow-600 to-orange-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-yellow-700 hover:to-orange-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1371,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1343,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"batch\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Batch Operations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1384,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-6\",\n                                                        children: \"Generate multiple transactions for maximum Nexus testnet interaction!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1385,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-blue-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-blue-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDD04 Batch Swaps\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1392,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-blue-700 mb-3\",\n                                                                        children: \"Execute 5 separate swap transactions (10, 20, 30, 40, 50 TKNA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1393,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleBatchSwaps,\n                                                                        disabled: loading || parseFloat(tokenABalance) < 150,\n                                                                        className: \"w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Execute Batch Swaps (5 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1396,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 mt-1\",\n                                                                        children: \"Requires: 150 TKNA minimum\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1404,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1391,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-purple-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-purple-800 mb-2\",\n                                                                        children: \"⚡ Transaction Spam\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1411,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-purple-700 mb-3\",\n                                                                        children: \"Generate 18 approval transactions across all contracts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1412,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleTransactionSpam,\n                                                                        disabled: loading,\n                                                                        className: \"w-full bg-purple-600 text-white py-3 rounded-lg font-medium hover:bg-purple-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Generate Transaction Spam (18 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1415,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-purple-600 mt-1\",\n                                                                        children: \"Generates many approval transactions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1423,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1410,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-green-50 rounded-xl text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-green-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCCA Transaction Counter\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1430,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-700 mb-2\",\n                                                                        children: \"Estimated transactions per full cycle:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1431,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"Basic Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1436,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"8-10 TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1437,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1435,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"With Batches\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1440,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"25+ TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1441,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1439,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1434,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1429,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-yellow-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCA1 Pro Tips\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1448,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-sm text-yellow-700 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Use batch operations to generate many transactions quickly\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1450,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Each operation creates multiple blockchain interactions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1451,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Perfect for maximizing Nexus testnet contribution\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1452,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Monitor your transaction count in MetaMask\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1453,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1449,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1447,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1383,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 982,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(tokenABalance) === 0 && activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-6 mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-yellow-800 mb-2\",\n                                                children: \"Need Test Tokens?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1464,\n                                                columnNumber: 19\n                                            }, this),\n                                            isDeployerAccount ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"You are the deployer! You have 1,000,000 Token A available.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1467,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: updateBalances,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200\",\n                                                        children: \"Refresh Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1470,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1466,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"To get test tokens, switch to the deployer account or ask for a transfer:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1480,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-yellow-600 mb-3 font-mono bg-yellow-100 p-2 rounded\",\n                                                        children: [\n                                                            \"Deployer: \",\n                                                            contractAddresses.deployer?.slice(0, 20),\n                                                            \"...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1483,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: requestTestTokens,\n                                                        disabled: loading,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                        children: \"Show Instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1486,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1479,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1463,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 954,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"DeFi Flow Guide:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1502,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"1. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Swap\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1504,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Trade Token A ⇄ Token B\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1504,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"2. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1505,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Provide both tokens → Get LP tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1505,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"3. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Stake\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1506,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Stake LP tokens → Earn rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1506,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"4. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Claim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1507,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Collect your earned rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1507,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"5. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Repeat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1508,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": More transactions = More Nexus interaction!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1508,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1503,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1501,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 907,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 831,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 801,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9974e75019d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dXMtc3dhcC1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YjhkZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjk5NzRlNzUwMTlkOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Nexus Swap - DeFi Token Exchange\",\n    description: \"Decentralized token swap application on Nexus blockchain\",\n    keywords: [\n        \"DeFi\",\n        \"Nexus\",\n        \"Token Swap\",\n        \"Blockchain\",\n        \"Cryptocurrency\"\n    ],\n    authors: [\n        {\n            name: \"Nexus Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\nexus\frontend\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cnexus%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cnexus%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();