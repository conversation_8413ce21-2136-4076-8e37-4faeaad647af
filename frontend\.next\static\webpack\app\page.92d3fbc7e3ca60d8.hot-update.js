"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Contract ABIs - Complete for all DeFi operations\nconst TokenA_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) external returns (bool)\",\n    \"function allowance(address owner, address spender) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) external returns (bool)\",\n    \"function transferFrom(address from, address to, uint256 amount) external returns (bool)\"\n];\nconst TokenB_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\"\n];\nconst TokenSwap_ABI = [\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getTokenBLiquidity() external view returns (uint256)\",\n    \"function getTokenABalance() external view returns (uint256)\",\n    \"function tokenA() external view returns (address)\",\n    \"function tokenB() external view returns (address)\",\n    \"event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut)\"\n];\nconst LiquidityPool_ABI = [\n    \"function addLiquidity(uint256 amountA, uint256 amountB) external returns (uint256)\",\n    \"function removeLiquidity(uint256 liquidity) external returns (uint256, uint256)\",\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getReserves() external view returns (uint256, uint256)\",\n    \"function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256)\",\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function name() external view returns (string)\"\n];\nconst Staking_ABI = [\n    \"function stake(uint256 amount) external\",\n    \"function withdraw(uint256 amount) external\",\n    \"function claimReward() external\",\n    \"function exit() external\",\n    \"function compound() external\",\n    \"function multiStake(uint256[] amounts) external\",\n    \"function multiWithdraw(uint256[] amounts) external\",\n    \"function multiClaim(uint256 times) external\",\n    \"function earned(address account) external view returns (uint256)\",\n    \"function balances(address account) external view returns (uint256)\",\n    \"function getStakingInfo(address user) external view returns (uint256, uint256, uint256, uint256)\"\n];\n// Nexus network configuration\nconst NEXUS_NETWORK = {\n    chainId: \"0xF64\",\n    chainName: \"Nexus Testnet III\",\n    nativeCurrency: {\n        name: \"NEX\",\n        symbol: \"NEX\",\n        decimals: 18\n    },\n    rpcUrls: [\n        \"https://testnet3.rpc.nexus.xyz\"\n    ],\n    blockExplorerUrls: [\n        \"https://explorer.nexus.xyz\"\n    ]\n};\n// Default contract addresses (will be replaced with deployed addresses)\nconst DEFAULT_ADDRESSES = {\n    TokenA: \"0x83FD4F92926AC4FB7a0b510aD5615Fe76e588cF8\",\n    TokenB: \"0x67bae4B1E5528Fb6C92E7E0BC243341dc71330db\",\n    TokenSwap: \"0xf18529580694Ff80e836132875a52505124358EC\"\n};\nfunction Home() {\n    var _contractAddresses_deployer;\n    _s();\n    // Basic states\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenABalance, setTokenABalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [tokenBBalance, setTokenBBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [lpTokenBalance, setLpTokenBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [stakedBalance, setStakedBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [earnedRewards, setEarnedRewards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_ADDRESSES);\n    const [liquidity, setLiquidity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [isCorrectNetwork, setIsCorrectNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab and form states\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"swap\");\n    const [swapAmount, setSwapAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountA, setLiquidityAmountA] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountB, setLiquidityAmountB] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [stakeAmount, setStakeAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Token selection states\n    const [selectedTokenIn, setSelectedTokenIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenA\");\n    const [selectedTokenOut, setSelectedTokenOut] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenB\");\n    const [availableTokens, setAvailableTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkIfWalletIsConnected();\n        loadContractAddresses();\n        setupEventListeners();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (account && isCorrectNetwork) {\n            updateBalances();\n            updateLiquidity();\n            loadAvailableTokens();\n        }\n    }, [\n        account,\n        contractAddresses,\n        isCorrectNetwork\n    ]);\n    async function loadAvailableTokens() {\n        if (!account || !isCorrectNetwork) return;\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const tokens = [\n                {\n                    symbol: \"TKNA\",\n                    name: \"Token A\",\n                    address: contractAddresses.TokenA,\n                    balance: tokenABalance,\n                    decimals: 18\n                },\n                {\n                    symbol: \"TKNB\",\n                    name: \"Token B\",\n                    address: contractAddresses.TokenB,\n                    balance: tokenBBalance,\n                    decimals: 18\n                }\n            ];\n            // Add LP token if available\n            if (contractAddresses.LiquidityPool) {\n                tokens.push({\n                    symbol: \"NLP\",\n                    name: \"Nexus LP Token\",\n                    address: contractAddresses.LiquidityPool,\n                    balance: lpTokenBalance,\n                    decimals: 18\n                });\n            }\n            setAvailableTokens(tokens);\n        } catch (error) {\n            console.error(\"Error loading available tokens:\", error);\n        }\n    }\n    function setupEventListeners() {\n        const { ethereum } = window;\n        if (!ethereum) return;\n        // Listen for account changes\n        ethereum.on(\"accountsChanged\", (accounts)=>{\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                checkNetwork();\n            } else {\n                setAccount(\"\");\n                setIsCorrectNetwork(false);\n            }\n        });\n        // Listen for network changes\n        ethereum.on(\"chainChanged\", (chainId)=>{\n            console.log(\"Network changed to:\", chainId);\n            checkNetwork();\n            // Reload page to reset state\n            window.location.reload();\n        });\n        // Cleanup function\n        return ()=>{\n            if (ethereum.removeListener) {\n                ethereum.removeListener(\"accountsChanged\", ()=>{});\n                ethereum.removeListener(\"chainChanged\", ()=>{});\n            }\n        };\n    }\n    async function loadContractAddresses() {\n        try {\n            // Try to load deployed addresses from public folder\n            const response = await fetch(\"/deployedAddresses.json\");\n            if (response.ok) {\n                const addresses = await response.json();\n                setContractAddresses(addresses);\n                console.log(\"Loaded contract addresses:\", addresses);\n            } else {\n                console.warn(\"Could not load deployed addresses, using defaults\");\n            }\n        } catch (error) {\n            console.warn(\"Could not load deployed addresses:\", error);\n        }\n    }\n    async function checkIfWalletIsConnected() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_accounts\"\n            });\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                await checkNetwork();\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setError(\"Error saat mengecek koneksi wallet\");\n        }\n    }\n    async function checkNetwork() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setIsCorrectNetwork(false);\n                return;\n            }\n            const chainId = await ethereum.request({\n                method: \"eth_chainId\"\n            });\n            console.log(\"Current chainId:\", chainId, \"Expected:\", NEXUS_NETWORK.chainId);\n            // Convert both to same format for comparison\n            const currentChainId = parseInt(chainId, 16);\n            const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);\n            if (currentChainId === expectedChainId) {\n                setIsCorrectNetwork(true);\n                setError(\"\");\n                console.log(\"✅ Connected to correct network\");\n            } else {\n                setIsCorrectNetwork(false);\n                setError(\"Wrong network. Current: \".concat(currentChainId, \", Expected: \").concat(expectedChainId, \" (Nexus Testnet III)\"));\n                console.log(\"❌ Wrong network detected\");\n            }\n        } catch (error) {\n            console.error(\"Error checking network:\", error);\n            setIsCorrectNetwork(false);\n            setError(\"Error checking network\");\n        }\n    }\n    async function switchToNexusNetwork() {\n        try {\n            const { ethereum } = window;\n            setError(\"\");\n            setSuccess(\"Switching to Nexus network...\");\n            try {\n                await ethereum.request({\n                    method: \"wallet_switchEthereumChain\",\n                    params: [\n                        {\n                            chainId: NEXUS_NETWORK.chainId\n                        }\n                    ]\n                });\n                console.log(\"✅ Network switch requested\");\n            } catch (switchError) {\n                console.log(\"Switch error code:\", switchError.code);\n                // Network belum ditambahkan, tambahkan dulu\n                if (switchError.code === 4902) {\n                    console.log(\"Adding Nexus network...\");\n                    await ethereum.request({\n                        method: \"wallet_addEthereumChain\",\n                        params: [\n                            NEXUS_NETWORK\n                        ]\n                    });\n                    console.log(\"✅ Network added\");\n                } else {\n                    throw switchError;\n                }\n            }\n            // Wait a bit for network to switch\n            setTimeout(async ()=>{\n                await checkNetwork();\n                setSuccess(\"\");\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error switching network:\", error);\n            setError(\"Gagal switch ke Nexus network: \" + (error.message || \"Unknown error\"));\n            setSuccess(\"\");\n        }\n    }\n    async function connectWallet() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            setAccount(accounts[0]);\n            await checkNetwork();\n            setError(\"\");\n            setSuccess(\"Wallet berhasil terhubung!\");\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n            setError(\"Gagal menghubungkan wallet\");\n        }\n    }\n    async function updateBalances() {\n        if (!account || !isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {\n            console.log(\"Contract addresses not loaded yet, skipping balance update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating all balances for:\", account);\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, provider);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, provider);\n            // Get basic token balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            setTokenABalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceA));\n            setTokenBBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceB));\n            // Get LP token balance if LiquidityPool exists\n            if (contractAddresses.LiquidityPool) {\n                const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, provider);\n                const lpBalance = await lpContract.balanceOf(account);\n                setLpTokenBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(lpBalance));\n            }\n            // Get staking info if Staking exists\n            if (contractAddresses.Staking) {\n                const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, provider);\n                const [stakedBal, earned] = await Promise.all([\n                    stakingContract.balances(account),\n                    stakingContract.earned(account)\n                ]);\n                setStakedBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(stakedBal));\n                setEarnedRewards(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(earned));\n            }\n            console.log(\"All balances updated successfully\");\n        } catch (error) {\n            console.error(\"Error updating balances:\", error);\n            setError(\"Error connecting to contracts\");\n        }\n    }\n    async function updateLiquidity() {\n        if (!isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {\n            console.log(\"TokenSwap address not loaded yet, skipping liquidity update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating liquidity for TokenSwap:\", contractAddresses.TokenSwap);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);\n            try {\n                const liquidityAmount = await swapContract.getTokenBLiquidity();\n                console.log(\"Raw liquidity:\", liquidityAmount.toString());\n                setLiquidity(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(liquidityAmount));\n                console.log(\"Liquidity updated successfully\");\n            } catch (contractError) {\n                console.error(\"TokenSwap contract call error:\", contractError);\n                setError(\"Error reading liquidity. TokenSwap contract may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating liquidity:\", error);\n            setError(\"Error connecting to TokenSwap contract\");\n        }\n    }\n    async function handleSwap() {\n        if (!swapAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            // Determine which tokens to use\n            const isTokenAToB = selectedTokenIn === \"TokenA\";\n            const inputTokenAddress = isTokenAToB ? contractAddresses.TokenA : contractAddresses.TokenB;\n            const inputTokenContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(inputTokenAddress, TokenA_ABI, signer);\n            // Use LiquidityPool for swapping (more advanced than TokenSwap)\n            const swapContract = contractAddresses.LiquidityPool ? new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer) : new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(swapAmount);\n            // Check balance\n            const balance = await inputTokenContract.balanceOf(account);\n            if (balance < amount) {\n                setError(\"Saldo \".concat(selectedTokenIn === \"TokenA\" ? \"Token A\" : \"Token B\", \" tidak mencukupi\"));\n                return;\n            }\n            // Check allowance\n            const swapAddress = contractAddresses.LiquidityPool || contractAddresses.TokenSwap;\n            const allowance = await inputTokenContract.allowance(account, swapAddress);\n            if (allowance < amount) {\n                setSuccess(\"Menyetujui penggunaan \".concat(selectedTokenIn === \"TokenA\" ? \"Token A\" : \"Token B\", \"...\"));\n                const approveTx = await inputTokenContract.approve(swapAddress, amount);\n                await approveTx.wait();\n                setSuccess(\"Approval berhasil! Melakukan swap...\");\n            } else {\n                setSuccess(\"Melakukan swap...\");\n            }\n            // Perform swap\n            if (contractAddresses.LiquidityPool) {\n                // Use LiquidityPool swap function\n                const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);\n                await swapTx.wait();\n            } else {\n                // Use old TokenSwap (only supports TokenA -> TokenB)\n                if (!isTokenAToB) {\n                    setError(\"TokenSwap hanya mendukung Token A → Token B\");\n                    return;\n                }\n                const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);\n                await swapTx.wait();\n            }\n            setSuccess(\"Swap \".concat(selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" → \").concat(selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" berhasil! \\uD83C\\uDF89\"));\n            setSwapAmount(\"\");\n            // Update balances\n            await updateBalances();\n            await updateLiquidity();\n        } catch (error) {\n            console.error(\"Error during swap:\", error);\n            if (error.code === \"ACTION_REJECTED\") {\n                setError(\"Transaksi dibatalkan oleh user\");\n            } else if (error.message.includes(\"insufficient funds\")) {\n                setError(\"Saldo NEX tidak mencukupi untuk gas fee\");\n            } else {\n                setError(\"Swap gagal: \" + (error.reason || error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleAddLiquidity() {\n        if (!liquidityAmountA || !liquidityAmountB || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const amountA = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountA);\n            const amountB = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountB);\n            // Check balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            if (balanceA < amountA) {\n                setError(\"Saldo Token A tidak mencukupi\");\n                return;\n            }\n            if (balanceB < amountB) {\n                setError(\"Saldo Token B tidak mencukupi\");\n                return;\n            }\n            // Approve tokens\n            setSuccess(\"Menyetujui Token A...\");\n            const approveATx = await tokenAContract.approve(contractAddresses.LiquidityPool, amountA);\n            await approveATx.wait();\n            setSuccess(\"Menyetujui Token B...\");\n            const approveBTx = await tokenBContract.approve(contractAddresses.LiquidityPool, amountB);\n            await approveBTx.wait();\n            // Add liquidity\n            setSuccess(\"Menambahkan likuiditas...\");\n            const addLiquidityTx = await lpContract.addLiquidity(amountA, amountB);\n            await addLiquidityTx.wait();\n            setSuccess(\"Likuiditas berhasil ditambahkan! \\uD83C\\uDF89\");\n            setLiquidityAmountA(\"\");\n            setLiquidityAmountB(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error adding liquidity:\", error);\n            setError(\"Gagal menambahkan likuiditas: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleStake() {\n        if (!stakeAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(stakeAmount);\n            // Check LP balance\n            const lpBalance = await lpContract.balanceOf(account);\n            if (lpBalance < amount) {\n                setError(\"Saldo LP Token tidak mencukupi\");\n                return;\n            }\n            // Approve LP tokens\n            setSuccess(\"Menyetujui LP Tokens...\");\n            const approveTx = await lpContract.approve(contractAddresses.Staking, amount);\n            await approveTx.wait();\n            // Stake\n            setSuccess(\"Melakukan stake...\");\n            const stakeTx = await stakingContract.stake(amount);\n            await stakeTx.wait();\n            setSuccess(\"Stake berhasil! \\uD83C\\uDF89\");\n            setStakeAmount(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error staking:\", error);\n            setError(\"Gagal melakukan stake: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleClaim() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            setSuccess(\"Mengklaim rewards...\");\n            const claimTx = await stakingContract.claimReward();\n            await claimTx.wait();\n            setSuccess(\"Rewards berhasil diklaim! \\uD83C\\uDF89\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error claiming:\", error);\n            setError(\"Gagal mengklaim rewards: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const clearMessages = ()=>{\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const isDeployerAccount = account && contractAddresses.deployer && account.toLowerCase() === contractAddresses.deployer.toLowerCase();\n    async function requestTestTokens() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"Requesting test tokens...\");\n        try {\n            // Check if user is the deployer\n            if (isDeployerAccount) {\n                setSuccess(\"You are the deployer! You already have all tokens \\uD83C\\uDF89\");\n                await updateBalances();\n                return;\n            }\n            // For non-deployer users, show instructions\n            setError(\"\");\n            setSuccess(\"\");\n            alert(\"To get test tokens:\\n\\n1. Switch to deployer account: \".concat(contractAddresses.deployer, \"\\n2. Or ask the deployer to send you tokens\\n3. Or use a faucet if available\\n\\nYour current address: \").concat(account, \"\\nDeployer address: \").concat(contractAddresses.deployer, \"\\n\\nThe deployer has 1,000,000 Token A available for distribution.\"));\n        } catch (error) {\n            console.error(\"Error requesting test tokens:\", error);\n            setError(\"Failed to get test tokens: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Batch Operations untuk Maximum Transactions\n    async function handleBatchSwaps() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            // Multiple small swaps untuk generate banyak transaksi\n            const amounts = [\n                \"10\",\n                \"20\",\n                \"30\",\n                \"40\",\n                \"50\"\n            ]; // 5 transaksi swap\n            let totalAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"0\");\n            for (const amount of amounts){\n                totalAmount += ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amount);\n            }\n            // Check balance\n            const balance = await tokenAContract.balanceOf(account);\n            if (balance < totalAmount) {\n                setError(\"Saldo Token A tidak mencukupi untuk batch swaps\");\n                return;\n            }\n            // Approve total amount\n            setSuccess(\"Menyetujui total amount untuk batch swaps...\");\n            const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, totalAmount);\n            await approveTx.wait();\n            // Execute multiple swaps\n            for(let i = 0; i < amounts.length; i++){\n                setSuccess(\"Melakukan swap \".concat(i + 1, \"/\").concat(amounts.length, \"...\"));\n                const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amounts[i]);\n                const swapTx = await swapContract.swap(contractAddresses.TokenA, amount, 0);\n                await swapTx.wait();\n                // Small delay between transactions\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            setSuccess(\"Batch swaps berhasil! \".concat(amounts.length, \" transaksi completed \\uD83C\\uDF89\"));\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error during batch swaps:\", error);\n            setError(\"Batch swaps gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleTransactionSpam() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            // Generate many approval transactions\n            const spamAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"1\");\n            const contracts = [\n                contractAddresses.TokenSwap,\n                contractAddresses.LiquidityPool,\n                contractAddresses.Staking\n            ];\n            let transactionCount = 0;\n            for (const contractAddr of contracts){\n                // Multiple approvals for each contract\n                for(let i = 0; i < 3; i++){\n                    setSuccess(\"Generating approval transaction \".concat(transactionCount + 1, \"...\"));\n                    const approveTx = await tokenAContract.approve(contractAddr, spamAmount);\n                    await approveTx.wait();\n                    transactionCount++;\n                    // Also approve TokenB\n                    const approveBTx = await tokenBContract.approve(contractAddr, spamAmount);\n                    await approveBTx.wait();\n                    transactionCount++;\n                    // Small delay\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                }\n            }\n            setSuccess(\"Transaction spam completed! \".concat(transactionCount, \" transactions generated \\uD83D\\uDE80\"));\n        } catch (error) {\n            console.error(\"Error during transaction spam:\", error);\n            setError(\"Transaction spam gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 relative overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-10 w-32 h-32 bg-cyan-400 transform rotate-12\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 804,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-24 h-24 bg-pink-500 transform -rotate-12\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 805,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 left-1/4 w-28 h-28 bg-yellow-400 transform rotate-45\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 806,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-1/3 w-20 h-20 bg-green-400 transform -rotate-45\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 807,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 803,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10 flex flex-col items-end space-y-3\",\n                children: [\n                    account && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-cyan-400 text-black px-4 py-2 border-4 border-black font-black text-sm shadow-[4px_4px_0px_0px_theme(colors.pink.500)] transform -rotate-1\",\n                        children: [\n                            \"\\uD83D\\uDD17 \",\n                            formatAddress(account)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 812,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-2 border-4 border-black font-black text-sm shadow-[4px_4px_0px_0px_theme(colors.yellow.400)] transform rotate-1 \".concat(isCorrectNetwork ? \"bg-green-400 text-black\" : \"bg-red-500 text-white animate-pulse\"),\n                        children: isCorrectNetwork ? \"✅ NEXUS TESTNET\" : \"⚠️ WRONG NETWORK!\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800 text-cyan-400 px-3 py-2 border-4 border-cyan-400 font-mono text-xs max-w-xs shadow-[4px_4px_0px_0px_theme(colors.gray.600)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"\\uD83D\\uDD17 Chain: 3940\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDCC4 Contracts: \",\n                                    contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? \"✅\" : \"❌\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 825,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\uD83D\\uDC64 You: \",\n                                    account ? account.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 823,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 810,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-screen px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8 sm:mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative inline-block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl sm:text-6xl lg:text-7xl font-black mb-4 text-white transform -rotate-1\",\n                                        children: \"NEXUS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 bg-yellow-400 text-black px-3 py-1 border-4 border-black font-black text-lg sm:text-xl transform rotate-12 shadow-[4px_4px_0px_0px_theme(colors.pink.500)]\",\n                                        children: \"SWAP\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-cyan-400 text-black px-6 py-3 border-4 border-black font-black text-sm sm:text-lg max-w-2xl mx-auto mt-6 shadow-[8px_8px_0px_0px_theme(colors.purple.500)] transform rotate-1\",\n                                children: \"\\uD83D\\uDE80 DEFI ON NEXUS BLOCKCHAIN • MAXIMUM TRANSACTIONS • AIRDROP READY \\uD83C\\uDFAF\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 833,\n                        columnNumber: 9\n                    }, this),\n                    (error || success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mb-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-500 text-white px-6 py-4 border-4 border-black font-black text-sm mb-3 shadow-[8px_8px_0px_0px_theme(colors.yellow.400)] transform -rotate-1 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"❌ \",\n                                            error\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"bg-white text-red-500 px-2 py-1 border-2 border-black font-black hover:bg-yellow-400 hover:text-black transition-colors\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 853,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-400 text-black px-6 py-4 border-4 border-black font-black text-sm mb-3 shadow-[8px_8px_0px_0px_theme(colors.pink.500)] transform rotate-1 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"✅ \",\n                                            success\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"bg-white text-green-600 px-2 py-1 border-2 border-black font-black hover:bg-cyan-400 hover:text-black transition-colors\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 849,\n                        columnNumber: 11\n                    }, this),\n                    !account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: connectWallet,\n                                className: \"bg-yellow-400 text-black px-8 py-4 border-4 border-black font-black text-xl shadow-[8px_8px_0px_0px_theme(colors.cyan.400)] hover:shadow-[12px_12px_0px_0px_theme(colors.cyan.400)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200\",\n                                children: \"\\uD83D\\uDD17 CONNECT WALLET\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-pink-500 text-white px-6 py-3 border-4 border-black font-black text-sm mt-6 shadow-[4px_4px_0px_0px_theme(colors.purple.500)] transform -rotate-1 max-w-md mx-auto\",\n                                children: \"\\uD83D\\uDCAB CONNECT TO START DEFI JOURNEY ON NEXUS \\uD83D\\uDE80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 878,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 870,\n                        columnNumber: 11\n                    }, this) : !isCorrectNetwork ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: switchToNexusNetwork,\n                                        className: \"bg-orange-500 text-white px-8 py-4 border-4 border-black font-black text-xl shadow-[8px_8px_0px_0px_theme(colors.red.500)] hover:shadow-[12px_12px_0px_0px_theme(colors.red.500)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200 animate-pulse\",\n                                        children: \"\\uD83D\\uDD04 SWITCH TO NEXUS\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: checkNetwork,\n                                            className: \"text-sm text-blue-600 hover:text-blue-800 underline\",\n                                            children: \"Already switched? Click to refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 893,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Please switch to Nexus Testnet III to continue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 883,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border-4 border-white p-4 mb-6 shadow-[8px_8px_0px_0px_theme(colors.cyan.400)] transform -rotate-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-400 text-black px-3 py-1 border-4 border-black font-black text-sm mb-4 shadow-[4px_4px_0px_0px_theme(colors.pink.500)] transform rotate-1 inline-block\",\n                                        children: \"PORTFOLIO\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 910,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-cyan-400 text-black p-3 border-4 border-black shadow-[4px_4px_0px_0px_theme(colors.blue.600)] transform rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs mb-1\",\n                                                        children: \"\\uD83D\\uDD35 TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-black\",\n                                                        children: parseFloat(tokenABalance).toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-pink-400 text-black p-3 border-4 border-black shadow-[4px_4px_0px_0px_theme(colors.purple.600)] transform -rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs mb-1\",\n                                                        children: \"\\uD83D\\uDFE3 TKNB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 921,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-black\",\n                                                        children: parseFloat(tokenBBalance).toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 920,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-400 text-black p-3 border-4 border-black shadow-[4px_4px_0px_0px_theme(colors.emerald.600)] transform rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs mb-1\",\n                                                        children: \"\\uD83D\\uDCA7 LP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-black\",\n                                                        children: parseFloat(lpTokenBalance).toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 928,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-400 text-black p-3 border-4 border-black shadow-[4px_4px_0px_0px_theme(colors.red.600)] transform -rotate-1 hover:rotate-0 transition-transform\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-black text-xs mb-1\",\n                                                        children: \"\\uD83C\\uDFE6 STAKE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 933,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-black\",\n                                                        children: parseFloat(stakedBalance).toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(earnedRewards) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 bg-yellow-400 text-black p-4 border-4 border-black shadow-[6px_6px_0px_0px_theme(colors.orange.500)] transform rotate-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-black text-sm\",\n                                                children: \"\\uD83C\\uDF81 PENDING REWARDS\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-mono text-3xl font-black\",\n                                                children: [\n                                                    parseFloat(earnedRewards).toFixed(4),\n                                                    \" TKNB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 942,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 940,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 909,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border-4 border-white shadow-[8px_8px_0px_0px_theme(colors.pink.500)] transform rotate-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex border-b-4 border-white\",\n                                        children: [\n                                            {\n                                                id: \"swap\",\n                                                label: \"SWAP\",\n                                                icon: \"\\uD83D\\uDD04\",\n                                                color: \"bg-cyan-400\"\n                                            },\n                                            {\n                                                id: \"liquidity\",\n                                                label: \"POOL\",\n                                                icon: \"\\uD83D\\uDCA7\",\n                                                color: \"bg-blue-400\"\n                                            },\n                                            {\n                                                id: \"stake\",\n                                                label: \"STAKE\",\n                                                icon: \"\\uD83C\\uDFE6\",\n                                                color: \"bg-green-400\"\n                                            },\n                                            {\n                                                id: \"claim\",\n                                                label: \"CLAIM\",\n                                                icon: \"\\uD83C\\uDF81\",\n                                                color: \"bg-yellow-400\"\n                                            },\n                                            {\n                                                id: \"batch\",\n                                                label: \"BATCH\",\n                                                icon: \"⚡\",\n                                                color: \"bg-pink-400\"\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"flex-1 px-2 py-2 border-r-4 border-white last:border-r-0 font-black text-xs transition-all \".concat(activeTab === tab.id ? \"\".concat(tab.color, \" text-black shadow-[inset_4px_4px_0px_0px_theme(colors.gray.800)]\") : \"bg-gray-700 text-white hover:bg-gray-600\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm mb-1\",\n                                                            children: tab.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 970,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs\",\n                                                            children: tab.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 969,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, tab.id, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 951,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-700 border-4 border-cyan-400 p-4 shadow-[4px_4px_0px_0px_theme(colors.cyan.400)]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-black text-white text-xs\",\n                                                                        children: \"FROM\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-black text-cyan-400 text-xs\",\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCB0 \",\n                                                                            selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance).toFixed(2) : parseFloat(tokenBBalance).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 985,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: selectedTokenIn,\n                                                                        onChange: (e)=>setSelectedTokenIn(e.target.value),\n                                                                        className: \"bg-cyan-400 text-black border-2 border-black px-3 py-2 font-black text-sm focus:outline-none\",\n                                                                        disabled: loading,\n                                                                        title: \"Select token to swap from\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"TokenA\",\n                                                                                children: \"\\uD83D\\uDD35 TKNA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 997,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"TokenB\",\n                                                                                children: \"\\uD83D\\uDFE3 TKNB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 998,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 990,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: swapAmount,\n                                                                        onChange: (e)=>setSwapAmount(e.target.value),\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"flex-1 bg-transparent text-white font-mono text-xl font-black focus:outline-none text-right\",\n                                                                        disabled: loading\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1000,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center -my-2 relative z-10\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>{\n                                                                const temp = selectedTokenIn;\n                                                                setSelectedTokenIn(selectedTokenOut);\n                                                                setSelectedTokenOut(temp);\n                                                            },\n                                                            className: \"bg-pink-500 text-white p-3 border-4 border-black font-black text-xl shadow-[4px_4px_0px_0px_theme(colors.yellow.400)] hover:shadow-[6px_6px_0px_0px_theme(colors.yellow.400)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200 rotate-45 hover:rotate-0\",\n                                                            disabled: loading,\n                                                            children: \"\\uD83D\\uDD04\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1013,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1012,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-700 border-4 border-pink-400 p-4 shadow-[4px_4px_0px_0px_theme(colors.pink.400)]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-black text-white text-xs\",\n                                                                        children: \"TO\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1030,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-black text-pink-400 text-xs\",\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCB0 \",\n                                                                            selectedTokenOut === \"TokenA\" ? parseFloat(tokenABalance).toFixed(2) : parseFloat(tokenBBalance).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1031,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1029,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: selectedTokenOut,\n                                                                        onChange: (e)=>setSelectedTokenOut(e.target.value),\n                                                                        className: \"bg-pink-400 text-black border-2 border-black px-3 py-2 font-black text-sm focus:outline-none\",\n                                                                        disabled: loading,\n                                                                        title: \"Select token to swap to\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"TokenB\",\n                                                                                children: \"\\uD83D\\uDFE3 TKNB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1043,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"TokenA\",\n                                                                                children: \"\\uD83D\\uDD35 TKNA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1044,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1036,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 text-white font-mono text-xl font-black text-right\",\n                                                                        children: swapAmount ? (parseFloat(swapAmount) * 0.997).toFixed(4) : \"0.0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1046,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1035,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setSwapAmount(\"10\"),\n                                                                className: \"flex-1 bg-yellow-400 text-black py-2 border-2 border-black font-black text-xs hover:bg-yellow-300\",\n                                                                disabled: loading,\n                                                                children: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1054,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setSwapAmount(\"100\"),\n                                                                className: \"flex-1 bg-yellow-400 text-black py-2 border-2 border-black font-black text-xs hover:bg-yellow-300\",\n                                                                disabled: loading,\n                                                                children: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1062,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setSwapAmount(selectedTokenIn === \"TokenA\" ? tokenABalance : tokenBBalance),\n                                                                className: \"flex-1 bg-yellow-400 text-black py-2 border-2 border-black font-black text-xs hover:bg-yellow-300\",\n                                                                disabled: loading,\n                                                                children: \"MAX\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1070,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1053,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleSwap,\n                                                        disabled: loading || !swapAmount || parseFloat(swapAmount) <= 0,\n                                                        className: \"w-full bg-green-400 text-black py-4 border-4 border-black font-black text-lg shadow-[6px_6px_0px_0px_theme(colors.green.600)] hover:shadow-[8px_8px_0px_0px_theme(colors.green.600)] transform hover:-translate-x-1 hover:-translate-y-1 transition-all duration-200 disabled:bg-gray-500 disabled:text-gray-300\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-4 border-black border-t-transparent mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1089,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"SWAPPING...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 25\n                                                        }, this) : \"\\uD83D\\uDD04 SWAP \".concat(selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" → \").concat(selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"liquidity\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1101,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token A Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1105,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: liquidityAmountA,\n                                                                                onChange: (e)=>{\n                                                                                    console.log(\"Liquidity A input changed:\", e.target.value);\n                                                                                    setLiquidityAmountA(e.target.value);\n                                                                                },\n                                                                                placeholder: \"0.0\",\n                                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                                disabled: loading,\n                                                                                min: \"0\",\n                                                                                step: \"0.01\",\n                                                                                autoComplete: \"off\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1109,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                                children: \"TKNA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1123,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1108,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"Available: \",\n                                                                                    parseFloat(tokenABalance).toFixed(4),\n                                                                                    \" TKNA\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1128,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(\"10\"),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"10\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1132,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(\"50\"),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"50\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1140,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountA(tokenABalance),\n                                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                        disabled: loading || parseFloat(tokenABalance) === 0,\n                                                                                        children: \"Max\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1148,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1131,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1127,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            'Input: \"',\n                                                                            liquidityAmountA,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1158,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1104,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token B Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1164,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: liquidityAmountB,\n                                                                                onChange: (e)=>{\n                                                                                    console.log(\"Liquidity B input changed:\", e.target.value);\n                                                                                    setLiquidityAmountB(e.target.value);\n                                                                                },\n                                                                                placeholder: \"0.0\",\n                                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                                disabled: loading,\n                                                                                min: \"0\",\n                                                                                step: \"0.01\",\n                                                                                autoComplete: \"off\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1168,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                                children: \"TKNB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1182,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1167,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"Available: \",\n                                                                                    parseFloat(tokenBBalance).toFixed(4),\n                                                                                    \" TKNB\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1187,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(\"10\"),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"10\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1191,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(\"50\"),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading,\n                                                                                        children: \"50\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1199,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        onClick: ()=>setLiquidityAmountB(tokenBBalance),\n                                                                                        className: \"text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded hover:bg-purple-200\",\n                                                                                        disabled: loading || parseFloat(tokenBBalance) === 0,\n                                                                                        children: \"Max\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1207,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1190,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1186,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            'Input: \"',\n                                                                            liquidityAmountB,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1217,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1163,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1103,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleAddLiquidity,\n                                                        disabled: loading || !liquidityAmountA || !liquidityAmountB,\n                                                        className: \"w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1223,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1100,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"stake\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1236,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Stake Amount (LP Tokens)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1239,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: stakeAmount,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Stake input changed:\", e.target.value);\n                                                                            setStakeAmount(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1243,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1257,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1242,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Available: \",\n                                                                            parseFloat(lpTokenBalance).toFixed(4),\n                                                                            \" NLP\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1262,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setStakeAmount(\"1\"),\n                                                                                className: \"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200\",\n                                                                                disabled: loading,\n                                                                                children: \"1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1266,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setStakeAmount(\"5\"),\n                                                                                className: \"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200\",\n                                                                                disabled: loading,\n                                                                                children: \"5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1274,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setStakeAmount(lpTokenBalance),\n                                                                                className: \"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded hover:bg-orange-200\",\n                                                                                disabled: loading || parseFloat(lpTokenBalance) === 0,\n                                                                                children: \"Max\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1282,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1265,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1261,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: [\n                                                                    'Input: \"',\n                                                                    stakeAmount,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1292,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1238,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleStake,\n                                                        disabled: loading || !stakeAmount || parseFloat(lpTokenBalance) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-orange-600 to-red-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1297,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1235,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"claim\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6 p-4 bg-yellow-50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mb-2\",\n                                                                children: \"Pending Rewards\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1313,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-mono text-3xl font-bold text-yellow-600\",\n                                                                children: parseFloat(earnedRewards).toFixed(6)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1314,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"TKNB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1317,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1312,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Staked\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1322,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold\",\n                                                                        children: parseFloat(stakedBalance).toFixed(4)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1323,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1326,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1321,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"APY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1329,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold text-green-600\",\n                                                                        children: \"~50%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1330,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"Estimated\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1333,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1328,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1320,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleClaim,\n                                                        disabled: loading || parseFloat(earnedRewards) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-yellow-600 to-orange-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-yellow-700 hover:to-orange-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1337,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1309,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"batch\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Batch Operations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-6\",\n                                                        children: \"Generate multiple transactions for maximum Nexus testnet interaction!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-blue-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-blue-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDD04 Batch Swaps\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1358,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-blue-700 mb-3\",\n                                                                        children: \"Execute 5 separate swap transactions (10, 20, 30, 40, 50 TKNA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1359,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleBatchSwaps,\n                                                                        disabled: loading || parseFloat(tokenABalance) < 150,\n                                                                        className: \"w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Execute Batch Swaps (5 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1362,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 mt-1\",\n                                                                        children: \"Requires: 150 TKNA minimum\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1370,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1357,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-purple-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-purple-800 mb-2\",\n                                                                        children: \"⚡ Transaction Spam\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1377,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-purple-700 mb-3\",\n                                                                        children: \"Generate 18 approval transactions across all contracts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1378,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleTransactionSpam,\n                                                                        disabled: loading,\n                                                                        className: \"w-full bg-purple-600 text-white py-3 rounded-lg font-medium hover:bg-purple-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Generate Transaction Spam (18 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1381,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-purple-600 mt-1\",\n                                                                        children: \"Generates many approval transactions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1389,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-green-50 rounded-xl text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-green-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCCA Transaction Counter\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1396,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-700 mb-2\",\n                                                                        children: \"Estimated transactions per full cycle:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1397,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"Basic Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1402,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"8-10 TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1403,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1401,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"With Batches\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1406,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"25+ TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1407,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1405,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1400,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1395,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-yellow-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCA1 Pro Tips\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1414,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-sm text-yellow-700 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Use batch operations to generate many transactions quickly\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1416,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Each operation creates multiple blockchain interactions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1417,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Perfect for maximizing Nexus testnet contribution\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1418,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Monitor your transaction count in MetaMask\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1419,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1415,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1413,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1355,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(tokenABalance) === 0 && activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-6 mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-yellow-800 mb-2\",\n                                                children: \"Need Test Tokens?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1430,\n                                                columnNumber: 19\n                                            }, this),\n                                            isDeployerAccount ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"You are the deployer! You have 1,000,000 Token A available.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1433,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: updateBalances,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200\",\n                                                        children: \"Refresh Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1436,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1432,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"To get test tokens, switch to the deployer account or ask for a transfer:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1446,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-yellow-600 mb-3 font-mono bg-yellow-100 p-2 rounded\",\n                                                        children: [\n                                                            \"Deployer: \",\n                                                            (_contractAddresses_deployer = contractAddresses.deployer) === null || _contractAddresses_deployer === void 0 ? void 0 : _contractAddresses_deployer.slice(0, 20),\n                                                            \"...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: requestTestTokens,\n                                                        disabled: loading,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                        children: \"Show Instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1452,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1445,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1429,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 950,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"DeFi Flow Guide:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"1. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Swap\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1470,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Trade Token A ⇄ Token B\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"2. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1471,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Provide both tokens → Get LP tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1471,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"3. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Stake\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1472,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Stake LP tokens → Earn rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"4. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Claim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1473,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Collect your earned rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1473,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"5. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Repeat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1474,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": More transactions = More Nexus interaction!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1474,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1469,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1467,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 907,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 831,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 801,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Xn9OxmB2iOgPw0us/9Bh41mAbgc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});