{"author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/logger": "^5.8.0"}, "description": "Bytes utility functions for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/bytes", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/bytes", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"auto-build": "npm run build -- -w", "build": "tsc -p ./tsconfig.json", "test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xc03a78632cb02f6821e27812cb654f96b13ad63454a4750b783ea98241bee5a8", "types": "./lib/index.d.ts", "version": "5.8.0"}