"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@adraffy";
exports.ids = ["vendor-chunks/@adraffy"];
exports.modules = {

/***/ "(ssr)/./node_modules/@adraffy/ens-normalize/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@adraffy/ens-normalize/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ens_beautify: () => (/* binding */ ens_beautify),\n/* harmony export */   ens_emoji: () => (/* binding */ ens_emoji),\n/* harmony export */   ens_normalize: () => (/* binding */ ens_normalize),\n/* harmony export */   ens_normalize_fragment: () => (/* binding */ ens_normalize_fragment),\n/* harmony export */   ens_split: () => (/* binding */ ens_split),\n/* harmony export */   ens_tokenize: () => (/* binding */ ens_tokenize),\n/* harmony export */   is_combining_mark: () => (/* binding */ is_combining_mark),\n/* harmony export */   nfc: () => (/* binding */ nfc),\n/* harmony export */   nfd: () => (/* binding */ nfd),\n/* harmony export */   safe_str_from_cps: () => (/* binding */ safe_str_from_cps),\n/* harmony export */   should_escape: () => (/* binding */ should_escape)\n/* harmony export */ });\n// created 2023-09-25T01:01:55.148Z\n// compressed base64-encoded blob for include-ens data\n// source: https://github.com/adraffy/ens-normalize.js/blob/main/src/make.js\n// see: https://github.com/adraffy/ens-normalize.js#security\n// SHA-256: 0565ed049b9cf1614bb9e11ba7d8ac6a6fb96c893253d890f7e2b2884b9ded32\nvar COMPRESSED$1 = \"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\";\nconst FENCED = new Map([\n    [\n        8217,\n        \"apostrophe\"\n    ],\n    [\n        8260,\n        \"fraction slash\"\n    ],\n    [\n        12539,\n        \"middle dot\"\n    ]\n]);\nconst NSM_MAX = 4;\nfunction decode_arithmetic(bytes) {\n    let pos = 0;\n    function u16() {\n        return bytes[pos++] << 8 | bytes[pos++];\n    }\n    // decode the frequency table\n    let symbol_count = u16();\n    let total = 1;\n    let acc = [\n        0,\n        1\n    ]; // first symbol has frequency 1\n    for(let i = 1; i < symbol_count; i++){\n        acc.push(total += u16());\n    }\n    // skip the sized-payload that the last 3 symbols index into\n    let skip = u16();\n    let pos_payload = pos;\n    pos += skip;\n    let read_width = 0;\n    let read_buffer = 0;\n    function read_bit() {\n        if (read_width == 0) {\n            // this will read beyond end of buffer\n            // but (undefined|0) => zero pad\n            read_buffer = read_buffer << 8 | bytes[pos++];\n            read_width = 8;\n        }\n        return read_buffer >> --read_width & 1;\n    }\n    const N = 31;\n    const FULL = 2 ** N;\n    const HALF = FULL >>> 1;\n    const QRTR = HALF >> 1;\n    const MASK = FULL - 1;\n    // fill register\n    let register = 0;\n    for(let i = 0; i < N; i++)register = register << 1 | read_bit();\n    let symbols = [];\n    let low = 0;\n    let range = FULL; // treat like a float\n    while(true){\n        let value = Math.floor(((register - low + 1) * total - 1) / range);\n        let start = 0;\n        let end = symbol_count;\n        while(end - start > 1){\n            let mid = start + end >>> 1;\n            if (value < acc[mid]) {\n                end = mid;\n            } else {\n                start = mid;\n            }\n        }\n        if (start == 0) break; // first symbol is end mark\n        symbols.push(start);\n        let a = low + Math.floor(range * acc[start] / total);\n        let b = low + Math.floor(range * acc[start + 1] / total) - 1;\n        while(((a ^ b) & HALF) == 0){\n            register = register << 1 & MASK | read_bit();\n            a = a << 1 & MASK;\n            b = b << 1 & MASK | 1;\n        }\n        while(a & ~b & QRTR){\n            register = register & HALF | register << 1 & MASK >>> 1 | read_bit();\n            a = a << 1 ^ HALF;\n            b = (b ^ HALF) << 1 | HALF | 1;\n        }\n        low = a;\n        range = 1 + b - a;\n    }\n    let offset = symbol_count - 4;\n    return symbols.map((x)=>{\n        switch(x - offset){\n            case 3:\n                return offset + 0x10100 + (bytes[pos_payload++] << 16 | bytes[pos_payload++] << 8 | bytes[pos_payload++]);\n            case 2:\n                return offset + 0x100 + (bytes[pos_payload++] << 8 | bytes[pos_payload++]);\n            case 1:\n                return offset + bytes[pos_payload++];\n            default:\n                return x - 1;\n        }\n    });\n}\n// returns an iterator which returns the next symbol\nfunction read_payload(v) {\n    let pos = 0;\n    return ()=>v[pos++];\n}\nfunction read_compressed_payload(s) {\n    return read_payload(decode_arithmetic(unsafe_atob(s)));\n}\n// unsafe in the sense:\n// expected well-formed Base64 w/o padding \n// 20220922: added for https://github.com/adraffy/ens-normalize.js/issues/4\nfunction unsafe_atob(s) {\n    let lookup = [];\n    [\n        ...\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\"\n    ].forEach((c, i)=>lookup[c.charCodeAt(0)] = i);\n    let n = s.length;\n    let ret = new Uint8Array(6 * n >> 3);\n    for(let i = 0, pos = 0, width = 0, carry = 0; i < n; i++){\n        carry = carry << 6 | lookup[s.charCodeAt(i)];\n        width += 6;\n        if (width >= 8) {\n            ret[pos++] = carry >> (width -= 8);\n        }\n    }\n    return ret;\n}\n// eg. [0,1,2,3...] => [0,-1,1,-2,...]\nfunction signed(i) {\n    return i & 1 ? ~i >> 1 : i >> 1;\n}\nfunction read_deltas(n, next) {\n    let v = Array(n);\n    for(let i = 0, x = 0; i < n; i++)v[i] = x += signed(next());\n    return v;\n}\n// [123][5] => [0 3] [1 1] [0 0]\nfunction read_sorted(next, prev = 0) {\n    let ret = [];\n    while(true){\n        let x = next();\n        let n = next();\n        if (!n) break;\n        prev += x;\n        for(let i = 0; i < n; i++){\n            ret.push(prev + i);\n        }\n        prev += n + 1;\n    }\n    return ret;\n}\nfunction read_sorted_arrays(next) {\n    return read_array_while(()=>{\n        let v = read_sorted(next);\n        if (v.length) return v;\n    });\n}\n// returns map of x => ys\nfunction read_mapped(next) {\n    let ret = [];\n    while(true){\n        let w = next();\n        if (w == 0) break;\n        ret.push(read_linear_table(w, next));\n    }\n    while(true){\n        let w = next() - 1;\n        if (w < 0) break;\n        ret.push(read_replacement_table(w, next));\n    }\n    return ret.flat();\n}\n// read until next is falsy\n// return array of read values\nfunction read_array_while(next) {\n    let v = [];\n    while(true){\n        let x = next(v.length);\n        if (!x) break;\n        v.push(x);\n    }\n    return v;\n}\n// read w columns of length n\n// return as n rows of length w\nfunction read_transposed(n, w, next) {\n    let m = Array(n).fill().map(()=>[]);\n    for(let i = 0; i < w; i++){\n        read_deltas(n, next).forEach((x, j)=>m[j].push(x));\n    }\n    return m;\n}\n// returns [[x, ys], [x+dx, ys+dy], [x+2*dx, ys+2*dy], ...]\n// where dx/dy = steps, n = run size, w = length of y\nfunction read_linear_table(w, next) {\n    let dx = 1 + next();\n    let dy = next();\n    let vN = read_array_while(next);\n    let m = read_transposed(vN.length, 1 + w, next);\n    return m.flatMap((v, i)=>{\n        let [x, ...ys] = v;\n        return Array(vN[i]).fill().map((_, j)=>{\n            let j_dy = j * dy;\n            return [\n                x + j * dx,\n                ys.map((y)=>y + j_dy)\n            ];\n        });\n    });\n}\n// return [[x, ys...], ...]\n// where w = length of y\nfunction read_replacement_table(w, next) {\n    let n = 1 + next();\n    let m = read_transposed(n, 1 + w, next);\n    return m.map((v)=>[\n            v[0],\n            v.slice(1)\n        ]);\n}\nfunction read_trie(next) {\n    let ret = [];\n    let sorted = read_sorted(next);\n    expand(decode([]), []);\n    return ret; // not sorted\n    function decode(Q) {\n        let S = next(); // state: valid, save, check\n        let B = read_array_while(()=>{\n            let cps = read_sorted(next).map((i)=>sorted[i]);\n            if (cps.length) return decode(cps);\n        });\n        return {\n            S,\n            B,\n            Q\n        };\n    }\n    function expand({ S, B }, cps, saved) {\n        if (S & 4 && saved === cps[cps.length - 1]) return;\n        if (S & 2) saved = cps[cps.length - 1];\n        if (S & 1) ret.push(cps);\n        for (let br of B){\n            for (let cp of br.Q){\n                expand(br, [\n                    ...cps,\n                    cp\n                ], saved);\n            }\n        }\n    }\n}\nfunction hex_cp(cp) {\n    return cp.toString(16).toUpperCase().padStart(2, \"0\");\n}\nfunction quote_cp(cp) {\n    return `{${hex_cp(cp)}}`; // raffy convention: like \"\\u{X}\" w/o the \"\\u\"\n}\n/*\r\nexport function explode_cp(s) {\r\n\treturn [...s].map(c => c.codePointAt(0));\r\n}\r\n*/ function explode_cp(s) {\n    let cps = [];\n    for(let pos = 0, len = s.length; pos < len;){\n        let cp = s.codePointAt(pos);\n        pos += cp < 0x10000 ? 1 : 2;\n        cps.push(cp);\n    }\n    return cps;\n}\nfunction str_from_cps(cps) {\n    const chunk = 4096;\n    let len = cps.length;\n    if (len < chunk) return String.fromCodePoint(...cps);\n    let buf = [];\n    for(let i = 0; i < len;){\n        buf.push(String.fromCodePoint(...cps.slice(i, i += chunk)));\n    }\n    return buf.join(\"\");\n}\nfunction compare_arrays(a, b) {\n    let n = a.length;\n    let c = n - b.length;\n    for(let i = 0; c == 0 && i < n; i++)c = a[i] - b[i];\n    return c;\n}\n// created 2023-09-25T01:01:55.148Z\n// compressed base64-encoded blob for include-nf data\n// source: https://github.com/adraffy/ens-normalize.js/blob/main/src/make.js\n// see: https://github.com/adraffy/ens-normalize.js#security\n// SHA-256: a974b6f8541fc29d919bc85118af0a44015851fab5343f8679cb31be2bdb209e\nvar COMPRESSED = \"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\";\n// https://unicode.org/reports/tr15/\n// for reference implementation\n// see: /derive/nf.js\n// algorithmic hangul\n// https://www.unicode.org/versions/Unicode15.0.0/ch03.pdf (page 144)\nconst S0 = 0xAC00;\nconst L0 = 0x1100;\nconst V0 = 0x1161;\nconst T0 = 0x11A7;\nconst L_COUNT = 19;\nconst V_COUNT = 21;\nconst T_COUNT = 28;\nconst N_COUNT = V_COUNT * T_COUNT;\nconst S_COUNT = L_COUNT * N_COUNT;\nconst S1 = S0 + S_COUNT;\nconst L1 = L0 + L_COUNT;\nconst V1 = V0 + V_COUNT;\nconst T1 = T0 + T_COUNT;\nfunction unpack_cc(packed) {\n    return packed >> 24 & 0xFF;\n}\nfunction unpack_cp(packed) {\n    return packed & 0xFFFFFF;\n}\nlet SHIFTED_RANK, EXCLUSIONS, DECOMP, RECOMP;\nfunction init$1() {\n    //console.time('nf');\n    let r = read_compressed_payload(COMPRESSED);\n    SHIFTED_RANK = new Map(read_sorted_arrays(r).flatMap((v, i)=>v.map((x)=>[\n                x,\n                i + 1 << 24\n            ]))); // pre-shifted\n    EXCLUSIONS = new Set(read_sorted(r));\n    DECOMP = new Map();\n    RECOMP = new Map();\n    for (let [cp, cps] of read_mapped(r)){\n        if (!EXCLUSIONS.has(cp) && cps.length == 2) {\n            let [a, b] = cps;\n            let bucket = RECOMP.get(a);\n            if (!bucket) {\n                bucket = new Map();\n                RECOMP.set(a, bucket);\n            }\n            bucket.set(b, cp);\n        }\n        DECOMP.set(cp, cps.reverse()); // stored reversed\n    }\n//console.timeEnd('nf');\n// 20230905: 11ms\n}\nfunction is_hangul(cp) {\n    return cp >= S0 && cp < S1;\n}\nfunction compose_pair(a, b) {\n    if (a >= L0 && a < L1 && b >= V0 && b < V1) {\n        return S0 + (a - L0) * N_COUNT + (b - V0) * T_COUNT;\n    } else if (is_hangul(a) && b > T0 && b < T1 && (a - S0) % T_COUNT == 0) {\n        return a + (b - T0);\n    } else {\n        let recomp = RECOMP.get(a);\n        if (recomp) {\n            recomp = recomp.get(b);\n            if (recomp) {\n                return recomp;\n            }\n        }\n        return -1;\n    }\n}\nfunction decomposed(cps) {\n    if (!SHIFTED_RANK) init$1();\n    let ret = [];\n    let buf = [];\n    let check_order = false;\n    function add(cp) {\n        let cc = SHIFTED_RANK.get(cp);\n        if (cc) {\n            check_order = true;\n            cp |= cc;\n        }\n        ret.push(cp);\n    }\n    for (let cp of cps){\n        while(true){\n            if (cp < 0x80) {\n                ret.push(cp);\n            } else if (is_hangul(cp)) {\n                let s_index = cp - S0;\n                let l_index = s_index / N_COUNT | 0;\n                let v_index = s_index % N_COUNT / T_COUNT | 0;\n                let t_index = s_index % T_COUNT;\n                add(L0 + l_index);\n                add(V0 + v_index);\n                if (t_index > 0) add(T0 + t_index);\n            } else {\n                let mapped = DECOMP.get(cp);\n                if (mapped) {\n                    buf.push(...mapped);\n                } else {\n                    add(cp);\n                }\n            }\n            if (!buf.length) break;\n            cp = buf.pop();\n        }\n    }\n    if (check_order && ret.length > 1) {\n        let prev_cc = unpack_cc(ret[0]);\n        for(let i = 1; i < ret.length; i++){\n            let cc = unpack_cc(ret[i]);\n            if (cc == 0 || prev_cc <= cc) {\n                prev_cc = cc;\n                continue;\n            }\n            let j = i - 1;\n            while(true){\n                let tmp = ret[j + 1];\n                ret[j + 1] = ret[j];\n                ret[j] = tmp;\n                if (!j) break;\n                prev_cc = unpack_cc(ret[--j]);\n                if (prev_cc <= cc) break;\n            }\n            prev_cc = unpack_cc(ret[i]);\n        }\n    }\n    return ret;\n}\nfunction composed_from_decomposed(v) {\n    let ret = [];\n    let stack = [];\n    let prev_cp = -1;\n    let prev_cc = 0;\n    for (let packed of v){\n        let cc = unpack_cc(packed);\n        let cp = unpack_cp(packed);\n        if (prev_cp == -1) {\n            if (cc == 0) {\n                prev_cp = cp;\n            } else {\n                ret.push(cp);\n            }\n        } else if (prev_cc > 0 && prev_cc >= cc) {\n            if (cc == 0) {\n                ret.push(prev_cp, ...stack);\n                stack.length = 0;\n                prev_cp = cp;\n            } else {\n                stack.push(cp);\n            }\n            prev_cc = cc;\n        } else {\n            let composed = compose_pair(prev_cp, cp);\n            if (composed >= 0) {\n                prev_cp = composed;\n            } else if (prev_cc == 0 && cc == 0) {\n                ret.push(prev_cp);\n                prev_cp = cp;\n            } else {\n                stack.push(cp);\n                prev_cc = cc;\n            }\n        }\n    }\n    if (prev_cp >= 0) {\n        ret.push(prev_cp, ...stack);\n    }\n    return ret;\n}\n// note: cps can be iterable\nfunction nfd(cps) {\n    return decomposed(cps).map(unpack_cp);\n}\nfunction nfc(cps) {\n    return composed_from_decomposed(decomposed(cps));\n}\nconst HYPHEN = 0x2D;\nconst STOP = 0x2E;\nconst STOP_CH = \".\";\nconst FE0F = 0xFE0F;\nconst UNIQUE_PH = 1;\n// 20230913: replace [...v] with Array_from(v) to avoid large spreads\nconst Array_from = (x)=>Array.from(x); // Array.from.bind(Array);\nfunction group_has_cp(g, cp) {\n    // 20230913: keep primary and secondary distinct instead of creating valid union\n    return g.P.has(cp) || g.Q.has(cp);\n}\nclass Emoji extends Array {\n    get is_emoji() {\n        return true;\n    }\n}\nlet MAPPED, IGNORED, CM, NSM, ESCAPE, NFC_CHECK, GROUPS, WHOLE_VALID, WHOLE_MAP, VALID, EMOJI_LIST, EMOJI_ROOT;\nfunction init() {\n    if (MAPPED) return;\n    let r = read_compressed_payload(COMPRESSED$1);\n    const read_sorted_array = ()=>read_sorted(r);\n    const read_sorted_set = ()=>new Set(read_sorted_array());\n    const set_add_many = (set, v)=>v.forEach((x)=>set.add(x));\n    MAPPED = new Map(read_mapped(r));\n    IGNORED = read_sorted_set(); // ignored characters are not valid, so just read raw codepoints\n    /*\r\n\t// direct include from payload is smaller than the decompression code\r\n\tconst FENCED = new Map(read_array_while(() => {\r\n\t\tlet cp = r();\r\n\t\tif (cp) return [cp, read_str(r())];\r\n\t}));\r\n\t*/ // 20230217: we still need all CM for proper error formatting\n    // but norm only needs NSM subset that are potentially-valid\n    CM = read_sorted_array();\n    NSM = new Set(read_sorted_array().map((i)=>CM[i]));\n    CM = new Set(CM);\n    ESCAPE = read_sorted_set(); // characters that should not be printed\n    NFC_CHECK = read_sorted_set(); // only needed to illustrate ens_tokenize() transformations\n    let chunks = read_sorted_arrays(r);\n    let unrestricted = r();\n    //const read_chunked = () => new Set(read_sorted_array().flatMap(i => chunks[i]).concat(read_sorted_array()));\n    const read_chunked = ()=>{\n        // 20230921: build set in parts, 2x faster\n        let set = new Set();\n        read_sorted_array().forEach((i)=>set_add_many(set, chunks[i]));\n        set_add_many(set, read_sorted_array());\n        return set;\n    };\n    GROUPS = read_array_while((i)=>{\n        // minifier property mangling seems unsafe\n        // so these are manually renamed to single chars\n        let N = read_array_while(r).map((x)=>x + 0x60);\n        if (N.length) {\n            let R = i >= unrestricted; // unrestricted then restricted\n            N[0] -= 32; // capitalize\n            N = str_from_cps(N);\n            if (R) N = `Restricted[${N}]`;\n            let P = read_chunked(); // primary\n            let Q = read_chunked(); // secondary\n            let M = !r(); // not-whitelisted, check for NSM\n            // *** this code currently isn't needed ***\n            /*\r\n\t\t\tlet V = [...P, ...Q].sort((a, b) => a-b); // derive: sorted valid\r\n\t\t\tlet M = r()-1; // number of combining mark\r\n\t\t\tif (M < 0) { // whitelisted\r\n\t\t\t\tM = new Map(read_array_while(() => {\r\n\t\t\t\t\tlet i = r();\r\n\t\t\t\t\tif (i) return [V[i-1], read_array_while(() => {\r\n\t\t\t\t\t\tlet v = read_array_while(r);\r\n\t\t\t\t\t\tif (v.length) return v.map(x => x-1);\r\n\t\t\t\t\t})];\r\n\t\t\t\t}));\r\n\t\t\t}*/ return {\n                N,\n                P,\n                Q,\n                M,\n                R\n            };\n        }\n    });\n    // decode compressed wholes\n    WHOLE_VALID = read_sorted_set();\n    WHOLE_MAP = new Map();\n    let wholes = read_sorted_array().concat(Array_from(WHOLE_VALID)).sort((a, b)=>a - b); // must be sorted\n    wholes.forEach((cp, i)=>{\n        let d = r();\n        let w = wholes[i] = d ? wholes[i - d] : {\n            V: [],\n            M: new Map()\n        };\n        w.V.push(cp); // add to member set\n        if (!WHOLE_VALID.has(cp)) {\n            WHOLE_MAP.set(cp, w); // register with whole map\n        }\n    });\n    // compute confusable-extent complements\n    // usage: WHOLE_MAP.get(cp).M.get(cp) = complement set\n    for (let { V, M } of new Set(WHOLE_MAP.values())){\n        // connect all groups that have each whole character\n        let recs = [];\n        for (let cp of V){\n            let gs = GROUPS.filter((g)=>group_has_cp(g, cp));\n            let rec = recs.find(({ G })=>gs.some((g)=>G.has(g)));\n            if (!rec) {\n                rec = {\n                    G: new Set(),\n                    V: []\n                };\n                recs.push(rec);\n            }\n            rec.V.push(cp);\n            set_add_many(rec.G, gs);\n        }\n        // per character cache groups which are not a member of the extent\n        let union = recs.flatMap((x)=>Array_from(x.G)); // all of the groups used by this whole\n        for (let { G, V } of recs){\n            let complement = new Set(union.filter((g)=>!G.has(g))); // groups not covered by the extent\n            for (let cp of V){\n                M.set(cp, complement); // this is the same reference\n            }\n        }\n    }\n    // compute valid set\n    // 20230924: VALID was union but can be re-used\n    VALID = new Set(); // exists in 1+ groups\n    let multi = new Set(); // exists in 2+ groups\n    const add_to_union = (cp)=>VALID.has(cp) ? multi.add(cp) : VALID.add(cp);\n    for (let g of GROUPS){\n        for (let cp of g.P)add_to_union(cp);\n        for (let cp of g.Q)add_to_union(cp);\n    }\n    // dual purpose WHOLE_MAP: return placeholder if unique non-confusable\n    for (let cp of VALID){\n        if (!WHOLE_MAP.has(cp) && !multi.has(cp)) {\n            WHOLE_MAP.set(cp, UNIQUE_PH);\n        }\n    }\n    // add all decomposed parts\n    // see derive: \"Valid is Closed (via Brute-force)\"\n    set_add_many(VALID, nfd(VALID));\n    // decode emoji\n    // 20230719: emoji are now fully-expanded to avoid quirk logic \n    EMOJI_LIST = read_trie(r).map((v)=>Emoji.from(v)).sort(compare_arrays);\n    EMOJI_ROOT = new Map(); // this has approx 7K nodes (2+ per emoji)\n    for (let cps of EMOJI_LIST){\n        // 20230719: change to *slightly* stricter algorithm which disallows \n        // insertion of misplaced FE0F in emoji sequences (matching ENSIP-15)\n        // example: beautified [A B] (eg. flag emoji) \n        //  before: allow: [A FE0F B], error: [A FE0F FE0F B] \n        //   after: error: both\n        // note: this code now matches ENSNormalize.{cs,java} logic\n        let prev = [\n            EMOJI_ROOT\n        ];\n        for (let cp of cps){\n            let next = prev.map((node)=>{\n                let child = node.get(cp);\n                if (!child) {\n                    // should this be object? \n                    // (most have 1-2 items, few have many)\n                    // 20230719: no, v8 default map is 4?\n                    child = new Map();\n                    node.set(cp, child);\n                }\n                return child;\n            });\n            if (cp === FE0F) {\n                prev.push(...next); // less than 20 elements\n            } else {\n                prev = next;\n            }\n        }\n        for (let x of prev){\n            x.V = cps;\n        }\n    }\n}\n// if escaped: {HEX}\n//       else: \"x\" {HEX}\nfunction quoted_cp(cp) {\n    return (should_escape(cp) ? \"\" : `${bidi_qq(safe_str_from_cps([\n        cp\n    ]))} `) + quote_cp(cp);\n}\n// 20230211: some messages can be mixed-directional and result in spillover\n// use 200E after a quoted string to force the remainder of a string from \n// acquring the direction of the quote\n// https://www.w3.org/International/questions/qa-bidi-unicode-controls#exceptions\nfunction bidi_qq(s) {\n    return `\"${s}\"\\u200E`; // strong LTR\n}\nfunction check_label_extension(cps) {\n    if (cps.length >= 4 && cps[2] == HYPHEN && cps[3] == HYPHEN) {\n        throw new Error(`invalid label extension: \"${str_from_cps(cps.slice(0, 4))}\"`); // this can only be ascii so cant be bidi\n    }\n}\nfunction check_leading_underscore(cps) {\n    const UNDERSCORE = 0x5F;\n    for(let i = cps.lastIndexOf(UNDERSCORE); i > 0;){\n        if (cps[--i] !== UNDERSCORE) {\n            throw new Error(\"underscore allowed only at start\");\n        }\n    }\n}\n// check that a fenced cp is not leading, trailing, or touching another fenced cp\nfunction check_fenced(cps) {\n    let cp = cps[0];\n    let prev = FENCED.get(cp);\n    if (prev) throw error_placement(`leading ${prev}`);\n    let n = cps.length;\n    let last = -1; // prevents trailing from throwing\n    for(let i = 1; i < n; i++){\n        cp = cps[i];\n        let match = FENCED.get(cp);\n        if (match) {\n            // since cps[0] isn't fenced, cps[1] cannot throw\n            if (last == i) throw error_placement(`${prev} + ${match}`);\n            last = i + 1;\n            prev = match;\n        }\n    }\n    if (last == n) throw error_placement(`trailing ${prev}`);\n}\n// create a safe to print string \n// invisibles are escaped\n// leading cm uses placeholder\n// if cps exceed max, middle truncate with ellipsis\n// quoter(cp) => string, eg. 3000 => \"{3000}\"\n// note: in html, you'd call this function then replace [<>&] with entities\nfunction safe_str_from_cps(cps, max = Infinity, quoter = quote_cp) {\n    //if (Number.isInteger(cps)) cps = [cps];\n    //if (!Array.isArray(cps)) throw new TypeError(`expected codepoints`);\n    let buf = [];\n    if (is_combining_mark(cps[0])) buf.push(\"◌\");\n    if (cps.length > max) {\n        max >>= 1;\n        cps = [\n            ...cps.slice(0, max),\n            0x2026,\n            ...cps.slice(-max)\n        ];\n    }\n    let prev = 0;\n    let n = cps.length;\n    for(let i = 0; i < n; i++){\n        let cp = cps[i];\n        if (should_escape(cp)) {\n            buf.push(str_from_cps(cps.slice(prev, i)));\n            buf.push(quoter(cp));\n            prev = i + 1;\n        }\n    }\n    buf.push(str_from_cps(cps.slice(prev, n)));\n    return buf.join(\"\");\n}\n// note: set(s) cannot be exposed because they can be modified\n// note: Object.freeze() doesn't work\nfunction is_combining_mark(cp) {\n    init();\n    return CM.has(cp);\n}\nfunction should_escape(cp) {\n    init();\n    return ESCAPE.has(cp);\n}\n// return all supported emoji as fully-qualified emoji \n// ordered by length then lexicographic \nfunction ens_emoji() {\n    init();\n    return EMOJI_LIST.map((x)=>x.slice()); // emoji are exposed so copy\n}\nfunction ens_normalize_fragment(frag, decompose) {\n    init();\n    let nf = decompose ? nfd : nfc;\n    return frag.split(STOP_CH).map((label)=>str_from_cps(tokens_from_str(explode_cp(label), nf, filter_fe0f).flat())).join(STOP_CH);\n}\nfunction ens_normalize(name) {\n    return flatten(split(name, nfc, filter_fe0f));\n}\nfunction ens_beautify(name) {\n    let labels = split(name, nfc, (x)=>x); // emoji not exposed\n    for (let { type, output, error } of labels){\n        if (error) break; // flatten will throw\n        // replace leading/trailing hyphen\n        // 20230121: consider beautifing all or leading/trailing hyphen to unicode variant\n        // not exactly the same in every font, but very similar: \"-\" vs \"‐\"\n        /*\r\n\t\tconst UNICODE_HYPHEN = 0x2010;\r\n\t\t// maybe this should replace all for visual consistancy?\r\n\t\t// `node tools/reg-count.js regex ^-\\{2,\\}` => 592\r\n\t\t//for (let i = 0; i < output.length; i++) if (output[i] == 0x2D) output[i] = 0x2010;\r\n\t\tif (output[0] == HYPHEN) output[0] = UNICODE_HYPHEN;\r\n\t\tlet end = output.length-1;\r\n\t\tif (output[end] == HYPHEN) output[end] = UNICODE_HYPHEN;\r\n\t\t*/ // 20230123: WHATWG URL uses \"CheckHyphens\" false\n        // https://url.spec.whatwg.org/#idna\n        // update ethereum symbol\n        // ξ => Ξ if not greek\n        if (type !== \"Greek\") array_replace(output, 0x3BE, 0x39E);\n    // 20221213: fixes bidi subdomain issue, but breaks invariant (200E is disallowed)\n    // could be fixed with special case for: 2D (.) + 200E (LTR)\n    // https://discuss.ens.domains/t/bidi-label-ordering-spoof/15824\n    //output.splice(0, 0, 0x200E);\n    }\n    return flatten(labels);\n}\nfunction array_replace(v, a, b) {\n    let prev = 0;\n    while(true){\n        let next = v.indexOf(a, prev);\n        if (next < 0) break;\n        v[next] = b;\n        prev = next + 1;\n    }\n}\nfunction ens_split(name, preserve_emoji) {\n    return split(name, nfc, preserve_emoji ? (x)=>x.slice() : filter_fe0f); // emoji are exposed so copy\n}\nfunction split(name, nf, ef) {\n    if (!name) return []; // 20230719: empty name allowance\n    init();\n    let offset = 0;\n    // https://unicode.org/reports/tr46/#Validity_Criteria\n    // 4.) \"The label must not contain a U+002E ( . ) FULL STOP.\"\n    return name.split(STOP_CH).map((label)=>{\n        let input = explode_cp(label);\n        let info = {\n            input,\n            offset\n        };\n        offset += input.length + 1; // + stop\n        try {\n            // 1.) \"The label must be in Unicode Normalization Form NFC\"\n            let tokens = info.tokens = tokens_from_str(input, nf, ef);\n            let token_count = tokens.length;\n            let type;\n            if (!token_count) {\n                //norm = [];\n                //type = 'None'; // use this instead of next match, \"ASCII\"\n                // 20230120: change to strict\n                // https://discuss.ens.domains/t/ens-name-normalization-2nd/14564/59\n                throw new Error(`empty label`);\n            }\n            let norm = info.output = tokens.flat();\n            check_leading_underscore(norm);\n            let emoji = info.emoji = token_count > 1 || tokens[0].is_emoji; // same as: tokens.some(x => x.is_emoji);\n            if (!emoji && norm.every((cp)=>cp < 0x80)) {\n                // 20230123: matches matches WHATWG, see note 3.3\n                check_label_extension(norm); // only needed for ascii\n                // cant have fenced\n                // cant have cm\n                // cant have wholes\n                // see derive: \"Fastpath ASCII\"\n                type = \"ASCII\";\n            } else {\n                let chars = tokens.flatMap((x)=>x.is_emoji ? [] : x); // all of the nfc tokens concat together\n                if (!chars.length) {\n                    type = \"Emoji\";\n                } else {\n                    // 5.) \"The label must not begin with a combining mark, that is: General_Category=Mark.\"\n                    if (CM.has(norm[0])) throw error_placement(\"leading combining mark\");\n                    for(let i = 1; i < token_count; i++){\n                        let cps = tokens[i];\n                        if (!cps.is_emoji && CM.has(cps[0])) {\n                            // bidi_qq() not needed since emoji is LTR and cps is a CM\n                            throw error_placement(`emoji + combining mark: \"${str_from_cps(tokens[i - 1])} + ${safe_str_from_cps([\n                                cps[0]\n                            ])}\"`);\n                        }\n                    }\n                    check_fenced(norm);\n                    let unique = Array_from(new Set(chars));\n                    let [g] = determine_group(unique); // take the first match\n                    // see derive: \"Matching Groups have Same CM Style\"\n                    // alternative: could form a hybrid type: Latin/Japanese/...\t\n                    check_group(g, chars); // need text in order\n                    check_whole(g, unique); // only need unique text (order would be required for multiple-char confusables)\n                    type = g.N;\n                // 20230121: consider exposing restricted flag\n                // it's simpler to just check for 'Restricted'\n                // or even better: type.endsWith(']')\n                //if (g.R) info.restricted = true;\n                }\n            }\n            info.type = type;\n        } catch (err) {\n            info.error = err; // use full error object\n        }\n        return info;\n    });\n}\nfunction check_whole(group, unique) {\n    let maker;\n    let shared = [];\n    for (let cp of unique){\n        let whole = WHOLE_MAP.get(cp);\n        if (whole === UNIQUE_PH) return; // unique, non-confusable\n        if (whole) {\n            let set = whole.M.get(cp); // groups which have a character that look-like this character\n            maker = maker ? maker.filter((g)=>set.has(g)) : Array_from(set);\n            if (!maker.length) return; // confusable intersection is empty\n        } else {\n            shared.push(cp);\n        }\n    }\n    if (maker) {\n        // we have 1+ confusable\n        // check if any of the remaining groups\n        // contain the shared characters too\n        for (let g of maker){\n            if (shared.every((cp)=>group_has_cp(g, cp))) {\n                throw new Error(`whole-script confusable: ${group.N}/${g.N}`);\n            }\n        }\n    }\n}\n// assumption: unique.size > 0\n// returns list of matching groups\nfunction determine_group(unique) {\n    let groups = GROUPS;\n    for (let cp of unique){\n        // note: we need to dodge CM that are whitelisted\n        // but that code isn't currently necessary\n        let gs = groups.filter((g)=>group_has_cp(g, cp));\n        if (!gs.length) {\n            if (!GROUPS.some((g)=>group_has_cp(g, cp))) {\n                // the character was composed of valid parts\n                // but it's NFC form is invalid\n                // 20230716: change to more exact statement, see: ENSNormalize.{cs,java}\n                // note: this doesn't have to be a composition\n                // 20230720: change to full check\n                throw error_disallowed(cp); // this should be rare\n            } else {\n                // there is no group that contains all these characters\n                // throw using the highest priority group that matched\n                // https://www.unicode.org/reports/tr39/#mixed_script_confusables\n                throw error_group_member(groups[0], cp);\n            }\n        }\n        groups = gs;\n        if (gs.length == 1) break; // there is only one group left\n    }\n    // there are at least 1 group(s) with all of these characters\n    return groups;\n}\n// throw on first error\nfunction flatten(split) {\n    return split.map(({ input, error, output })=>{\n        if (error) {\n            // don't print label again if just a single label\n            let msg = error.message;\n            // bidi_qq() only necessary if msg is digits\n            throw new Error(split.length == 1 ? msg : `Invalid label ${bidi_qq(safe_str_from_cps(input, 63))}: ${msg}`);\n        }\n        return str_from_cps(output);\n    }).join(STOP_CH);\n}\nfunction error_disallowed(cp) {\n    // TODO: add cp to error?\n    return new Error(`disallowed character: ${quoted_cp(cp)}`);\n}\nfunction error_group_member(g, cp) {\n    let quoted = quoted_cp(cp);\n    let gg = GROUPS.find((g)=>g.P.has(cp)); // only check primary\n    if (gg) {\n        quoted = `${gg.N} ${quoted}`;\n    }\n    return new Error(`illegal mixture: ${g.N} + ${quoted}`);\n}\nfunction error_placement(where) {\n    return new Error(`illegal placement: ${where}`);\n}\n// assumption: cps.length > 0\n// assumption: cps[0] isn't a CM\n// assumption: the previous character isn't an emoji\nfunction check_group(g, cps) {\n    for (let cp of cps){\n        if (!group_has_cp(g, cp)) {\n            // for whitelisted scripts, this will throw illegal mixture on invalid cm, eg. \"e{300}{300}\"\n            // at the moment, it's unnecessary to introduce an extra error type\n            // until there exists a whitelisted multi-character\n            //   eg. if (M < 0 && is_combining_mark(cp)) { ... }\n            // there are 3 cases:\n            //   1. illegal cm for wrong group => mixture error\n            //   2. illegal cm for same group => cm error\n            //       requires set of whitelist cm per group: \n            //        eg. new Set([...g.P, ...g.Q].flatMap(nfc).filter(cp => CM.has(cp)))\n            //   3. wrong group => mixture error\n            throw error_group_member(g, cp);\n        }\n    }\n    //if (M >= 0) { // we have a known fixed cm count\n    if (g.M) {\n        let decomposed = nfd(cps);\n        for(let i = 1, e = decomposed.length; i < e; i++){\n            // 20230210: bugfix: using cps instead of decomposed h/t Carbon225\n            /*\r\n\t\t\tif (CM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\twhile (j < e && CM.has(decomposed[j])) j++;\r\n\t\t\t\tif (j - i > M) {\r\n\t\t\t\t\tthrow new Error(`too many combining marks: ${g.N} ${bidi_qq(str_from_cps(decomposed.slice(i-1, j)))} (${j-i}/${M})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t\t*/ // 20230217: switch to NSM counting\n            // https://www.unicode.org/reports/tr39/#Optional_Detection\n            if (NSM.has(decomposed[i])) {\n                let j = i + 1;\n                for(let cp; j < e && NSM.has(cp = decomposed[j]); j++){\n                    // a. Forbid sequences of the same nonspacing mark.\n                    for(let k = i; k < j; k++){\n                        if (decomposed[k] == cp) {\n                            throw new Error(`duplicate non-spacing marks: ${quoted_cp(cp)}`);\n                        }\n                    }\n                }\n                // parse to end so we have full nsm count\n                // b. Forbid sequences of more than 4 nonspacing marks (gc=Mn or gc=Me).\n                if (j - i > NSM_MAX) {\n                    // note: this slice starts with a base char or spacing-mark cm\n                    throw new Error(`excessive non-spacing marks: ${bidi_qq(safe_str_from_cps(decomposed.slice(i - 1, j)))} (${j - i}/${NSM_MAX})`);\n                }\n                i = j;\n            }\n        }\n    }\n// *** this code currently isn't needed ***\n/*\r\n\tlet cm_whitelist = M instanceof Map;\r\n\tfor (let i = 0, e = cps.length; i < e; ) {\r\n\t\tlet cp = cps[i++];\r\n\t\tlet seqs = cm_whitelist && M.get(cp);\r\n\t\tif (seqs) { \r\n\t\t\t// list of codepoints that can follow\r\n\t\t\t// if this exists, this will always be 1+\r\n\t\t\tlet j = i;\r\n\t\t\twhile (j < e && CM.has(cps[j])) j++;\r\n\t\t\tlet cms = cps.slice(i, j);\r\n\t\t\tlet match = seqs.find(seq => !compare_arrays(seq, cms));\r\n\t\t\tif (!match) throw new Error(`disallowed combining mark sequence: \"${safe_str_from_cps([cp, ...cms])}\"`);\r\n\t\t\ti = j;\r\n\t\t} else if (!V.has(cp)) {\r\n\t\t\t// https://www.unicode.org/reports/tr39/#mixed_script_confusables\r\n\t\t\tlet quoted = quoted_cp(cp);\r\n\t\t\tfor (let cp of cps) {\r\n\t\t\t\tlet u = UNIQUE.get(cp);\r\n\t\t\t\tif (u && u !== g) {\r\n\t\t\t\t\t// if both scripts are restricted this error is confusing\r\n\t\t\t\t\t// because we don't differentiate RestrictedA from RestrictedB \r\n\t\t\t\t\tif (!u.R) quoted = `${quoted} is ${u.N}`;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthrow new Error(`disallowed ${g.N} character: ${quoted}`);\r\n\t\t\t//throw new Error(`disallowed character: ${quoted} (expected ${g.N})`);\r\n\t\t\t//throw new Error(`${g.N} does not allow: ${quoted}`);\r\n\t\t}\r\n\t}\r\n\tif (!cm_whitelist) {\r\n\t\tlet decomposed = nfd(cps);\r\n\t\tfor (let i = 1, e = decomposed.length; i < e; i++) { // we know it can't be cm leading\r\n\t\t\tif (CM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\twhile (j < e && CM.has(decomposed[j])) j++;\r\n\t\t\t\tif (j - i > M) {\r\n\t\t\t\t\tthrow new Error(`too many combining marks: \"${str_from_cps(decomposed.slice(i-1, j))}\" (${j-i}/${M})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t*/ }\n// given a list of codepoints\n// returns a list of lists, where emoji are a fully-qualified (as Array subclass)\n// eg. explode_cp(\"abc💩d\") => [[61, 62, 63], Emoji[1F4A9, FE0F], [64]]\n// 20230818: rename for 'process' name collision h/t Javarome\n// https://github.com/adraffy/ens-normalize.js/issues/23\nfunction tokens_from_str(input, nf, ef) {\n    let ret = [];\n    let chars = [];\n    input = input.slice().reverse(); // flip so we can pop\n    while(input.length){\n        let emoji = consume_emoji_reversed(input);\n        if (emoji) {\n            if (chars.length) {\n                ret.push(nf(chars));\n                chars = [];\n            }\n            ret.push(ef(emoji));\n        } else {\n            let cp = input.pop();\n            if (VALID.has(cp)) {\n                chars.push(cp);\n            } else {\n                let cps = MAPPED.get(cp);\n                if (cps) {\n                    chars.push(...cps); // less than 10 elements\n                } else if (!IGNORED.has(cp)) {\n                    // 20230912: unicode 15.1 changed the order of processing such that\n                    // disallowed parts are only rejected after NFC\n                    // https://unicode.org/reports/tr46/#Validity_Criteria\n                    // this doesn't impact normalization as of today\n                    // technically, this error can be removed as the group logic will apply similar logic\n                    // however the error type might be less clear\n                    throw error_disallowed(cp);\n                }\n            }\n        }\n    }\n    if (chars.length) {\n        ret.push(nf(chars));\n    }\n    return ret;\n}\nfunction filter_fe0f(cps) {\n    return cps.filter((cp)=>cp != FE0F);\n}\n// given array of codepoints\n// returns the longest valid emoji sequence (or undefined if no match)\n// *MUTATES* the supplied array\n// disallows interleaved ignored characters\n// fills (optional) eaten array with matched codepoints\nfunction consume_emoji_reversed(cps, eaten) {\n    let node = EMOJI_ROOT;\n    let emoji;\n    let pos = cps.length;\n    while(pos){\n        node = node.get(cps[--pos]);\n        if (!node) break;\n        let { V } = node;\n        if (V) {\n            emoji = V;\n            if (eaten) eaten.push(...cps.slice(pos).reverse()); // (optional) copy input, used for ens_tokenize()\n            cps.length = pos; // truncate\n        }\n    }\n    return emoji;\n}\n// ************************************************************\n// tokenizer \nconst TY_VALID = \"valid\";\nconst TY_MAPPED = \"mapped\";\nconst TY_IGNORED = \"ignored\";\nconst TY_DISALLOWED = \"disallowed\";\nconst TY_EMOJI = \"emoji\";\nconst TY_NFC = \"nfc\";\nconst TY_STOP = \"stop\";\nfunction ens_tokenize(name, { nf = true } = {}) {\n    init();\n    let input = explode_cp(name).reverse();\n    let eaten = [];\n    let tokens = [];\n    while(input.length){\n        let emoji = consume_emoji_reversed(input, eaten);\n        if (emoji) {\n            tokens.push({\n                type: TY_EMOJI,\n                emoji: emoji.slice(),\n                input: eaten,\n                cps: filter_fe0f(emoji)\n            });\n            eaten = []; // reset buffer\n        } else {\n            let cp = input.pop();\n            if (cp == STOP) {\n                tokens.push({\n                    type: TY_STOP,\n                    cp\n                });\n            } else if (VALID.has(cp)) {\n                tokens.push({\n                    type: TY_VALID,\n                    cps: [\n                        cp\n                    ]\n                });\n            } else if (IGNORED.has(cp)) {\n                tokens.push({\n                    type: TY_IGNORED,\n                    cp\n                });\n            } else {\n                let cps = MAPPED.get(cp);\n                if (cps) {\n                    tokens.push({\n                        type: TY_MAPPED,\n                        cp,\n                        cps: cps.slice()\n                    });\n                } else {\n                    tokens.push({\n                        type: TY_DISALLOWED,\n                        cp\n                    });\n                }\n            }\n        }\n    }\n    if (nf) {\n        for(let i = 0, start = -1; i < tokens.length; i++){\n            let token = tokens[i];\n            if (is_valid_or_mapped(token.type)) {\n                if (requires_check(token.cps)) {\n                    let end = i + 1;\n                    for(let pos = end; pos < tokens.length; pos++){\n                        let { type, cps } = tokens[pos];\n                        if (is_valid_or_mapped(type)) {\n                            if (!requires_check(cps)) break;\n                            end = pos + 1;\n                        } else if (type !== TY_IGNORED) {\n                            break;\n                        }\n                    }\n                    if (start < 0) start = i;\n                    let slice = tokens.slice(start, end);\n                    let cps0 = slice.flatMap((x)=>is_valid_or_mapped(x.type) ? x.cps : []); // strip junk tokens\n                    let cps = nfc(cps0);\n                    if (compare_arrays(cps, cps0)) {\n                        tokens.splice(start, end - start, {\n                            type: TY_NFC,\n                            input: cps0,\n                            cps,\n                            tokens0: collapse_valid_tokens(slice),\n                            tokens: ens_tokenize(str_from_cps(cps), {\n                                nf: false\n                            })\n                        });\n                        i = start;\n                    } else {\n                        i = end - 1; // skip to end of slice\n                    }\n                    start = -1; // reset\n                } else {\n                    start = i; // remember last\n                }\n            } else if (token.type !== TY_IGNORED) {\n                start = -1; // reset\n            }\n        }\n    }\n    return collapse_valid_tokens(tokens);\n}\nfunction is_valid_or_mapped(type) {\n    return type == TY_VALID || type == TY_MAPPED;\n}\nfunction requires_check(cps) {\n    return cps.some((cp)=>NFC_CHECK.has(cp));\n}\nfunction collapse_valid_tokens(tokens) {\n    for(let i = 0; i < tokens.length; i++){\n        if (tokens[i].type == TY_VALID) {\n            let j = i + 1;\n            while(j < tokens.length && tokens[j].type == TY_VALID)j++;\n            tokens.splice(i, j - i, {\n                type: TY_VALID,\n                cps: tokens.slice(i, j).flatMap((x)=>x.cps)\n            });\n        }\n    }\n    return tokens;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@adraffy/ens-normalize/dist/index.mjs\n");

/***/ })

};
;