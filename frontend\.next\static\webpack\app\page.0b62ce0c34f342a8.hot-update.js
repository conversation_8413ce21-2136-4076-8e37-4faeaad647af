"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Contract ABIs - Complete for all DeFi operations\nconst TokenA_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function approve(address spender, uint256 amount) external returns (bool)\",\n    \"function allowance(address owner, address spender) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function transfer(address to, uint256 amount) external returns (bool)\",\n    \"function transferFrom(address from, address to, uint256 amount) external returns (bool)\"\n];\nconst TokenB_ABI = [\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function decimals() external view returns (uint8)\",\n    \"function name() external view returns (string)\",\n    \"function totalSupply() external view returns (uint256)\"\n];\nconst TokenSwap_ABI = [\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getTokenBLiquidity() external view returns (uint256)\",\n    \"function getTokenABalance() external view returns (uint256)\",\n    \"function tokenA() external view returns (address)\",\n    \"function tokenB() external view returns (address)\",\n    \"event Swap(address indexed user, address tokenIn, address tokenOut, uint256 amountIn, uint256 amountOut)\"\n];\nconst LiquidityPool_ABI = [\n    \"function addLiquidity(uint256 amountA, uint256 amountB) external returns (uint256)\",\n    \"function removeLiquidity(uint256 liquidity) external returns (uint256, uint256)\",\n    \"function swap(address tokenIn, uint256 amountIn, uint256 minAmountOut) external returns (uint256)\",\n    \"function getReserves() external view returns (uint256, uint256)\",\n    \"function getAmountOut(address tokenIn, uint256 amountIn) external view returns (uint256)\",\n    \"function balanceOf(address owner) external view returns (uint256)\",\n    \"function totalSupply() external view returns (uint256)\",\n    \"function symbol() external view returns (string)\",\n    \"function name() external view returns (string)\"\n];\nconst Staking_ABI = [\n    \"function stake(uint256 amount) external\",\n    \"function withdraw(uint256 amount) external\",\n    \"function claimReward() external\",\n    \"function exit() external\",\n    \"function compound() external\",\n    \"function multiStake(uint256[] amounts) external\",\n    \"function multiWithdraw(uint256[] amounts) external\",\n    \"function multiClaim(uint256 times) external\",\n    \"function earned(address account) external view returns (uint256)\",\n    \"function balances(address account) external view returns (uint256)\",\n    \"function getStakingInfo(address user) external view returns (uint256, uint256, uint256, uint256)\"\n];\n// Nexus network configuration\nconst NEXUS_NETWORK = {\n    chainId: \"0xF64\",\n    chainName: \"Nexus Testnet III\",\n    nativeCurrency: {\n        name: \"NEX\",\n        symbol: \"NEX\",\n        decimals: 18\n    },\n    rpcUrls: [\n        \"https://testnet3.rpc.nexus.xyz\"\n    ],\n    blockExplorerUrls: [\n        \"https://explorer.nexus.xyz\"\n    ]\n};\n// Default contract addresses (will be replaced with deployed addresses)\nconst DEFAULT_ADDRESSES = {\n    TokenA: \"0x0000000000000000000000000000000000000000\",\n    TokenB: \"0x0000000000000000000000000000000000000000\",\n    TokenSwap: \"0x0000000000000000000000000000000000000000\"\n};\nfunction Home() {\n    var _contractAddresses_deployer;\n    _s();\n    // Basic states\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tokenABalance, setTokenABalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [tokenBBalance, setTokenBBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [lpTokenBalance, setLpTokenBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [stakedBalance, setStakedBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [earnedRewards, setEarnedRewards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contractAddresses, setContractAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_ADDRESSES);\n    const [liquidity, setLiquidity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [isCorrectNetwork, setIsCorrectNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Tab and form states\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"swap\");\n    const [swapAmount, setSwapAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountA, setLiquidityAmountA] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liquidityAmountB, setLiquidityAmountB] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [stakeAmount, setStakeAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Token selection states\n    const [selectedTokenIn, setSelectedTokenIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenA\");\n    const [selectedTokenOut, setSelectedTokenOut] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TokenB\");\n    const [availableTokens, setAvailableTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkIfWalletIsConnected();\n        loadContractAddresses();\n        setupEventListeners();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (account && isCorrectNetwork) {\n            updateBalances();\n            updateLiquidity();\n            loadAvailableTokens();\n        }\n    }, [\n        account,\n        contractAddresses,\n        isCorrectNetwork\n    ]);\n    async function loadAvailableTokens() {\n        if (!account || !isCorrectNetwork) return;\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const tokens = [\n                {\n                    symbol: \"TKNA\",\n                    name: \"Token A\",\n                    address: contractAddresses.TokenA,\n                    balance: tokenABalance,\n                    decimals: 18\n                },\n                {\n                    symbol: \"TKNB\",\n                    name: \"Token B\",\n                    address: contractAddresses.TokenB,\n                    balance: tokenBBalance,\n                    decimals: 18\n                }\n            ];\n            // Add LP token if available\n            if (contractAddresses.LiquidityPool) {\n                tokens.push({\n                    symbol: \"NLP\",\n                    name: \"Nexus LP Token\",\n                    address: contractAddresses.LiquidityPool,\n                    balance: lpTokenBalance,\n                    decimals: 18\n                });\n            }\n            setAvailableTokens(tokens);\n        } catch (error) {\n            console.error(\"Error loading available tokens:\", error);\n        }\n    }\n    function setupEventListeners() {\n        const { ethereum } = window;\n        if (!ethereum) return;\n        // Listen for account changes\n        ethereum.on(\"accountsChanged\", (accounts)=>{\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                checkNetwork();\n            } else {\n                setAccount(\"\");\n                setIsCorrectNetwork(false);\n            }\n        });\n        // Listen for network changes\n        ethereum.on(\"chainChanged\", (chainId)=>{\n            console.log(\"Network changed to:\", chainId);\n            checkNetwork();\n            // Reload page to reset state\n            window.location.reload();\n        });\n        // Cleanup function\n        return ()=>{\n            if (ethereum.removeListener) {\n                ethereum.removeListener(\"accountsChanged\", ()=>{});\n                ethereum.removeListener(\"chainChanged\", ()=>{});\n            }\n        };\n    }\n    async function loadContractAddresses() {\n        try {\n            // Try to load deployed addresses from public folder\n            const response = await fetch(\"/deployedAddresses.json\");\n            if (response.ok) {\n                const addresses = await response.json();\n                setContractAddresses(addresses);\n                console.log(\"Loaded contract addresses:\", addresses);\n            } else {\n                console.warn(\"Could not load deployed addresses, using defaults\");\n            }\n        } catch (error) {\n            console.warn(\"Could not load deployed addresses:\", error);\n        }\n    }\n    async function checkIfWalletIsConnected() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_accounts\"\n            });\n            if (accounts.length > 0) {\n                setAccount(accounts[0]);\n                await checkNetwork();\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setError(\"Error saat mengecek koneksi wallet\");\n        }\n    }\n    async function checkNetwork() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setIsCorrectNetwork(false);\n                return;\n            }\n            const chainId = await ethereum.request({\n                method: \"eth_chainId\"\n            });\n            console.log(\"Current chainId:\", chainId, \"Expected:\", NEXUS_NETWORK.chainId);\n            // Convert both to same format for comparison\n            const currentChainId = parseInt(chainId, 16);\n            const expectedChainId = parseInt(NEXUS_NETWORK.chainId, 16);\n            if (currentChainId === expectedChainId) {\n                setIsCorrectNetwork(true);\n                setError(\"\");\n                console.log(\"✅ Connected to correct network\");\n            } else {\n                setIsCorrectNetwork(false);\n                setError(\"Wrong network. Current: \".concat(currentChainId, \", Expected: \").concat(expectedChainId, \" (Nexus Testnet III)\"));\n                console.log(\"❌ Wrong network detected\");\n            }\n        } catch (error) {\n            console.error(\"Error checking network:\", error);\n            setIsCorrectNetwork(false);\n            setError(\"Error checking network\");\n        }\n    }\n    async function switchToNexusNetwork() {\n        try {\n            const { ethereum } = window;\n            setError(\"\");\n            setSuccess(\"Switching to Nexus network...\");\n            try {\n                await ethereum.request({\n                    method: \"wallet_switchEthereumChain\",\n                    params: [\n                        {\n                            chainId: NEXUS_NETWORK.chainId\n                        }\n                    ]\n                });\n                console.log(\"✅ Network switch requested\");\n            } catch (switchError) {\n                console.log(\"Switch error code:\", switchError.code);\n                // Network belum ditambahkan, tambahkan dulu\n                if (switchError.code === 4902) {\n                    console.log(\"Adding Nexus network...\");\n                    await ethereum.request({\n                        method: \"wallet_addEthereumChain\",\n                        params: [\n                            NEXUS_NETWORK\n                        ]\n                    });\n                    console.log(\"✅ Network added\");\n                } else {\n                    throw switchError;\n                }\n            }\n            // Wait a bit for network to switch\n            setTimeout(async ()=>{\n                await checkNetwork();\n                setSuccess(\"\");\n            }, 1000);\n        } catch (error) {\n            console.error(\"Error switching network:\", error);\n            setError(\"Gagal switch ke Nexus network: \" + (error.message || \"Unknown error\"));\n            setSuccess(\"\");\n        }\n    }\n    async function connectWallet() {\n        try {\n            const { ethereum } = window;\n            if (!ethereum) {\n                setError(\"MetaMask tidak terdeteksi. Silakan install MetaMask.\");\n                return;\n            }\n            const accounts = await ethereum.request({\n                method: \"eth_requestAccounts\"\n            });\n            setAccount(accounts[0]);\n            await checkNetwork();\n            setError(\"\");\n            setSuccess(\"Wallet berhasil terhubung!\");\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n            setError(\"Gagal menghubungkan wallet\");\n        }\n    }\n    async function updateBalances() {\n        if (!account || !isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenA === DEFAULT_ADDRESSES.TokenA) {\n            console.log(\"Contract addresses not loaded yet, skipping balance update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating all balances for:\", account);\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, provider);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, provider);\n            // Get basic token balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            setTokenABalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceA));\n            setTokenBBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(balanceB));\n            // Get LP token balance if LiquidityPool exists\n            if (contractAddresses.LiquidityPool) {\n                const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, provider);\n                const lpBalance = await lpContract.balanceOf(account);\n                setLpTokenBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(lpBalance));\n            }\n            // Get staking info if Staking exists\n            if (contractAddresses.Staking) {\n                const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, provider);\n                const [stakedBal, earned] = await Promise.all([\n                    stakingContract.balances(account),\n                    stakingContract.earned(account)\n                ]);\n                setStakedBalance(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(stakedBal));\n                setEarnedRewards(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(earned));\n            }\n            console.log(\"All balances updated successfully\");\n        } catch (error) {\n            console.error(\"Error updating balances:\", error);\n            setError(\"Error connecting to contracts\");\n        }\n    }\n    async function updateLiquidity() {\n        if (!isCorrectNetwork) return;\n        // Check if contract addresses are valid\n        if (contractAddresses.TokenSwap === DEFAULT_ADDRESSES.TokenSwap) {\n            console.log(\"TokenSwap address not loaded yet, skipping liquidity update\");\n            return;\n        }\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            console.log(\"Updating liquidity for TokenSwap:\", contractAddresses.TokenSwap);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, provider);\n            try {\n                const liquidityAmount = await swapContract.getTokenBLiquidity();\n                console.log(\"Raw liquidity:\", liquidityAmount.toString());\n                setLiquidity(ethers__WEBPACK_IMPORTED_MODULE_4__.formatEther(liquidityAmount));\n                console.log(\"Liquidity updated successfully\");\n            } catch (contractError) {\n                console.error(\"TokenSwap contract call error:\", contractError);\n                setError(\"Error reading liquidity. TokenSwap contract may not be deployed correctly.\");\n            }\n        } catch (error) {\n            console.error(\"Error updating liquidity:\", error);\n            setError(\"Error connecting to TokenSwap contract\");\n        }\n    }\n    async function handleSwap() {\n        if (!swapAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            // Determine which tokens to use\n            const isTokenAToB = selectedTokenIn === \"TokenA\";\n            const inputTokenAddress = isTokenAToB ? contractAddresses.TokenA : contractAddresses.TokenB;\n            const inputTokenContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(inputTokenAddress, TokenA_ABI, signer);\n            // Use LiquidityPool for swapping (more advanced than TokenSwap)\n            const swapContract = contractAddresses.LiquidityPool ? new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer) : new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(swapAmount);\n            // Check balance\n            const balance = await inputTokenContract.balanceOf(account);\n            if (balance < amount) {\n                setError(\"Saldo \".concat(selectedTokenIn === \"TokenA\" ? \"Token A\" : \"Token B\", \" tidak mencukupi\"));\n                return;\n            }\n            // Check allowance\n            const swapAddress = contractAddresses.LiquidityPool || contractAddresses.TokenSwap;\n            const allowance = await inputTokenContract.allowance(account, swapAddress);\n            if (allowance < amount) {\n                setSuccess(\"Menyetujui penggunaan \".concat(selectedTokenIn === \"TokenA\" ? \"Token A\" : \"Token B\", \"...\"));\n                const approveTx = await inputTokenContract.approve(swapAddress, amount);\n                await approveTx.wait();\n                setSuccess(\"Approval berhasil! Melakukan swap...\");\n            } else {\n                setSuccess(\"Melakukan swap...\");\n            }\n            // Perform swap\n            if (contractAddresses.LiquidityPool) {\n                // Use LiquidityPool swap function\n                const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);\n                await swapTx.wait();\n            } else {\n                // Use old TokenSwap (only supports TokenA -> TokenB)\n                if (!isTokenAToB) {\n                    setError(\"TokenSwap hanya mendukung Token A → Token B\");\n                    return;\n                }\n                const swapTx = await swapContract.swap(inputTokenAddress, amount, 0);\n                await swapTx.wait();\n            }\n            setSuccess(\"Swap \".concat(selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" → \").concat(selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" berhasil! \\uD83C\\uDF89\"));\n            setSwapAmount(\"\");\n            // Update balances\n            await updateBalances();\n            await updateLiquidity();\n        } catch (error) {\n            console.error(\"Error during swap:\", error);\n            if (error.code === \"ACTION_REJECTED\") {\n                setError(\"Transaksi dibatalkan oleh user\");\n            } else if (error.message.includes(\"insufficient funds\")) {\n                setError(\"Saldo NEX tidak mencukupi untuk gas fee\");\n            } else {\n                setError(\"Swap gagal: \" + (error.reason || error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleAddLiquidity() {\n        if (!liquidityAmountA || !liquidityAmountB || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const amountA = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountA);\n            const amountB = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(liquidityAmountB);\n            // Check balances\n            const [balanceA, balanceB] = await Promise.all([\n                tokenAContract.balanceOf(account),\n                tokenBContract.balanceOf(account)\n            ]);\n            if (balanceA < amountA) {\n                setError(\"Saldo Token A tidak mencukupi\");\n                return;\n            }\n            if (balanceB < amountB) {\n                setError(\"Saldo Token B tidak mencukupi\");\n                return;\n            }\n            // Approve tokens\n            setSuccess(\"Menyetujui Token A...\");\n            const approveATx = await tokenAContract.approve(contractAddresses.LiquidityPool, amountA);\n            await approveATx.wait();\n            setSuccess(\"Menyetujui Token B...\");\n            const approveBTx = await tokenBContract.approve(contractAddresses.LiquidityPool, amountB);\n            await approveBTx.wait();\n            // Add liquidity\n            setSuccess(\"Menambahkan likuiditas...\");\n            const addLiquidityTx = await lpContract.addLiquidity(amountA, amountB);\n            await addLiquidityTx.wait();\n            setSuccess(\"Likuiditas berhasil ditambahkan! \\uD83C\\uDF89\");\n            setLiquidityAmountA(\"\");\n            setLiquidityAmountB(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error adding liquidity:\", error);\n            setError(\"Gagal menambahkan likuiditas: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleStake() {\n        if (!stakeAmount || !account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const lpContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.LiquidityPool, LiquidityPool_ABI, signer);\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(stakeAmount);\n            // Check LP balance\n            const lpBalance = await lpContract.balanceOf(account);\n            if (lpBalance < amount) {\n                setError(\"Saldo LP Token tidak mencukupi\");\n                return;\n            }\n            // Approve LP tokens\n            setSuccess(\"Menyetujui LP Tokens...\");\n            const approveTx = await lpContract.approve(contractAddresses.Staking, amount);\n            await approveTx.wait();\n            // Stake\n            setSuccess(\"Melakukan stake...\");\n            const stakeTx = await stakingContract.stake(amount);\n            await stakeTx.wait();\n            setSuccess(\"Stake berhasil! \\uD83C\\uDF89\");\n            setStakeAmount(\"\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error staking:\", error);\n            setError(\"Gagal melakukan stake: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleClaim() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const stakingContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.Staking, Staking_ABI, signer);\n            setSuccess(\"Mengklaim rewards...\");\n            const claimTx = await stakingContract.claimReward();\n            await claimTx.wait();\n            setSuccess(\"Rewards berhasil diklaim! \\uD83C\\uDF89\");\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error claiming:\", error);\n            setError(\"Gagal mengklaim rewards: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const clearMessages = ()=>{\n        setError(\"\");\n        setSuccess(\"\");\n    };\n    const isDeployerAccount = account && contractAddresses.deployer && account.toLowerCase() === contractAddresses.deployer.toLowerCase();\n    async function requestTestTokens() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"Requesting test tokens...\");\n        try {\n            // Check if user is the deployer\n            if (isDeployerAccount) {\n                setSuccess(\"You are the deployer! You already have all tokens \\uD83C\\uDF89\");\n                await updateBalances();\n                return;\n            }\n            // For non-deployer users, show instructions\n            setError(\"\");\n            setSuccess(\"\");\n            alert(\"To get test tokens:\\n\\n1. Switch to deployer account: \".concat(contractAddresses.deployer, \"\\n2. Or ask the deployer to send you tokens\\n3. Or use a faucet if available\\n\\nYour current address: \").concat(account, \"\\nDeployer address: \").concat(contractAddresses.deployer, \"\\n\\nThe deployer has 1,000,000 Token A available for distribution.\"));\n        } catch (error) {\n            console.error(\"Error requesting test tokens:\", error);\n            setError(\"Failed to get test tokens: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Batch Operations untuk Maximum Transactions\n    async function handleBatchSwaps() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const swapContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenSwap, TokenSwap_ABI, signer);\n            // Multiple small swaps untuk generate banyak transaksi\n            const amounts = [\n                \"10\",\n                \"20\",\n                \"30\",\n                \"40\",\n                \"50\"\n            ]; // 5 transaksi swap\n            let totalAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"0\");\n            for (const amount of amounts){\n                totalAmount += ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amount);\n            }\n            // Check balance\n            const balance = await tokenAContract.balanceOf(account);\n            if (balance < totalAmount) {\n                setError(\"Saldo Token A tidak mencukupi untuk batch swaps\");\n                return;\n            }\n            // Approve total amount\n            setSuccess(\"Menyetujui total amount untuk batch swaps...\");\n            const approveTx = await tokenAContract.approve(contractAddresses.TokenSwap, totalAmount);\n            await approveTx.wait();\n            // Execute multiple swaps\n            for(let i = 0; i < amounts.length; i++){\n                setSuccess(\"Melakukan swap \".concat(i + 1, \"/\").concat(amounts.length, \"...\"));\n                const amount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(amounts[i]);\n                const swapTx = await swapContract.swap(contractAddresses.TokenA, amount, 0);\n                await swapTx.wait();\n                // Small delay between transactions\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            }\n            setSuccess(\"Batch swaps berhasil! \".concat(amounts.length, \" transaksi completed \\uD83C\\uDF89\"));\n            await updateBalances();\n        } catch (error) {\n            console.error(\"Error during batch swaps:\", error);\n            setError(\"Batch swaps gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    async function handleTransactionSpam() {\n        if (!account || !isCorrectNetwork) return;\n        setLoading(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const { ethereum } = window;\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(ethereum);\n            const signer = await provider.getSigner();\n            const tokenAContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenA, TokenA_ABI, signer);\n            const tokenBContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(contractAddresses.TokenB, TokenB_ABI, signer);\n            // Generate many approval transactions\n            const spamAmount = ethers__WEBPACK_IMPORTED_MODULE_4__.parseEther(\"1\");\n            const contracts = [\n                contractAddresses.TokenSwap,\n                contractAddresses.LiquidityPool,\n                contractAddresses.Staking\n            ];\n            let transactionCount = 0;\n            for (const contractAddr of contracts){\n                // Multiple approvals for each contract\n                for(let i = 0; i < 3; i++){\n                    setSuccess(\"Generating approval transaction \".concat(transactionCount + 1, \"...\"));\n                    const approveTx = await tokenAContract.approve(contractAddr, spamAmount);\n                    await approveTx.wait();\n                    transactionCount++;\n                    // Also approve TokenB\n                    const approveBTx = await tokenBContract.approve(contractAddr, spamAmount);\n                    await approveBTx.wait();\n                    transactionCount++;\n                    // Small delay\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                }\n            }\n            setSuccess(\"Transaction spam completed! \".concat(transactionCount, \" transactions generated \\uD83D\\uDE80\"));\n        } catch (error) {\n            console.error(\"Error during transaction spam:\", error);\n            setError(\"Transaction spam gagal: \" + (error.reason || error.message));\n        } finally{\n            setLoading(false);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-6 right-6 flex flex-col items-end space-y-2\",\n                children: [\n                    account && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Connected:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 806,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            formatAddress(account)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 805,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full \".concat(isCorrectNetwork ? \"bg-green-500\" : \"bg-red-500\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: isCorrectNetwork ? \"Nexus Testnet III\" : \"Wrong Network\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 811,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 809,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 max-w-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Expected Chain: 3940 (0xF64)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 817,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Contracts: \",\n                                    contractAddresses.TokenA !== DEFAULT_ADDRESSES.TokenA ? \"✅\" : \"❌\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Deployer: \",\n                                    contractAddresses.deployer ? contractAddresses.deployer.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 819,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Your Address: \",\n                                    account ? account.slice(0, 8) + \"...\" : \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 820,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 803,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-screen px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"Nexus Swap\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Decentralized token exchange on Nexus blockchain. Swap Token A for Token B at a fixed 1:1 ratio.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 830,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 826,\n                        columnNumber: 9\n                    }, this),\n                    (error || success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mb-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 840,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-red-500 hover:text-red-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-3 flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: success\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearMessages,\n                                        className: \"text-green-500 hover:text-green-700\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 849,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 837,\n                        columnNumber: 11\n                    }, this),\n                    !account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: connectWallet,\n                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Connect your MetaMask wallet to start swapping tokens\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 858,\n                        columnNumber: 11\n                    }, this) : !isCorrectNetwork ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: switchToNexusNetwork,\n                                        className: \"bg-orange-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                        children: \"Switch to Nexus Network\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: checkNetwork,\n                                            className: \"text-sm text-blue-600 hover:text-blue-800 underline\",\n                                            children: \"Already switched? Click to refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 872,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mt-4\",\n                                children: \"Please switch to Nexus Testnet III to continue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 890,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 871,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl shadow-xl p-6 border border-gray-200 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4 text-center\",\n                                        children: \"Portfolio Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-blue-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-blue-600\",\n                                                        children: parseFloat(tokenABalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-purple-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Token B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-purple-600\",\n                                                        children: parseFloat(tokenBBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"TKNB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-green-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-green-600\",\n                                                        children: parseFloat(lpTokenBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"NLP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-orange-50 rounded-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-1\",\n                                                        children: \"Staked\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-mono text-lg font-bold text-orange-600\",\n                                                        children: parseFloat(stakedBalance).toFixed(2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"NLP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 899,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(earnedRewards) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center p-3 bg-yellow-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Pending Rewards\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-mono text-xl font-bold text-yellow-600\",\n                                                children: [\n                                                    parseFloat(earnedRewards).toFixed(4),\n                                                    \" TKNB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 897,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl shadow-xl border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex border-b border-gray-200\",\n                                        children: [\n                                            {\n                                                id: \"swap\",\n                                                label: \"Swap\",\n                                                icon: \"\\uD83D\\uDD04\"\n                                            },\n                                            {\n                                                id: \"liquidity\",\n                                                label: \"Add Liquidity\",\n                                                icon: \"\\uD83D\\uDCA7\"\n                                            },\n                                            {\n                                                id: \"stake\",\n                                                label: \"Stake\",\n                                                icon: \"\\uD83C\\uDFE6\"\n                                            },\n                                            {\n                                                id: \"claim\",\n                                                label: \"Claim\",\n                                                icon: \"\\uD83C\\uDF81\"\n                                            },\n                                            {\n                                                id: \"batch\",\n                                                label: \"Batch Ops\",\n                                                icon: \"⚡\"\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"flex-1 px-4 py-3 text-sm font-medium transition-colors \".concat(activeTab === tab.id ? \"text-blue-600 border-b-2 border-blue-600 bg-blue-50\" : \"text-gray-500 hover:text-gray-700 hover:bg-gray-50\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-2\",\n                                                        children: tab.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    tab.label\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Swap Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 969,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"From\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 975,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: selectedTokenIn,\n                                                                                onChange: (e)=>setSelectedTokenIn(e.target.value),\n                                                                                className: \"flex-1 bg-white border-2 border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                                                disabled: loading,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenA\",\n                                                                                        children: \"TKNA - Token A\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 983,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenB\",\n                                                                                        children: \"TKNB - Token B\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 984,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 977,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"px-3 py-2 bg-gray-100 rounded-lg text-sm\",\n                                                                                children: [\n                                                                                    \"Balance: \",\n                                                                                    selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 986,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 976,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>{\n                                                                        const temp = selectedTokenIn;\n                                                                        setSelectedTokenIn(selectedTokenOut);\n                                                                        setSelectedTokenOut(temp);\n                                                                    },\n                                                                    className: \"p-2 bg-blue-100 hover:bg-blue-200 rounded-full transition-colors\",\n                                                                    disabled: loading,\n                                                                    children: \"\\uD83D\\uDD04\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 994,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"To\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1010,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: selectedTokenOut,\n                                                                                onChange: (e)=>setSelectedTokenOut(e.target.value),\n                                                                                className: \"flex-1 bg-white border-2 border-gray-300 px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                                                disabled: loading,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenB\",\n                                                                                        children: \"TKNB - Token B\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1018,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"TokenA\",\n                                                                                        children: \"TKNA - Token A\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1019,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1012,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"px-3 py-2 bg-gray-100 rounded-lg text-sm\",\n                                                                                children: [\n                                                                                    \"Balance: \",\n                                                                                    selectedTokenOut === \"TokenA\" ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1021,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1011,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1009,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 972,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Amount to Swap\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: swapAmount,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Swap input changed:\", e.target.value);\n                                                                            setSwapAmount(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-4 pr-20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400 shadow-sm\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1034,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                        children: selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1048,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1033,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"Exchange rate: 1 \",\n                                                                            selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\",\n                                                                            \" ≈ 1 \",\n                                                                            selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1053,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(\"10\"),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading,\n                                                                                children: \"10\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1057,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(\"100\"),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading,\n                                                                                children: \"100\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1065,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setSwapAmount(selectedTokenIn === \"TokenA\" ? tokenABalance : tokenBBalance),\n                                                                                className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200\",\n                                                                                disabled: loading || (selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance) === 0 : parseFloat(tokenBBalance) === 0),\n                                                                                children: \"Max\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1073,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1056,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1052,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    \"Available: \",\n                                                                    selectedTokenIn === \"TokenA\" ? parseFloat(tokenABalance).toFixed(4) : parseFloat(tokenBBalance).toFixed(4),\n                                                                    \" \",\n                                                                    selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\",\n                                                                    ' | Input: \"',\n                                                                    swapAmount,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1083,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Pool Liquidity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1090,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-mono text-lg font-semibold text-gray-800\",\n                                                                children: [\n                                                                    parseFloat(liquidity).toFixed(2),\n                                                                    \" Token B\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1091,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1089,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleSwap,\n                                                        disabled: loading || !swapAmount || parseFloat(swapAmount) <= 0,\n                                                        className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1105,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Processing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1104,\n                                                            columnNumber: 25\n                                                        }, this) : \"Swap \".concat(selectedTokenIn === \"TokenA\" ? \"TKNA\" : \"TKNB\", \" → \").concat(selectedTokenOut === \"TokenA\" ? \"TKNA\" : \"TKNB\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1097,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"liquidity\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1117,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token A Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1121,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: liquidityAmountA,\n                                                                                onChange: (e)=>{\n                                                                                    console.log(\"Liquidity A input changed:\", e.target.value);\n                                                                                    setLiquidityAmountA(e.target.value);\n                                                                                },\n                                                                                placeholder: \"0.0\",\n                                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                                disabled: loading,\n                                                                                min: \"0\",\n                                                                                step: \"0.01\",\n                                                                                autoComplete: \"off\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1125,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                                children: \"TKNA\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1139,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1124,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Available: \",\n                                                                            parseFloat(tokenABalance).toFixed(4),\n                                                                            ' TKNA | Current: \"',\n                                                                            liquidityAmountA,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1143,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1120,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                        children: \"Token B Amount\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1149,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                value: liquidityAmountB,\n                                                                                onChange: (e)=>{\n                                                                                    console.log(\"Liquidity B input changed:\", e.target.value);\n                                                                                    setLiquidityAmountB(e.target.value);\n                                                                                },\n                                                                                placeholder: \"0.0\",\n                                                                                className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                                disabled: loading,\n                                                                                min: \"0\",\n                                                                                step: \"0.01\",\n                                                                                autoComplete: \"off\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1153,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                                children: \"TKNB\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1167,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1152,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Available: \",\n                                                                            parseFloat(tokenBBalance).toFixed(4),\n                                                                            ' TKNB | Current: \"',\n                                                                            liquidityAmountB,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1171,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1148,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1119,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleAddLiquidity,\n                                                        disabled: loading || !liquidityAmountA || !liquidityAmountB,\n                                                        className: \"w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1177,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1116,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"stake\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1190,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Stake Amount (LP Tokens)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1193,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: stakeAmount,\n                                                                        onChange: (e)=>{\n                                                                            console.log(\"Stake input changed:\", e.target.value);\n                                                                            setStakeAmount(e.target.value);\n                                                                        },\n                                                                        placeholder: \"0.0\",\n                                                                        className: \"w-full bg-white border-2 border-gray-300 px-4 py-3 pr-16 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg text-gray-900 placeholder-gray-400\",\n                                                                        disabled: loading,\n                                                                        min: \"0\",\n                                                                        step: \"0.01\",\n                                                                        autoComplete: \"off\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1197,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium pointer-events-none\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1211,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1196,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    \"Available: \",\n                                                                    parseFloat(lpTokenBalance).toFixed(4),\n                                                                    ' NLP | Current: \"',\n                                                                    stakeAmount,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1215,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1192,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleStake,\n                                                        disabled: loading || !stakeAmount || parseFloat(lpTokenBalance) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-orange-600 to-red-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-orange-700 hover:to-red-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Stake LP Tokens\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1220,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1189,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"claim\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1233,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6 p-4 bg-yellow-50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mb-2\",\n                                                                children: \"Pending Rewards\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1236,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-mono text-3xl font-bold text-yellow-600\",\n                                                                children: parseFloat(earnedRewards).toFixed(6)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1237,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"TKNB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1240,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1235,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Staked\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1245,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold\",\n                                                                        children: parseFloat(stakedBalance).toFixed(4)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1246,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"NLP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1249,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1244,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"APY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1252,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-mono text-lg font-semibold text-green-600\",\n                                                                        children: \"~50%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1253,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: \"Estimated\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1256,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1251,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleClaim,\n                                                        disabled: loading || parseFloat(earnedRewards) === 0,\n                                                        className: \"w-full bg-gradient-to-r from-yellow-600 to-orange-600 text-white py-4 rounded-xl text-lg font-semibold hover:from-yellow-700 hover:to-orange-700 transition-all duration-200 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: loading ? \"Processing...\" : \"Claim Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1260,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1232,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === \"batch\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-4\",\n                                                        children: \"Batch Operations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1273,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-6\",\n                                                        children: \"Generate multiple transactions for maximum Nexus testnet interaction!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1274,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-blue-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-blue-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDD04 Batch Swaps\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1281,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-blue-700 mb-3\",\n                                                                        children: \"Execute 5 separate swap transactions (10, 20, 30, 40, 50 TKNA)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1282,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleBatchSwaps,\n                                                                        disabled: loading || parseFloat(tokenABalance) < 150,\n                                                                        className: \"w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Execute Batch Swaps (5 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1285,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 mt-1\",\n                                                                        children: \"Requires: 150 TKNA minimum\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1293,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1280,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-purple-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-purple-800 mb-2\",\n                                                                        children: \"⚡ Transaction Spam\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1300,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-purple-700 mb-3\",\n                                                                        children: \"Generate 18 approval transactions across all contracts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1301,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: handleTransactionSpam,\n                                                                        disabled: loading,\n                                                                        className: \"w-full bg-purple-600 text-white py-3 rounded-lg font-medium hover:bg-purple-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                                        children: loading ? \"Processing...\" : \"Generate Transaction Spam (18 TXs)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1304,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-purple-600 mt-1\",\n                                                                        children: \"Generates many approval transactions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1312,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1299,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-green-50 rounded-xl text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-green-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCCA Transaction Counter\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1319,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-700 mb-2\",\n                                                                        children: \"Estimated transactions per full cycle:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1320,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"Basic Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1325,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"8-10 TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1326,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1324,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-white p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-semibold\",\n                                                                                        children: \"With Batches\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1329,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: \"25+ TXs\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1330,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1328,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1323,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1318,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 bg-yellow-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-800 mb-2\",\n                                                                        children: \"\\uD83D\\uDCA1 Pro Tips\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1337,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-sm text-yellow-700 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Use batch operations to generate many transactions quickly\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1339,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Each operation creates multiple blockchain interactions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1340,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Perfect for maximizing Nexus testnet contribution\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1341,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Monitor your transaction count in MetaMask\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1342,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1338,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1336,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1278,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1272,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 966,\n                                        columnNumber: 15\n                                    }, this),\n                                    parseFloat(tokenABalance) === 0 && activeTab === \"swap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-6 mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-yellow-800 mb-2\",\n                                                children: \"Need Test Tokens?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1353,\n                                                columnNumber: 19\n                                            }, this),\n                                            isDeployerAccount ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"You are the deployer! You have 1,000,000 Token A available.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1356,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: updateBalances,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200\",\n                                                        children: \"Refresh Balance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1359,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1355,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-700 mb-3\",\n                                                        children: \"To get test tokens, switch to the deployer account or ask for a transfer:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1369,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-yellow-600 mb-3 font-mono bg-yellow-100 p-2 rounded\",\n                                                        children: [\n                                                            \"Deployer: \",\n                                                            (_contractAddresses_deployer = contractAddresses.deployer) === null || _contractAddresses_deployer === void 0 ? void 0 : _contractAddresses_deployer.slice(0, 20),\n                                                            \"...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1372,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: requestTestTokens,\n                                                        disabled: loading,\n                                                        className: \"bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-all duration-200 disabled:bg-gray-400\",\n                                                        children: \"Show Instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1375,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1368,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1352,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 940,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-2\",\n                                        children: \"DeFi Flow Guide:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1391,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"1. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Swap\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1393,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Trade Token A ⇄ Token B\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1393,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"2. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Add Liquidity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1394,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Provide both tokens → Get LP tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1394,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"3. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Stake\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1395,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Stake LP tokens → Earn rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1395,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"4. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Claim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1396,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": Collect your earned rewards\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1396,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"5. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Repeat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1397,\n                                                        columnNumber: 24\n                                                    }, this),\n                                                    \": More transactions = More Nexus interaction!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1397,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1390,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 895,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 825,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\nexus\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 801,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Xn9OxmB2iOgPw0us/9Bh41mAbgc=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ1o7QUFFaEMsbURBQW1EO0FBQ25ELE1BQU1HLGFBQWE7SUFDakI7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0Q7QUFFRCxNQUFNQyxhQUFhO0lBQ2pCO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRDtBQUVELE1BQU1DLGdCQUFnQjtJQUNwQjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRDtBQUVELE1BQU1DLG9CQUFvQjtJQUN4QjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRDtBQUVELE1BQU1DLGNBQWM7SUFDbEI7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtDQUNEO0FBRUQsOEJBQThCO0FBQzlCLE1BQU1DLGdCQUFnQjtJQUNwQkMsU0FBUztJQUNUQyxXQUFXO0lBQ1hDLGdCQUFnQjtRQUNkQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsVUFBVTtJQUNaO0lBQ0FDLFNBQVM7UUFBQztLQUFpQztJQUMzQ0MsbUJBQW1CO1FBQUM7S0FBNkI7QUFDbkQ7QUFFQSx3RUFBd0U7QUFDeEUsTUFBTUMsb0JBQW9CO0lBQ3hCQyxRQUFRO0lBQ1JDLFFBQVE7SUFDUkMsV0FBVztBQUNiO0FBZWUsU0FBU0M7UUE4dkNXQzs7SUE3dkNqQyxlQUFlO0lBQ2YsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUd4QiwrQ0FBUUEsQ0FBUztJQUMvQyxNQUFNLENBQUN5QixlQUFlQyxpQkFBaUIsR0FBRzFCLCtDQUFRQSxDQUFTO0lBQzNELE1BQU0sQ0FBQzJCLGVBQWVDLGlCQUFpQixHQUFHNUIsK0NBQVFBLENBQVM7SUFDM0QsTUFBTSxDQUFDNkIsZ0JBQWdCQyxrQkFBa0IsR0FBRzlCLCtDQUFRQSxDQUFTO0lBQzdELE1BQU0sQ0FBQytCLGVBQWVDLGlCQUFpQixHQUFHaEMsK0NBQVFBLENBQVM7SUFDM0QsTUFBTSxDQUFDaUMsZUFBZUMsaUJBQWlCLEdBQUdsQywrQ0FBUUEsQ0FBUztJQUMzRCxNQUFNLENBQUNtQyxTQUFTQyxXQUFXLEdBQUdwQywrQ0FBUUEsQ0FBVTtJQUNoRCxNQUFNLENBQUNxQyxPQUFPQyxTQUFTLEdBQUd0QywrQ0FBUUEsQ0FBUztJQUMzQyxNQUFNLENBQUN1QyxTQUFTQyxXQUFXLEdBQUd4QywrQ0FBUUEsQ0FBUztJQUMvQyxNQUFNLENBQUNzQixtQkFBbUJtQixxQkFBcUIsR0FBR3pDLCtDQUFRQSxDQUFvQmlCO0lBQzlFLE1BQU0sQ0FBQ3lCLFdBQVdDLGFBQWEsR0FBRzNDLCtDQUFRQSxDQUFTO0lBQ25ELE1BQU0sQ0FBQzRDLGtCQUFrQkMsb0JBQW9CLEdBQUc3QywrQ0FBUUEsQ0FBVTtJQUVsRSxzQkFBc0I7SUFDdEIsTUFBTSxDQUFDOEMsV0FBV0MsYUFBYSxHQUFHL0MsK0NBQVFBLENBQVM7SUFDbkQsTUFBTSxDQUFDZ0QsWUFBWUMsY0FBYyxHQUFHakQsK0NBQVFBLENBQVM7SUFDckQsTUFBTSxDQUFDa0Qsa0JBQWtCQyxvQkFBb0IsR0FBR25ELCtDQUFRQSxDQUFTO0lBQ2pFLE1BQU0sQ0FBQ29ELGtCQUFrQkMsb0JBQW9CLEdBQUdyRCwrQ0FBUUEsQ0FBUztJQUNqRSxNQUFNLENBQUNzRCxhQUFhQyxlQUFlLEdBQUd2RCwrQ0FBUUEsQ0FBUztJQUN2RCxNQUFNLENBQUN3RCxnQkFBZ0JDLGtCQUFrQixHQUFHekQsK0NBQVFBLENBQVM7SUFFN0QseUJBQXlCO0lBQ3pCLE1BQU0sQ0FBQzBELGlCQUFpQkMsbUJBQW1CLEdBQUczRCwrQ0FBUUEsQ0FBUztJQUMvRCxNQUFNLENBQUM0RCxrQkFBa0JDLG9CQUFvQixHQUFHN0QsK0NBQVFBLENBQVM7SUFDakUsTUFBTSxDQUFDOEQsaUJBQWlCQyxtQkFBbUIsR0FBRy9ELCtDQUFRQSxDQUFRLEVBQUU7SUFFaEVDLGdEQUFTQSxDQUFDO1FBQ1IrRDtRQUNBQztRQUNBQztJQUNGLEdBQUcsRUFBRTtJQUVMakUsZ0RBQVNBLENBQUM7UUFDUixJQUFJc0IsV0FBV3FCLGtCQUFrQjtZQUMvQnVCO1lBQ0FDO1lBQ0FDO1FBQ0Y7SUFDRixHQUFHO1FBQUM5QztRQUFTRDtRQUFtQnNCO0tBQWlCO0lBRWpELGVBQWV5QjtRQUNiLElBQUksQ0FBQzlDLFdBQVcsQ0FBQ3FCLGtCQUFrQjtRQUVuQyxJQUFJO1lBQ0YsTUFBTSxFQUFFMEIsUUFBUSxFQUFFLEdBQUdDO1lBQ3JCLE1BQU1DLFdBQVcsSUFBSXRFLG1EQUFzQixDQUFDb0U7WUFFNUMsTUFBTUksU0FBUztnQkFDYjtvQkFDRTdELFFBQVE7b0JBQ1JELE1BQU07b0JBQ04rRCxTQUFTckQsa0JBQWtCSixNQUFNO29CQUNqQzBELFNBQVNuRDtvQkFDVFgsVUFBVTtnQkFDWjtnQkFDQTtvQkFDRUQsUUFBUTtvQkFDUkQsTUFBTTtvQkFDTitELFNBQVNyRCxrQkFBa0JILE1BQU07b0JBQ2pDeUQsU0FBU2pEO29CQUNUYixVQUFVO2dCQUNaO2FBQ0Q7WUFFRCw0QkFBNEI7WUFDNUIsSUFBSVEsa0JBQWtCdUQsYUFBYSxFQUFFO2dCQUNuQ0gsT0FBT0ksSUFBSSxDQUFDO29CQUNWakUsUUFBUTtvQkFDUkQsTUFBTTtvQkFDTitELFNBQVNyRCxrQkFBa0J1RCxhQUFhO29CQUN4Q0QsU0FBUy9DO29CQUNUZixVQUFVO2dCQUNaO1lBQ0Y7WUFFQWlELG1CQUFtQlc7UUFDckIsRUFBRSxPQUFPckMsT0FBTztZQUNkMEMsUUFBUTFDLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ25EO0lBQ0Y7SUFFQSxTQUFTNkI7UUFDUCxNQUFNLEVBQUVJLFFBQVEsRUFBRSxHQUFHQztRQUNyQixJQUFJLENBQUNELFVBQVU7UUFFZiw2QkFBNkI7UUFDN0JBLFNBQVNVLEVBQUUsQ0FBQyxtQkFBbUIsQ0FBQ0M7WUFDOUIsSUFBSUEsU0FBU0MsTUFBTSxHQUFHLEdBQUc7Z0JBQ3ZCMUQsV0FBV3lELFFBQVEsQ0FBQyxFQUFFO2dCQUN0QkU7WUFDRixPQUFPO2dCQUNMM0QsV0FBVztnQkFDWHFCLG9CQUFvQjtZQUN0QjtRQUNGO1FBRUEsNkJBQTZCO1FBQzdCeUIsU0FBU1UsRUFBRSxDQUFDLGdCQUFnQixDQUFDdkU7WUFDM0JzRSxRQUFRSyxHQUFHLENBQUMsdUJBQXVCM0U7WUFDbkMwRTtZQUNBLDZCQUE2QjtZQUM3QlosT0FBT2MsUUFBUSxDQUFDQyxNQUFNO1FBQ3hCO1FBRUEsbUJBQW1CO1FBQ25CLE9BQU87WUFDTCxJQUFJaEIsU0FBU2lCLGNBQWMsRUFBRTtnQkFDM0JqQixTQUFTaUIsY0FBYyxDQUFDLG1CQUFtQixLQUFPO2dCQUNsRGpCLFNBQVNpQixjQUFjLENBQUMsZ0JBQWdCLEtBQU87WUFDakQ7UUFDRjtJQUNGO0lBRUEsZUFBZXRCO1FBQ2IsSUFBSTtZQUNGLG9EQUFvRDtZQUNwRCxNQUFNdUIsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLElBQUlELFNBQVNFLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxZQUFZLE1BQU1ILFNBQVNJLElBQUk7Z0JBQ3JDbkQscUJBQXFCa0Q7Z0JBQ3JCWixRQUFRSyxHQUFHLENBQUMsOEJBQThCTztZQUM1QyxPQUFPO2dCQUNMWixRQUFRYyxJQUFJLENBQUM7WUFDZjtRQUNGLEVBQUUsT0FBT3hELE9BQU87WUFDZDBDLFFBQVFjLElBQUksQ0FBQyxzQ0FBc0N4RDtRQUNyRDtJQUNGO0lBRUEsZUFBZTJCO1FBQ2IsSUFBSTtZQUNGLE1BQU0sRUFBRU0sUUFBUSxFQUFFLEdBQUdDO1lBQ3JCLElBQUksQ0FBQ0QsVUFBVTtnQkFDYmhDLFNBQVM7Z0JBQ1Q7WUFDRjtZQUVBLE1BQU0yQyxXQUFXLE1BQU1YLFNBQVN3QixPQUFPLENBQUM7Z0JBQUVDLFFBQVE7WUFBZTtZQUNqRSxJQUFJZCxTQUFTQyxNQUFNLEdBQUcsR0FBRztnQkFDdkIxRCxXQUFXeUQsUUFBUSxDQUFDLEVBQUU7Z0JBQ3RCLE1BQU1FO1lBQ1I7UUFDRixFQUFFLE9BQU85QyxPQUFPO1lBQ2QwQyxRQUFRMUMsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkRDLFNBQVM7UUFDWDtJQUNGO0lBRUEsZUFBZTZDO1FBQ2IsSUFBSTtZQUNGLE1BQU0sRUFBRWIsUUFBUSxFQUFFLEdBQUdDO1lBQ3JCLElBQUksQ0FBQ0QsVUFBVTtnQkFDYnpCLG9CQUFvQjtnQkFDcEI7WUFDRjtZQUVBLE1BQU1wQyxVQUFVLE1BQU02RCxTQUFTd0IsT0FBTyxDQUFDO2dCQUFFQyxRQUFRO1lBQWM7WUFDL0RoQixRQUFRSyxHQUFHLENBQUMsb0JBQW9CM0UsU0FBUyxhQUFhRCxjQUFjQyxPQUFPO1lBRTNFLDZDQUE2QztZQUM3QyxNQUFNdUYsaUJBQWlCQyxTQUFTeEYsU0FBUztZQUN6QyxNQUFNeUYsa0JBQWtCRCxTQUFTekYsY0FBY0MsT0FBTyxFQUFFO1lBRXhELElBQUl1RixtQkFBbUJFLGlCQUFpQjtnQkFDdENyRCxvQkFBb0I7Z0JBQ3BCUCxTQUFTO2dCQUNUeUMsUUFBUUssR0FBRyxDQUFDO1lBQ2QsT0FBTztnQkFDTHZDLG9CQUFvQjtnQkFDcEJQLFNBQVMsMkJBQXdENEQsT0FBN0JGLGdCQUFlLGdCQUE4QixPQUFoQkUsaUJBQWdCO2dCQUNqRm5CLFFBQVFLLEdBQUcsQ0FBQztZQUNkO1FBQ0YsRUFBRSxPQUFPL0MsT0FBTztZQUNkMEMsUUFBUTFDLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDUSxvQkFBb0I7WUFDcEJQLFNBQVM7UUFDWDtJQUNGO0lBRUEsZUFBZTZEO1FBQ2IsSUFBSTtZQUNGLE1BQU0sRUFBRTdCLFFBQVEsRUFBRSxHQUFHQztZQUNyQmpDLFNBQVM7WUFDVEUsV0FBVztZQUVYLElBQUk7Z0JBQ0YsTUFBTThCLFNBQVN3QixPQUFPLENBQUM7b0JBQ3JCQyxRQUFRO29CQUNSSyxRQUFRO3dCQUFDOzRCQUFFM0YsU0FBU0QsY0FBY0MsT0FBTzt3QkFBQztxQkFBRTtnQkFDOUM7Z0JBQ0FzRSxRQUFRSyxHQUFHLENBQUM7WUFDZCxFQUFFLE9BQU9pQixhQUFrQjtnQkFDekJ0QixRQUFRSyxHQUFHLENBQUMsc0JBQXNCaUIsWUFBWUMsSUFBSTtnQkFDbEQsNENBQTRDO2dCQUM1QyxJQUFJRCxZQUFZQyxJQUFJLEtBQUssTUFBTTtvQkFDN0J2QixRQUFRSyxHQUFHLENBQUM7b0JBQ1osTUFBTWQsU0FBU3dCLE9BQU8sQ0FBQzt3QkFDckJDLFFBQVE7d0JBQ1JLLFFBQVE7NEJBQUM1Rjt5QkFBYztvQkFDekI7b0JBQ0F1RSxRQUFRSyxHQUFHLENBQUM7Z0JBQ2QsT0FBTztvQkFDTCxNQUFNaUI7Z0JBQ1I7WUFDRjtZQUVBLG1DQUFtQztZQUNuQ0UsV0FBVztnQkFDVCxNQUFNcEI7Z0JBQ04zQyxXQUFXO1lBQ2IsR0FBRztRQUVMLEVBQUUsT0FBT0gsT0FBWTtZQUNuQjBDLFFBQVExQyxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQ0MsU0FBUyxvQ0FBcUNELENBQUFBLE1BQU1tRSxPQUFPLElBQUksZUFBYztZQUM3RWhFLFdBQVc7UUFDYjtJQUNGO0lBRUEsZUFBZWlFO1FBQ2IsSUFBSTtZQUNGLE1BQU0sRUFBRW5DLFFBQVEsRUFBRSxHQUFHQztZQUNyQixJQUFJLENBQUNELFVBQVU7Z0JBQ2JoQyxTQUFTO2dCQUNUO1lBQ0Y7WUFFQSxNQUFNMkMsV0FBVyxNQUFNWCxTQUFTd0IsT0FBTyxDQUFDO2dCQUFFQyxRQUFRO1lBQXNCO1lBQ3hFdkUsV0FBV3lELFFBQVEsQ0FBQyxFQUFFO1lBQ3RCLE1BQU1FO1lBQ043QyxTQUFTO1lBQ1RFLFdBQVc7UUFDYixFQUFFLE9BQU9ILE9BQU87WUFDZDBDLFFBQVExQyxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQ0MsU0FBUztRQUNYO0lBQ0Y7SUFFQSxlQUFlNkI7UUFDYixJQUFJLENBQUM1QyxXQUFXLENBQUNxQixrQkFBa0I7UUFFbkMsd0NBQXdDO1FBQ3hDLElBQUl0QixrQkFBa0JKLE1BQU0sS0FBS0Qsa0JBQWtCQyxNQUFNLEVBQUU7WUFDekQ2RCxRQUFRSyxHQUFHLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSTtZQUNGLE1BQU0sRUFBRWQsUUFBUSxFQUFFLEdBQUdDO1lBQ3JCLE1BQU1DLFdBQVcsSUFBSXRFLG1EQUFzQixDQUFDb0U7WUFFNUNTLFFBQVFLLEdBQUcsQ0FBQyw4QkFBOEI3RDtZQUUxQyxNQUFNbUYsaUJBQWlCLElBQUl4Ryw0Q0FBZSxDQUFDb0Isa0JBQWtCSixNQUFNLEVBQUVmLFlBQVlxRTtZQUNqRixNQUFNb0MsaUJBQWlCLElBQUkxRyw0Q0FBZSxDQUFDb0Isa0JBQWtCSCxNQUFNLEVBQUVmLFlBQVlvRTtZQUVqRiwyQkFBMkI7WUFDM0IsTUFBTSxDQUFDcUMsVUFBVUMsU0FBUyxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztnQkFDN0NOLGVBQWVPLFNBQVMsQ0FBQzFGO2dCQUN6QnFGLGVBQWVLLFNBQVMsQ0FBQzFGO2FBQzFCO1lBRURHLGlCQUFpQnhCLCtDQUFrQixDQUFDMkc7WUFDcENqRixpQkFBaUIxQiwrQ0FBa0IsQ0FBQzRHO1lBRXBDLCtDQUErQztZQUMvQyxJQUFJeEYsa0JBQWtCdUQsYUFBYSxFQUFFO2dCQUNuQyxNQUFNc0MsYUFBYSxJQUFJakgsNENBQWUsQ0FBQ29CLGtCQUFrQnVELGFBQWEsRUFBRXZFLG1CQUFtQmtFO2dCQUMzRixNQUFNNEMsWUFBWSxNQUFNRCxXQUFXRixTQUFTLENBQUMxRjtnQkFDN0NPLGtCQUFrQjVCLCtDQUFrQixDQUFDa0g7WUFDdkM7WUFFQSxxQ0FBcUM7WUFDckMsSUFBSTlGLGtCQUFrQitGLE9BQU8sRUFBRTtnQkFDN0IsTUFBTUMsa0JBQWtCLElBQUlwSCw0Q0FBZSxDQUFDb0Isa0JBQWtCK0YsT0FBTyxFQUFFOUcsYUFBYWlFO2dCQUNwRixNQUFNLENBQUMrQyxXQUFXQyxPQUFPLEdBQUcsTUFBTVQsUUFBUUMsR0FBRyxDQUFDO29CQUM1Q00sZ0JBQWdCRyxRQUFRLENBQUNsRztvQkFDekIrRixnQkFBZ0JFLE1BQU0sQ0FBQ2pHO2lCQUN4QjtnQkFDRFMsaUJBQWlCOUIsK0NBQWtCLENBQUNxSDtnQkFDcENyRixpQkFBaUJoQywrQ0FBa0IsQ0FBQ3NIO1lBQ3RDO1lBRUF6QyxRQUFRSyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU8vQyxPQUFPO1lBQ2QwQyxRQUFRMUMsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUNDLFNBQVM7UUFDWDtJQUNGO0lBRUEsZUFBZThCO1FBQ2IsSUFBSSxDQUFDeEIsa0JBQWtCO1FBRXZCLHdDQUF3QztRQUN4QyxJQUFJdEIsa0JBQWtCRixTQUFTLEtBQUtILGtCQUFrQkcsU0FBUyxFQUFFO1lBQy9EMkQsUUFBUUssR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNLEVBQUVkLFFBQVEsRUFBRSxHQUFHQztZQUNyQixNQUFNQyxXQUFXLElBQUl0RSxtREFBc0IsQ0FBQ29FO1lBRTVDUyxRQUFRSyxHQUFHLENBQUMscUNBQXFDOUQsa0JBQWtCRixTQUFTO1lBRTVFLE1BQU1zRyxlQUFlLElBQUl4SCw0Q0FBZSxDQUFDb0Isa0JBQWtCRixTQUFTLEVBQUVmLGVBQWVtRTtZQUVyRixJQUFJO2dCQUNGLE1BQU1tRCxrQkFBa0IsTUFBTUQsYUFBYUUsa0JBQWtCO2dCQUM3RDdDLFFBQVFLLEdBQUcsQ0FBQyxrQkFBa0J1QyxnQkFBZ0JFLFFBQVE7Z0JBRXREbEYsYUFBYXpDLCtDQUFrQixDQUFDeUg7Z0JBQ2hDNUMsUUFBUUssR0FBRyxDQUFDO1lBQ2QsRUFBRSxPQUFPMEMsZUFBZTtnQkFDdEIvQyxRQUFRMUMsS0FBSyxDQUFDLGtDQUFrQ3lGO2dCQUNoRHhGLFNBQVM7WUFDWDtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkMEMsUUFBUTFDLEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDQyxTQUFTO1FBQ1g7SUFDRjtJQUVBLGVBQWV5RjtRQUNiLElBQUksQ0FBQy9FLGNBQWMsQ0FBQ3pCLFdBQVcsQ0FBQ3FCLGtCQUFrQjtRQUVsRFIsV0FBVztRQUNYRSxTQUFTO1FBQ1RFLFdBQVc7UUFFWCxJQUFJO1lBQ0YsTUFBTSxFQUFFOEIsUUFBUSxFQUFFLEdBQUdDO1lBQ3JCLE1BQU1DLFdBQVcsSUFBSXRFLG1EQUFzQixDQUFDb0U7WUFDNUMsTUFBTTBELFNBQVMsTUFBTXhELFNBQVN5RCxTQUFTO1lBRXZDLGdDQUFnQztZQUNoQyxNQUFNQyxjQUFjeEUsb0JBQW9CO1lBQ3hDLE1BQU15RSxvQkFBb0JELGNBQWM1RyxrQkFBa0JKLE1BQU0sR0FBR0ksa0JBQWtCSCxNQUFNO1lBQzNGLE1BQU1pSCxxQkFBcUIsSUFBSWxJLDRDQUFlLENBQUNpSSxtQkFBbUJoSSxZQUFZNkg7WUFFOUUsZ0VBQWdFO1lBQ2hFLE1BQU1OLGVBQWVwRyxrQkFBa0J1RCxhQUFhLEdBQ2hELElBQUkzRSw0Q0FBZSxDQUFDb0Isa0JBQWtCdUQsYUFBYSxFQUFFdkUsbUJBQW1CMEgsVUFDeEUsSUFBSTlILDRDQUFlLENBQUNvQixrQkFBa0JGLFNBQVMsRUFBRWYsZUFBZTJIO1lBRXBFLE1BQU1LLFNBQVNuSSw4Q0FBaUIsQ0FBQzhDO1lBRWpDLGdCQUFnQjtZQUNoQixNQUFNNEIsVUFBVSxNQUFNd0QsbUJBQW1CbkIsU0FBUyxDQUFDMUY7WUFDbkQsSUFBSXFELFVBQVV5RCxRQUFRO2dCQUNwQi9GLFNBQVMsU0FBOEQsT0FBckRvQixvQkFBb0IsV0FBVyxZQUFZLFdBQVU7Z0JBQ3ZFO1lBQ0Y7WUFFQSxrQkFBa0I7WUFDbEIsTUFBTTZFLGNBQWNqSCxrQkFBa0J1RCxhQUFhLElBQUl2RCxrQkFBa0JGLFNBQVM7WUFDbEYsTUFBTW9ILFlBQVksTUFBTUosbUJBQW1CSSxTQUFTLENBQUNqSCxTQUFTZ0g7WUFFOUQsSUFBSUMsWUFBWUgsUUFBUTtnQkFDdEI3RixXQUFXLHlCQUE4RSxPQUFyRGtCLG9CQUFvQixXQUFXLFlBQVksV0FBVTtnQkFDekYsTUFBTStFLFlBQVksTUFBTUwsbUJBQW1CTSxPQUFPLENBQUNILGFBQWFGO2dCQUNoRSxNQUFNSSxVQUFVRSxJQUFJO2dCQUNwQm5HLFdBQVc7WUFDYixPQUFPO2dCQUNMQSxXQUFXO1lBQ2I7WUFFQSxlQUFlO1lBQ2YsSUFBSWxCLGtCQUFrQnVELGFBQWEsRUFBRTtnQkFDbkMsa0NBQWtDO2dCQUNsQyxNQUFNK0QsU0FBUyxNQUFNbEIsYUFBYW1CLElBQUksQ0FBQ1YsbUJBQW1CRSxRQUFRO2dCQUNsRSxNQUFNTyxPQUFPRCxJQUFJO1lBQ25CLE9BQU87Z0JBQ0wscURBQXFEO2dCQUNyRCxJQUFJLENBQUNULGFBQWE7b0JBQ2hCNUYsU0FBUztvQkFDVDtnQkFDRjtnQkFDQSxNQUFNc0csU0FBUyxNQUFNbEIsYUFBYW1CLElBQUksQ0FBQ1YsbUJBQW1CRSxRQUFRO2dCQUNsRSxNQUFNTyxPQUFPRCxJQUFJO1lBQ25CO1lBRUFuRyxXQUFXLFFBQTREb0IsT0FBcERGLG9CQUFvQixXQUFXLFNBQVMsUUFBTyxPQUFxRCxPQUFoREUscUJBQXFCLFdBQVcsU0FBUyxRQUFPO1lBQ3ZIWCxjQUFjO1lBRWQsa0JBQWtCO1lBQ2xCLE1BQU1rQjtZQUNOLE1BQU1DO1FBRVIsRUFBRSxPQUFPL0IsT0FBWTtZQUNuQjBDLFFBQVExQyxLQUFLLENBQUMsc0JBQXNCQTtZQUNwQyxJQUFJQSxNQUFNaUUsSUFBSSxLQUFLLG1CQUFtQjtnQkFDcENoRSxTQUFTO1lBQ1gsT0FBTyxJQUFJRCxNQUFNbUUsT0FBTyxDQUFDc0MsUUFBUSxDQUFDLHVCQUF1QjtnQkFDdkR4RyxTQUFTO1lBQ1gsT0FBTztnQkFDTEEsU0FBUyxpQkFBa0JELENBQUFBLE1BQU0wRyxNQUFNLElBQUkxRyxNQUFNbUUsT0FBTztZQUMxRDtRQUNGLFNBQVU7WUFDUnBFLFdBQVc7UUFDYjtJQUNGO0lBRUEsZUFBZTRHO1FBQ2IsSUFBSSxDQUFDOUYsb0JBQW9CLENBQUNFLG9CQUFvQixDQUFDN0IsV0FBVyxDQUFDcUIsa0JBQWtCO1FBRTdFUixXQUFXO1FBQ1hFLFNBQVM7UUFDVEUsV0FBVztRQUVYLElBQUk7WUFDRixNQUFNLEVBQUU4QixRQUFRLEVBQUUsR0FBR0M7WUFDckIsTUFBTUMsV0FBVyxJQUFJdEUsbURBQXNCLENBQUNvRTtZQUM1QyxNQUFNMEQsU0FBUyxNQUFNeEQsU0FBU3lELFNBQVM7WUFFdkMsTUFBTXZCLGlCQUFpQixJQUFJeEcsNENBQWUsQ0FBQ29CLGtCQUFrQkosTUFBTSxFQUFFZixZQUFZNkg7WUFDakYsTUFBTXBCLGlCQUFpQixJQUFJMUcsNENBQWUsQ0FBQ29CLGtCQUFrQkgsTUFBTSxFQUFFZixZQUFZNEg7WUFDakYsTUFBTWIsYUFBYSxJQUFJakgsNENBQWUsQ0FBQ29CLGtCQUFrQnVELGFBQWEsRUFBR3ZFLG1CQUFtQjBIO1lBRTVGLE1BQU1pQixVQUFVL0ksOENBQWlCLENBQUNnRDtZQUNsQyxNQUFNZ0csVUFBVWhKLDhDQUFpQixDQUFDa0Q7WUFFbEMsaUJBQWlCO1lBQ2pCLE1BQU0sQ0FBQ3lELFVBQVVDLFNBQVMsR0FBRyxNQUFNQyxRQUFRQyxHQUFHLENBQUM7Z0JBQzdDTixlQUFlTyxTQUFTLENBQUMxRjtnQkFDekJxRixlQUFlSyxTQUFTLENBQUMxRjthQUMxQjtZQUVELElBQUlzRixXQUFXb0MsU0FBUztnQkFDdEIzRyxTQUFTO2dCQUNUO1lBQ0Y7WUFDQSxJQUFJd0UsV0FBV29DLFNBQVM7Z0JBQ3RCNUcsU0FBUztnQkFDVDtZQUNGO1lBRUEsaUJBQWlCO1lBQ2pCRSxXQUFXO1lBQ1gsTUFBTTJHLGFBQWEsTUFBTXpDLGVBQWVnQyxPQUFPLENBQUNwSCxrQkFBa0J1RCxhQUFhLEVBQUVvRTtZQUNqRixNQUFNRSxXQUFXUixJQUFJO1lBRXJCbkcsV0FBVztZQUNYLE1BQU00RyxhQUFhLE1BQU14QyxlQUFlOEIsT0FBTyxDQUFDcEgsa0JBQWtCdUQsYUFBYSxFQUFFcUU7WUFDakYsTUFBTUUsV0FBV1QsSUFBSTtZQUVyQixnQkFBZ0I7WUFDaEJuRyxXQUFXO1lBQ1gsTUFBTTZHLGlCQUFpQixNQUFNbEMsV0FBV21DLFlBQVksQ0FBQ0wsU0FBU0M7WUFDOUQsTUFBTUcsZUFBZVYsSUFBSTtZQUV6Qm5HLFdBQVc7WUFDWFcsb0JBQW9CO1lBQ3BCRSxvQkFBb0I7WUFFcEIsTUFBTWM7UUFFUixFQUFFLE9BQU85QixPQUFZO1lBQ25CMEMsUUFBUTFDLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDQyxTQUFTLG1DQUFvQ0QsQ0FBQUEsTUFBTTBHLE1BQU0sSUFBSTFHLE1BQU1tRSxPQUFPO1FBQzVFLFNBQVU7WUFDUnBFLFdBQVc7UUFDYjtJQUNGO0lBRUEsZUFBZW1IO1FBQ2IsSUFBSSxDQUFDakcsZUFBZSxDQUFDL0IsV0FBVyxDQUFDcUIsa0JBQWtCO1FBRW5EUixXQUFXO1FBQ1hFLFNBQVM7UUFDVEUsV0FBVztRQUVYLElBQUk7WUFDRixNQUFNLEVBQUU4QixRQUFRLEVBQUUsR0FBR0M7WUFDckIsTUFBTUMsV0FBVyxJQUFJdEUsbURBQXNCLENBQUNvRTtZQUM1QyxNQUFNMEQsU0FBUyxNQUFNeEQsU0FBU3lELFNBQVM7WUFFdkMsTUFBTWQsYUFBYSxJQUFJakgsNENBQWUsQ0FBQ29CLGtCQUFrQnVELGFBQWEsRUFBR3ZFLG1CQUFtQjBIO1lBQzVGLE1BQU1WLGtCQUFrQixJQUFJcEgsNENBQWUsQ0FBQ29CLGtCQUFrQitGLE9BQU8sRUFBRzlHLGFBQWF5SDtZQUVyRixNQUFNSyxTQUFTbkksOENBQWlCLENBQUNvRDtZQUVqQyxtQkFBbUI7WUFDbkIsTUFBTThELFlBQVksTUFBTUQsV0FBV0YsU0FBUyxDQUFDMUY7WUFDN0MsSUFBSTZGLFlBQVlpQixRQUFRO2dCQUN0Qi9GLFNBQVM7Z0JBQ1Q7WUFDRjtZQUVBLG9CQUFvQjtZQUNwQkUsV0FBVztZQUNYLE1BQU1pRyxZQUFZLE1BQU10QixXQUFXdUIsT0FBTyxDQUFDcEgsa0JBQWtCK0YsT0FBTyxFQUFFZ0I7WUFDdEUsTUFBTUksVUFBVUUsSUFBSTtZQUVwQixRQUFRO1lBQ1JuRyxXQUFXO1lBQ1gsTUFBTWdILFVBQVUsTUFBTWxDLGdCQUFnQm1DLEtBQUssQ0FBQ3BCO1lBQzVDLE1BQU1tQixRQUFRYixJQUFJO1lBRWxCbkcsV0FBVztZQUNYZSxlQUFlO1lBRWYsTUFBTVk7UUFFUixFQUFFLE9BQU85QixPQUFZO1lBQ25CMEMsUUFBUTFDLEtBQUssQ0FBQyxrQkFBa0JBO1lBQ2hDQyxTQUFTLDRCQUE2QkQsQ0FBQUEsTUFBTTBHLE1BQU0sSUFBSTFHLE1BQU1tRSxPQUFPO1FBQ3JFLFNBQVU7WUFDUnBFLFdBQVc7UUFDYjtJQUNGO0lBRUEsZUFBZXNIO1FBQ2IsSUFBSSxDQUFDbkksV0FBVyxDQUFDcUIsa0JBQWtCO1FBRW5DUixXQUFXO1FBQ1hFLFNBQVM7UUFDVEUsV0FBVztRQUVYLElBQUk7WUFDRixNQUFNLEVBQUU4QixRQUFRLEVBQUUsR0FBR0M7WUFDckIsTUFBTUMsV0FBVyxJQUFJdEUsbURBQXNCLENBQUNvRTtZQUM1QyxNQUFNMEQsU0FBUyxNQUFNeEQsU0FBU3lELFNBQVM7WUFFdkMsTUFBTVgsa0JBQWtCLElBQUlwSCw0Q0FBZSxDQUFDb0Isa0JBQWtCK0YsT0FBTyxFQUFHOUcsYUFBYXlIO1lBRXJGeEYsV0FBVztZQUNYLE1BQU1tSCxVQUFVLE1BQU1yQyxnQkFBZ0JzQyxXQUFXO1lBQ2pELE1BQU1ELFFBQVFoQixJQUFJO1lBRWxCbkcsV0FBVztZQUVYLE1BQU0yQjtRQUVSLEVBQUUsT0FBTzlCLE9BQVk7WUFDbkIwQyxRQUFRMUMsS0FBSyxDQUFDLG1CQUFtQkE7WUFDakNDLFNBQVMsOEJBQStCRCxDQUFBQSxNQUFNMEcsTUFBTSxJQUFJMUcsTUFBTW1FLE9BQU87UUFDdkUsU0FBVTtZQUNScEUsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNeUgsZ0JBQWdCLENBQUNsRjtRQUNyQixPQUFPLEdBQTRCQSxPQUF6QkEsUUFBUW1GLEtBQUssQ0FBQyxHQUFHLElBQUcsT0FBdUIsT0FBbEJuRixRQUFRbUYsS0FBSyxDQUFDLENBQUM7SUFDcEQ7SUFFQSxNQUFNQyxnQkFBZ0I7UUFDcEJ6SCxTQUFTO1FBQ1RFLFdBQVc7SUFDYjtJQUVBLE1BQU13SCxvQkFBb0J6SSxXQUFXRCxrQkFBa0IySSxRQUFRLElBQzdEMUksUUFBUTJJLFdBQVcsT0FBTzVJLGtCQUFrQjJJLFFBQVEsQ0FBQ0MsV0FBVztJQUVsRSxlQUFlQztRQUNiLElBQUksQ0FBQzVJLFdBQVcsQ0FBQ3FCLGtCQUFrQjtRQUVuQ1IsV0FBVztRQUNYRSxTQUFTO1FBQ1RFLFdBQVc7UUFFWCxJQUFJO1lBQ0YsZ0NBQWdDO1lBQ2hDLElBQUl3SCxtQkFBbUI7Z0JBQ3JCeEgsV0FBVztnQkFDWCxNQUFNMkI7Z0JBQ047WUFDRjtZQUVBLDRDQUE0QztZQUM1QzdCLFNBQVM7WUFDVEUsV0FBVztZQUNYNEgsTUFBTSx5REFNWTdJLE9BSlNELGtCQUFrQjJJLFFBQVEsRUFBQywwR0FLeEMzSSxPQURJQyxTQUFRLHdCQUNlLE9BQTNCRCxrQkFBa0IySSxRQUFRLEVBQUM7UUFJM0MsRUFBRSxPQUFPNUgsT0FBWTtZQUNuQjBDLFFBQVExQyxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQ0MsU0FBUyxnQ0FBaUNELENBQUFBLE1BQU0wRyxNQUFNLElBQUkxRyxNQUFNbUUsT0FBTztRQUN6RSxTQUFVO1lBQ1JwRSxXQUFXO1FBQ2I7SUFDRjtJQUVBLDhDQUE4QztJQUM5QyxlQUFlaUk7UUFDYixJQUFJLENBQUM5SSxXQUFXLENBQUNxQixrQkFBa0I7UUFFbkNSLFdBQVc7UUFDWEUsU0FBUztRQUNURSxXQUFXO1FBRVgsSUFBSTtZQUNGLE1BQU0sRUFBRThCLFFBQVEsRUFBRSxHQUFHQztZQUNyQixNQUFNQyxXQUFXLElBQUl0RSxtREFBc0IsQ0FBQ29FO1lBQzVDLE1BQU0wRCxTQUFTLE1BQU14RCxTQUFTeUQsU0FBUztZQUV2QyxNQUFNdkIsaUJBQWlCLElBQUl4Ryw0Q0FBZSxDQUFDb0Isa0JBQWtCSixNQUFNLEVBQUVmLFlBQVk2SDtZQUNqRixNQUFNTixlQUFlLElBQUl4SCw0Q0FBZSxDQUFDb0Isa0JBQWtCRixTQUFTLEVBQUVmLGVBQWUySDtZQUVyRix1REFBdUQ7WUFDdkQsTUFBTXNDLFVBQVU7Z0JBQUM7Z0JBQU07Z0JBQU07Z0JBQU07Z0JBQU07YUFBSyxFQUFFLG1CQUFtQjtZQUNuRSxJQUFJQyxjQUFjckssOENBQWlCLENBQUM7WUFFcEMsS0FBSyxNQUFNbUksVUFBVWlDLFFBQVM7Z0JBQzVCQyxlQUFlckssOENBQWlCLENBQUNtSTtZQUNuQztZQUVBLGdCQUFnQjtZQUNoQixNQUFNekQsVUFBVSxNQUFNOEIsZUFBZU8sU0FBUyxDQUFDMUY7WUFDL0MsSUFBSXFELFVBQVUyRixhQUFhO2dCQUN6QmpJLFNBQVM7Z0JBQ1Q7WUFDRjtZQUVBLHVCQUF1QjtZQUN2QkUsV0FBVztZQUNYLE1BQU1pRyxZQUFZLE1BQU0vQixlQUFlZ0MsT0FBTyxDQUFDcEgsa0JBQWtCRixTQUFTLEVBQUVtSjtZQUM1RSxNQUFNOUIsVUFBVUUsSUFBSTtZQUVwQix5QkFBeUI7WUFDekIsSUFBSyxJQUFJNkIsSUFBSSxHQUFHQSxJQUFJRixRQUFRcEYsTUFBTSxFQUFFc0YsSUFBSztnQkFDdkNoSSxXQUFXLGtCQUEyQjhILE9BQVRFLElBQUksR0FBRSxLQUFrQixPQUFmRixRQUFRcEYsTUFBTSxFQUFDO2dCQUNyRCxNQUFNbUQsU0FBU25JLDhDQUFpQixDQUFDb0ssT0FBTyxDQUFDRSxFQUFFO2dCQUMzQyxNQUFNNUIsU0FBUyxNQUFNbEIsYUFBYW1CLElBQUksQ0FBQ3ZILGtCQUFrQkosTUFBTSxFQUFFbUgsUUFBUTtnQkFDekUsTUFBTU8sT0FBT0QsSUFBSTtnQkFFakIsbUNBQW1DO2dCQUNuQyxNQUFNLElBQUk1QixRQUFRMEQsQ0FBQUEsVUFBV2xFLFdBQVdrRSxTQUFTO1lBQ25EO1lBRUFqSSxXQUFXLHlCQUF3QyxPQUFmOEgsUUFBUXBGLE1BQU0sRUFBQztZQUNuRCxNQUFNZjtRQUVSLEVBQUUsT0FBTzlCLE9BQVk7WUFDbkIwQyxRQUFRMUMsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0NDLFNBQVMsd0JBQXlCRCxDQUFBQSxNQUFNMEcsTUFBTSxJQUFJMUcsTUFBTW1FLE9BQU87UUFDakUsU0FBVTtZQUNScEUsV0FBVztRQUNiO0lBQ0Y7SUFFQSxlQUFlc0k7UUFDYixJQUFJLENBQUNuSixXQUFXLENBQUNxQixrQkFBa0I7UUFFbkNSLFdBQVc7UUFDWEUsU0FBUztRQUNURSxXQUFXO1FBRVgsSUFBSTtZQUNGLE1BQU0sRUFBRThCLFFBQVEsRUFBRSxHQUFHQztZQUNyQixNQUFNQyxXQUFXLElBQUl0RSxtREFBc0IsQ0FBQ29FO1lBQzVDLE1BQU0wRCxTQUFTLE1BQU14RCxTQUFTeUQsU0FBUztZQUV2QyxNQUFNdkIsaUJBQWlCLElBQUl4Ryw0Q0FBZSxDQUFDb0Isa0JBQWtCSixNQUFNLEVBQUVmLFlBQVk2SDtZQUNqRixNQUFNcEIsaUJBQWlCLElBQUkxRyw0Q0FBZSxDQUFDb0Isa0JBQWtCSCxNQUFNLEVBQUVmLFlBQVk0SDtZQUVqRixzQ0FBc0M7WUFDdEMsTUFBTTJDLGFBQWF6Syw4Q0FBaUIsQ0FBQztZQUNyQyxNQUFNMEssWUFBWTtnQkFDaEJ0SixrQkFBa0JGLFNBQVM7Z0JBQzNCRSxrQkFBa0J1RCxhQUFhO2dCQUMvQnZELGtCQUFrQitGLE9BQU87YUFDMUI7WUFFRCxJQUFJd0QsbUJBQW1CO1lBRXZCLEtBQUssTUFBTUMsZ0JBQWdCRixVQUFXO2dCQUNwQyx1Q0FBdUM7Z0JBQ3ZDLElBQUssSUFBSUosSUFBSSxHQUFHQSxJQUFJLEdBQUdBLElBQUs7b0JBQzFCaEksV0FBVyxtQ0FBd0QsT0FBckJxSSxtQkFBbUIsR0FBRTtvQkFFbkUsTUFBTXBDLFlBQVksTUFBTS9CLGVBQWVnQyxPQUFPLENBQUNvQyxjQUFlSDtvQkFDOUQsTUFBTWxDLFVBQVVFLElBQUk7b0JBQ3BCa0M7b0JBRUEsc0JBQXNCO29CQUN0QixNQUFNekIsYUFBYSxNQUFNeEMsZUFBZThCLE9BQU8sQ0FBQ29DLGNBQWVIO29CQUMvRCxNQUFNdkIsV0FBV1QsSUFBSTtvQkFDckJrQztvQkFFQSxjQUFjO29CQUNkLE1BQU0sSUFBSTlELFFBQVEwRCxDQUFBQSxVQUFXbEUsV0FBV2tFLFNBQVM7Z0JBQ25EO1lBQ0Y7WUFFQWpJLFdBQVcsK0JBQWdELE9BQWpCcUksa0JBQWlCO1FBRTdELEVBQUUsT0FBT3hJLE9BQVk7WUFDbkIwQyxRQUFRMUMsS0FBSyxDQUFDLGtDQUFrQ0E7WUFDaERDLFNBQVMsNkJBQThCRCxDQUFBQSxNQUFNMEcsTUFBTSxJQUFJMUcsTUFBTW1FLE9BQU87UUFDdEUsU0FBVTtZQUNScEUsV0FBVztRQUNiO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQzJJO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7b0JBQ1p6Six5QkFDQyw4REFBQ3dKO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUtELFdBQVU7MENBQWM7Ozs7Ozs0QkFBaUI7NEJBQUVuQixjQUFjdEk7Ozs7Ozs7a0NBR25FLDhEQUFDd0o7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVyx3QkFBeUUsT0FBakRwSSxtQkFBbUIsaUJBQWlCOzs7Ozs7MENBQzVFLDhEQUFDcUk7Z0NBQUtELFdBQVU7MENBQ2JwSSxtQkFBbUIsc0JBQXNCOzs7Ozs7Ozs7Ozs7a0NBSTlDLDhEQUFDbUk7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDswQ0FBSTs7Ozs7OzBDQUNMLDhEQUFDQTs7b0NBQUk7b0NBQVl6SixrQkFBa0JKLE1BQU0sS0FBS0Qsa0JBQWtCQyxNQUFNLEdBQUcsTUFBTTs7Ozs7OzswQ0FDL0UsOERBQUM2Sjs7b0NBQUk7b0NBQVd6SixrQkFBa0IySSxRQUFRLEdBQUczSSxrQkFBa0IySSxRQUFRLENBQUNILEtBQUssQ0FBQyxHQUFHLEtBQUssUUFBUTs7Ozs7OzswQ0FDOUYsOERBQUNpQjs7b0NBQUk7b0NBQWV4SixVQUFVQSxRQUFRdUksS0FBSyxDQUFDLEdBQUcsS0FBSyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtoRSw4REFBQ2lCO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBR0YsV0FBVTswQ0FBcUc7Ozs7OzswQ0FHbkgsOERBQUNHO2dDQUFFSCxXQUFVOzBDQUEwQzs7Ozs7Ozs7Ozs7O29CQU12RDNJLENBQUFBLFNBQVNFLE9BQU0sbUJBQ2YsOERBQUN3STt3QkFBSUMsV0FBVTs7NEJBQ1ozSSx1QkFDQyw4REFBQzBJO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0M7a0RBQU01STs7Ozs7O2tEQUNQLDhEQUFDK0k7d0NBQU9DLE1BQUs7d0NBQVNDLFNBQVN2Qjt3Q0FBZWlCLFdBQVU7a0RBQWtDOzs7Ozs7Ozs7Ozs7NEJBSzdGekkseUJBQ0MsOERBQUN3STtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNDO2tEQUFNMUk7Ozs7OztrREFDUCw4REFBQzZJO3dDQUFPQyxNQUFLO3dDQUFTQyxTQUFTdkI7d0NBQWVpQixXQUFVO2tEQUFzQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVFyRyxDQUFDekosd0JBQ0EsOERBQUN3Sjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNJO2dDQUNDQyxNQUFLO2dDQUNMQyxTQUFTN0U7Z0NBQ1R1RSxXQUFVOzBDQUNYOzs7Ozs7MENBR0QsOERBQUNHO2dDQUFFSCxXQUFVOzBDQUFxQjs7Ozs7Ozs7Ozs7K0JBSWxDLENBQUNwSSxpQ0FDSCw4REFBQ21JO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDSTt3Q0FDQ0MsTUFBSzt3Q0FDTEMsU0FBU25GO3dDQUNUNkUsV0FBVTtrREFDWDs7Ozs7O2tEQUdELDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0k7NENBQ0NDLE1BQUs7NENBQ0xDLFNBQVNuRzs0Q0FDVDZGLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtMLDhEQUFDRztnQ0FBRUgsV0FBVTswQ0FBcUI7Ozs7Ozs7Ozs7OzZDQUtwQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNPO3dDQUFHUCxXQUFVO2tEQUF5Qzs7Ozs7O2tEQUN2RCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNHO3dEQUFFSCxXQUFVO2tFQUE2Qjs7Ozs7O2tFQUMxQyw4REFBQ0c7d0RBQUVILFdBQVU7a0VBQ1ZRLFdBQVcvSixlQUFlZ0ssT0FBTyxDQUFDOzs7Ozs7a0VBRXJDLDhEQUFDTjt3REFBRUgsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7OzswREFFdkMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0c7d0RBQUVILFdBQVU7a0VBQTZCOzs7Ozs7a0VBQzFDLDhEQUFDRzt3REFBRUgsV0FBVTtrRUFDVlEsV0FBVzdKLGVBQWU4SixPQUFPLENBQUM7Ozs7OztrRUFFckMsOERBQUNOO3dEQUFFSCxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7OzBEQUV2Qyw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRzt3REFBRUgsV0FBVTtrRUFBNkI7Ozs7OztrRUFDMUMsOERBQUNHO3dEQUFFSCxXQUFVO2tFQUNWUSxXQUFXM0osZ0JBQWdCNEosT0FBTyxDQUFDOzs7Ozs7a0VBRXRDLDhEQUFDTjt3REFBRUgsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7OzswREFFdkMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0c7d0RBQUVILFdBQVU7a0VBQTZCOzs7Ozs7a0VBQzFDLDhEQUFDRzt3REFBRUgsV0FBVTtrRUFDVlEsV0FBV3pKLGVBQWUwSixPQUFPLENBQUM7Ozs7OztrRUFFckMsOERBQUNOO3dEQUFFSCxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQUd4Q1EsV0FBV3ZKLGlCQUFpQixtQkFDM0IsOERBQUM4STt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNHO2dEQUFFSCxXQUFVOzBEQUF3Qjs7Ozs7OzBEQUNyQyw4REFBQ0c7Z0RBQUVILFdBQVU7O29EQUNWUSxXQUFXdkosZUFBZXdKLE9BQU8sQ0FBQztvREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPOUMsOERBQUNWO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1o7NENBQ0M7Z0RBQUVVLElBQUk7Z0RBQVFDLE9BQU87Z0RBQVFDLE1BQU07NENBQUs7NENBQ3hDO2dEQUFFRixJQUFJO2dEQUFhQyxPQUFPO2dEQUFpQkMsTUFBTTs0Q0FBSzs0Q0FDdEQ7Z0RBQUVGLElBQUk7Z0RBQVNDLE9BQU87Z0RBQVNDLE1BQU07NENBQUs7NENBQzFDO2dEQUFFRixJQUFJO2dEQUFTQyxPQUFPO2dEQUFTQyxNQUFNOzRDQUFLOzRDQUMxQztnREFBRUYsSUFBSTtnREFBU0MsT0FBTztnREFBYUMsTUFBTTs0Q0FBSTt5Q0FDOUMsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLG9CQUNMLDhEQUFDVjtnREFFQ0MsTUFBSztnREFDTEMsU0FBUyxJQUFNdkksYUFBYStJLElBQUlKLEVBQUU7Z0RBQ2xDVixXQUFXLDBEQUlWLE9BSENsSSxjQUFjZ0osSUFBSUosRUFBRSxHQUNoQix3REFDQTs7a0VBR04sOERBQUNUO3dEQUFLRCxXQUFVO2tFQUFRYyxJQUFJRixJQUFJOzs7Ozs7b0RBQy9CRSxJQUFJSCxLQUFLOzsrQ0FWTEcsSUFBSUosRUFBRTs7Ozs7Ozs7OztrREFnQmpCLDhEQUFDWDt3Q0FBSUMsV0FBVTs7NENBQ1psSSxjQUFjLHdCQUNiLDhEQUFDaUk7O2tFQUNDLDhEQUFDZ0I7d0RBQUdmLFdBQVU7a0VBQTZCOzs7Ozs7a0VBRzNDLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBRWIsOERBQUNEOztrRkFDQyw4REFBQ1k7d0VBQU1YLFdBQVU7a0ZBQStDOzs7Ozs7a0ZBQ2hFLDhEQUFDRDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNnQjtnRkFDQ0MsT0FBT3ZJO2dGQUNQd0ksVUFBVSxDQUFDQyxJQUFNeEksbUJBQW1Cd0ksRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dGQUNsRGpCLFdBQVU7Z0ZBQ1ZxQixVQUFVbEs7O2tHQUVWLDhEQUFDbUs7d0ZBQU9MLE9BQU07a0dBQVM7Ozs7OztrR0FDdkIsOERBQUNLO3dGQUFPTCxPQUFNO2tHQUFTOzs7Ozs7Ozs7Ozs7MEZBRXpCLDhEQUFDbEI7Z0ZBQUlDLFdBQVU7O29GQUEyQztvRkFDOUN0SCxvQkFBb0IsV0FBVzhILFdBQVcvSixlQUFlZ0ssT0FBTyxDQUFDLEtBQUtELFdBQVc3SixlQUFlOEosT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQU14SCw4REFBQ1Y7Z0VBQUlDLFdBQVU7MEVBQ2IsNEVBQUNJO29FQUNDQyxNQUFLO29FQUNMQyxTQUFTO3dFQUNQLE1BQU1pQixPQUFPN0k7d0VBQ2JDLG1CQUFtQkM7d0VBQ25CQyxvQkFBb0IwSTtvRUFDdEI7b0VBQ0F2QixXQUFVO29FQUNWcUIsVUFBVWxLOzhFQUNYOzs7Ozs7Ozs7OzswRUFNSCw4REFBQzRJOztrRkFDQyw4REFBQ1k7d0VBQU1YLFdBQVU7a0ZBQStDOzs7Ozs7a0ZBQ2hFLDhEQUFDRDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNnQjtnRkFDQ0MsT0FBT3JJO2dGQUNQc0ksVUFBVSxDQUFDQyxJQUFNdEksb0JBQW9Cc0ksRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dGQUNuRGpCLFdBQVU7Z0ZBQ1ZxQixVQUFVbEs7O2tHQUVWLDhEQUFDbUs7d0ZBQU9MLE9BQU07a0dBQVM7Ozs7OztrR0FDdkIsOERBQUNLO3dGQUFPTCxPQUFNO2tHQUFTOzs7Ozs7Ozs7Ozs7MEZBRXpCLDhEQUFDbEI7Z0ZBQUlDLFdBQVU7O29GQUEyQztvRkFDOUNwSCxxQkFBcUIsV0FBVzRILFdBQVcvSixlQUFlZ0ssT0FBTyxDQUFDLEtBQUtELFdBQVc3SixlQUFlOEosT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU8zSCw4REFBQ1Y7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDVztnRUFBTVgsV0FBVTswRUFBK0M7Ozs7OzswRUFHaEUsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ3dCO3dFQUNDbkIsTUFBSzt3RUFDTFksT0FBT2pKO3dFQUNQa0osVUFBVSxDQUFDQzs0RUFDVHBILFFBQVFLLEdBQUcsQ0FBQyx1QkFBdUIrRyxFQUFFQyxNQUFNLENBQUNILEtBQUs7NEVBQ2pEaEosY0FBY2tKLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3RUFDOUI7d0VBQ0FRLGFBQVk7d0VBQ1p6QixXQUFVO3dFQUNWcUIsVUFBVWxLO3dFQUNWdUssS0FBSTt3RUFDSkMsTUFBSzt3RUFDTEMsY0FBYTs7Ozs7O2tGQUVmLDhEQUFDN0I7d0VBQUlDLFdBQVU7a0ZBQ1p0SCxvQkFBb0IsV0FBVyxTQUFTOzs7Ozs7Ozs7Ozs7MEVBRzdDLDhEQUFDcUg7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRzt3RUFBRUgsV0FBVTs7NEVBQXdCOzRFQUNqQnRILG9CQUFvQixXQUFXLFNBQVM7NEVBQU87NEVBQU1FLHFCQUFxQixXQUFXLFNBQVM7Ozs7Ozs7a0ZBRWxILDhEQUFDbUg7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDSTtnRkFDQ0MsTUFBSztnRkFDTEMsU0FBUyxJQUFNckksY0FBYztnRkFDN0IrSCxXQUFVO2dGQUNWcUIsVUFBVWxLOzBGQUNYOzs7Ozs7MEZBR0QsOERBQUNpSjtnRkFDQ0MsTUFBSztnRkFDTEMsU0FBUyxJQUFNckksY0FBYztnRkFDN0IrSCxXQUFVO2dGQUNWcUIsVUFBVWxLOzBGQUNYOzs7Ozs7MEZBR0QsOERBQUNpSjtnRkFDQ0MsTUFBSztnRkFDTEMsU0FBUyxJQUFNckksY0FBY1Msb0JBQW9CLFdBQVdqQyxnQkFBZ0JFO2dGQUM1RXFKLFdBQVU7Z0ZBQ1ZxQixVQUFVbEssV0FBWXVCLENBQUFBLG9CQUFvQixXQUFXOEgsV0FBVy9KLG1CQUFtQixJQUFJK0osV0FBVzdKLG1CQUFtQjswRkFDdEg7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFLTCw4REFBQ3dKO2dFQUFFSCxXQUFVOztvRUFBNkI7b0VBQzVCdEgsb0JBQW9CLFdBQVc4SCxXQUFXL0osZUFBZWdLLE9BQU8sQ0FBQyxLQUFLRCxXQUFXN0osZUFBZThKLE9BQU8sQ0FBQztvRUFBRztvRUFBRS9ILG9CQUFvQixXQUFXLFNBQVM7b0VBQU87b0VBQVlWO29FQUFXOzs7Ozs7Ozs7Ozs7O2tFQUtuTSw4REFBQytIO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0c7Z0VBQUVILFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3JDLDhEQUFDRztnRUFBRUgsV0FBVTs7b0VBQ1ZRLFdBQVc5SSxXQUFXK0ksT0FBTyxDQUFDO29FQUFHOzs7Ozs7Ozs7Ozs7O2tFQUt0Qyw4REFBQ0w7d0RBQ0NDLE1BQUs7d0RBQ0xDLFNBQVN2RDt3REFDVHNFLFVBQVVsSyxXQUFXLENBQUNhLGNBQWN3SSxXQUFXeEksZUFBZTt3REFDOURnSSxXQUFVO2tFQUVUN0ksd0JBQ0MsOERBQUM0STs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzs7Ozs7Z0VBQXVFOzs7Ozs7bUVBSXhGLFFBQTREcEgsT0FBcERGLG9CQUFvQixXQUFXLFNBQVMsUUFBTyxPQUFxRCxPQUFoREUscUJBQXFCLFdBQVcsU0FBUzs7Ozs7Ozs7Ozs7OzRDQU01R2QsY0FBYyw2QkFDYiw4REFBQ2lJOztrRUFDQyw4REFBQ2dCO3dEQUFHZixXQUFVO2tFQUE2Qjs7Ozs7O2tFQUUzQyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDs7a0ZBQ0MsOERBQUNZO3dFQUFNWCxXQUFVO2tGQUErQzs7Ozs7O2tGQUdoRSw4REFBQ0Q7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDd0I7Z0ZBQ0NuQixNQUFLO2dGQUNMWSxPQUFPL0k7Z0ZBQ1BnSixVQUFVLENBQUNDO29GQUNUcEgsUUFBUUssR0FBRyxDQUFDLDhCQUE4QitHLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvRkFDeEQ5SSxvQkFBb0JnSixFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0ZBQ3BDO2dGQUNBUSxhQUFZO2dGQUNaekIsV0FBVTtnRkFDVnFCLFVBQVVsSztnRkFDVnVLLEtBQUk7Z0ZBQ0pDLE1BQUs7Z0ZBQ0xDLGNBQWE7Ozs7OzswRkFFZiw4REFBQzdCO2dGQUFJQyxXQUFVOzBGQUFvRzs7Ozs7Ozs7Ozs7O2tGQUlySCw4REFBQ0c7d0VBQUVILFdBQVU7OzRFQUE2Qjs0RUFDNUJRLFdBQVcvSixlQUFlZ0ssT0FBTyxDQUFDOzRFQUFHOzRFQUFtQnZJOzRFQUFpQjs7Ozs7Ozs7Ozs7OzswRUFJekYsOERBQUM2SDs7a0ZBQ0MsOERBQUNZO3dFQUFNWCxXQUFVO2tGQUErQzs7Ozs7O2tGQUdoRSw4REFBQ0Q7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDd0I7Z0ZBQ0NuQixNQUFLO2dGQUNMWSxPQUFPN0k7Z0ZBQ1A4SSxVQUFVLENBQUNDO29GQUNUcEgsUUFBUUssR0FBRyxDQUFDLDhCQUE4QitHLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvRkFDeEQ1SSxvQkFBb0I4SSxFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0ZBQ3BDO2dGQUNBUSxhQUFZO2dGQUNaekIsV0FBVTtnRkFDVnFCLFVBQVVsSztnRkFDVnVLLEtBQUk7Z0ZBQ0pDLE1BQUs7Z0ZBQ0xDLGNBQWE7Ozs7OzswRkFFZiw4REFBQzdCO2dGQUFJQyxXQUFVOzBGQUFvRzs7Ozs7Ozs7Ozs7O2tGQUlySCw4REFBQ0c7d0VBQUVILFdBQVU7OzRFQUE2Qjs0RUFDNUJRLFdBQVc3SixlQUFlOEosT0FBTyxDQUFDOzRFQUFHOzRFQUFtQnJJOzRFQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFLM0YsOERBQUNnSTt3REFDQ0MsTUFBSzt3REFDTEMsU0FBU3RDO3dEQUNUcUQsVUFBVWxLLFdBQVcsQ0FBQ2Usb0JBQW9CLENBQUNFO3dEQUMzQzRILFdBQVU7a0VBRVQ3SSxVQUFVLGtCQUFrQjs7Ozs7Ozs7Ozs7OzRDQUtsQ1csY0FBYyx5QkFDYiw4REFBQ2lJOztrRUFDQyw4REFBQ2dCO3dEQUFHZixXQUFVO2tFQUE2Qjs7Ozs7O2tFQUUzQyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDVztnRUFBTVgsV0FBVTswRUFBK0M7Ozs7OzswRUFHaEUsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ3dCO3dFQUNDbkIsTUFBSzt3RUFDTFksT0FBTzNJO3dFQUNQNEksVUFBVSxDQUFDQzs0RUFDVHBILFFBQVFLLEdBQUcsQ0FBQyx3QkFBd0IrRyxFQUFFQyxNQUFNLENBQUNILEtBQUs7NEVBQ2xEMUksZUFBZTRJLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3RUFDL0I7d0VBQ0FRLGFBQVk7d0VBQ1p6QixXQUFVO3dFQUNWcUIsVUFBVWxLO3dFQUNWdUssS0FBSTt3RUFDSkMsTUFBSzt3RUFDTEMsY0FBYTs7Ozs7O2tGQUVmLDhEQUFDN0I7d0VBQUlDLFdBQVU7a0ZBQW9HOzs7Ozs7Ozs7Ozs7MEVBSXJILDhEQUFDRztnRUFBRUgsV0FBVTs7b0VBQTZCO29FQUM1QlEsV0FBVzNKLGdCQUFnQjRKLE9BQU8sQ0FBQztvRUFBRztvRUFBa0JuSTtvRUFBWTs7Ozs7Ozs7Ozs7OztrRUFJcEYsOERBQUM4SDt3REFDQ0MsTUFBSzt3REFDTEMsU0FBUy9CO3dEQUNUOEMsVUFBVWxLLFdBQVcsQ0FBQ21CLGVBQWVrSSxXQUFXM0osb0JBQW9CO3dEQUNwRW1KLFdBQVU7a0VBRVQ3SSxVQUFVLGtCQUFrQjs7Ozs7Ozs7Ozs7OzRDQUtsQ1csY0FBYyx5QkFDYiw4REFBQ2lJOztrRUFDQyw4REFBQ2dCO3dEQUFHZixXQUFVO2tFQUE2Qjs7Ozs7O2tFQUUzQyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRztnRUFBRUgsV0FBVTswRUFBNkI7Ozs7OzswRUFDMUMsOERBQUNHO2dFQUFFSCxXQUFVOzBFQUNWUSxXQUFXdkosZUFBZXdKLE9BQU8sQ0FBQzs7Ozs7OzBFQUVyQyw4REFBQ047Z0VBQUVILFdBQVU7MEVBQXdCOzs7Ozs7Ozs7Ozs7a0VBR3ZDLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0c7d0VBQUVILFdBQVU7a0ZBQXdCOzs7Ozs7a0ZBQ3JDLDhEQUFDRzt3RUFBRUgsV0FBVTtrRkFDVlEsV0FBV3pKLGVBQWUwSixPQUFPLENBQUM7Ozs7OztrRkFFckMsOERBQUNOO3dFQUFFSCxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7OzBFQUV2Qyw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRzt3RUFBRUgsV0FBVTtrRkFBd0I7Ozs7OztrRkFDckMsOERBQUNHO3dFQUFFSCxXQUFVO2tGQUFpRDs7Ozs7O2tGQUc5RCw4REFBQ0c7d0VBQUVILFdBQVU7a0ZBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSXpDLDhEQUFDSTt3REFDQ0MsTUFBSzt3REFDTEMsU0FBUzVCO3dEQUNUMkMsVUFBVWxLLFdBQVdxSixXQUFXdkosbUJBQW1CO3dEQUNuRCtJLFdBQVU7a0VBRVQ3SSxVQUFVLGtCQUFrQjs7Ozs7Ozs7Ozs7OzRDQUtsQ1csY0FBYyx5QkFDYiw4REFBQ2lJOztrRUFDQyw4REFBQ2dCO3dEQUFHZixXQUFVO2tFQUE2Qjs7Ozs7O2tFQUMzQyw4REFBQ0c7d0RBQUVILFdBQVU7a0VBQTZCOzs7Ozs7a0VBSTFDLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBRWIsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQzZCO3dFQUFHN0IsV0FBVTtrRkFBbUM7Ozs7OztrRkFDakQsOERBQUNHO3dFQUFFSCxXQUFVO2tGQUE2Qjs7Ozs7O2tGQUcxQyw4REFBQ0k7d0VBQ0NDLE1BQUs7d0VBQ0xDLFNBQVNqQjt3RUFDVGdDLFVBQVVsSyxXQUFXcUosV0FBVy9KLGlCQUFpQjt3RUFDakR1SixXQUFVO2tGQUVUN0ksVUFBVSxrQkFBa0I7Ozs7OztrRkFFL0IsOERBQUNnSjt3RUFBRUgsV0FBVTtrRkFBNkI7Ozs7Ozs7Ozs7OzswRUFNNUMsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQzZCO3dFQUFHN0IsV0FBVTtrRkFBcUM7Ozs7OztrRkFDbkQsOERBQUNHO3dFQUFFSCxXQUFVO2tGQUErQjs7Ozs7O2tGQUc1Qyw4REFBQ0k7d0VBQ0NDLE1BQUs7d0VBQ0xDLFNBQVNaO3dFQUNUMkIsVUFBVWxLO3dFQUNWNkksV0FBVTtrRkFFVDdJLFVBQVUsa0JBQWtCOzs7Ozs7a0ZBRS9CLDhEQUFDZ0o7d0VBQUVILFdBQVU7a0ZBQStCOzs7Ozs7Ozs7Ozs7MEVBTTlDLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUM2Qjt3RUFBRzdCLFdBQVU7a0ZBQW9DOzs7Ozs7a0ZBQ2xELDhEQUFDRzt3RUFBRUgsV0FBVTtrRkFBOEI7Ozs7OztrRkFHM0MsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDRDt3RkFBSUMsV0FBVTtrR0FBZ0I7Ozs7OztrR0FDL0IsOERBQUNEO2tHQUFJOzs7Ozs7Ozs7Ozs7MEZBRVAsOERBQUNBO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ0Q7d0ZBQUlDLFdBQVU7a0dBQWdCOzs7Ozs7a0dBQy9CLDhEQUFDRDtrR0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQU1YLDhEQUFDQTtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUM2Qjt3RUFBRzdCLFdBQVU7a0ZBQXFDOzs7Ozs7a0ZBQ25ELDhEQUFDOEI7d0VBQUc5QixXQUFVOzswRkFDWiw4REFBQytCOzBGQUFHOzs7Ozs7MEZBQ0osOERBQUNBOzBGQUFHOzs7Ozs7MEZBQ0osOERBQUNBOzBGQUFHOzs7Ozs7MEZBQ0osOERBQUNBOzBGQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBU2Z2QixXQUFXL0osbUJBQW1CLEtBQUtxQixjQUFjLHdCQUNoRCw4REFBQ2lJO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2U7Z0RBQUdmLFdBQVU7MERBQTZDOzs7Ozs7NENBQzFEaEIsa0NBQ0MsOERBQUNlOztrRUFDQyw4REFBQ0k7d0RBQUVILFdBQVU7a0VBQStCOzs7Ozs7a0VBRzVDLDhEQUFDSTt3REFDQ0MsTUFBSzt3REFDTEMsU0FBU25IO3dEQUNUNkcsV0FBVTtrRUFDWDs7Ozs7Ozs7Ozs7cUVBS0gsOERBQUNEOztrRUFDQyw4REFBQ0k7d0RBQUVILFdBQVU7a0VBQStCOzs7Ozs7a0VBRzVDLDhEQUFDRDt3REFBSUMsV0FBVTs7NERBQW1FOzZEQUNyRTFKLDhCQUFBQSxrQkFBa0IySSxRQUFRLGNBQTFCM0ksa0RBQUFBLDRCQUE0QndJLEtBQUssQ0FBQyxHQUFHOzREQUFJOzs7Ozs7O2tFQUV0RCw4REFBQ3NCO3dEQUNDQyxNQUFLO3dEQUNMQyxTQUFTbkI7d0RBQ1RrQyxVQUFVbEs7d0RBQ1Y2SSxXQUFVO2tFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBVVgsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2U7d0NBQUdmLFdBQVU7a0RBQXFCOzs7Ozs7a0RBQ25DLDhEQUFDZ0M7d0NBQUdoQyxXQUFVOzswREFDWiw4REFBQytCOztvREFBRztrRUFBRyw4REFBQ0U7a0VBQU87Ozs7OztvREFBYTs7Ozs7OzswREFDNUIsOERBQUNGOztvREFBRztrRUFBRyw4REFBQ0U7a0VBQU87Ozs7OztvREFBc0I7Ozs7Ozs7MERBQ3JDLDhEQUFDRjs7b0RBQUc7a0VBQUcsOERBQUNFO2tFQUFPOzs7Ozs7b0RBQWM7Ozs7Ozs7MERBQzdCLDhEQUFDRjs7b0RBQUc7a0VBQUcsOERBQUNFO2tFQUFPOzs7Ozs7b0RBQWM7Ozs7Ozs7MERBQzdCLDhEQUFDRjs7b0RBQUc7a0VBQUcsOERBQUNFO2tFQUFPOzs7Ozs7b0RBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFROUM7R0E5eEN3QjVMO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBldGhlcnMgfSBmcm9tICdldGhlcnMnO1xuXG4vLyBDb250cmFjdCBBQklzIC0gQ29tcGxldGUgZm9yIGFsbCBEZUZpIG9wZXJhdGlvbnNcbmNvbnN0IFRva2VuQV9BQkkgPSBbXG4gIFwiZnVuY3Rpb24gYmFsYW5jZU9mKGFkZHJlc3Mgb3duZXIpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAodWludDI1NilcIixcbiAgXCJmdW5jdGlvbiBhcHByb3ZlKGFkZHJlc3Mgc3BlbmRlciwgdWludDI1NiBhbW91bnQpIGV4dGVybmFsIHJldHVybnMgKGJvb2wpXCIsXG4gIFwiZnVuY3Rpb24gYWxsb3dhbmNlKGFkZHJlc3Mgb3duZXIsIGFkZHJlc3Mgc3BlbmRlcikgZXh0ZXJuYWwgdmlldyByZXR1cm5zICh1aW50MjU2KVwiLFxuICBcImZ1bmN0aW9uIHN5bWJvbCgpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAoc3RyaW5nKVwiLFxuICBcImZ1bmN0aW9uIGRlY2ltYWxzKCkgZXh0ZXJuYWwgdmlldyByZXR1cm5zICh1aW50OClcIixcbiAgXCJmdW5jdGlvbiBuYW1lKCkgZXh0ZXJuYWwgdmlldyByZXR1cm5zIChzdHJpbmcpXCIsXG4gIFwiZnVuY3Rpb24gdG90YWxTdXBwbHkoKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKHVpbnQyNTYpXCIsXG4gIFwiZnVuY3Rpb24gdHJhbnNmZXIoYWRkcmVzcyB0bywgdWludDI1NiBhbW91bnQpIGV4dGVybmFsIHJldHVybnMgKGJvb2wpXCIsXG4gIFwiZnVuY3Rpb24gdHJhbnNmZXJGcm9tKGFkZHJlc3MgZnJvbSwgYWRkcmVzcyB0bywgdWludDI1NiBhbW91bnQpIGV4dGVybmFsIHJldHVybnMgKGJvb2wpXCJcbl07XG5cbmNvbnN0IFRva2VuQl9BQkkgPSBbXG4gIFwiZnVuY3Rpb24gYmFsYW5jZU9mKGFkZHJlc3Mgb3duZXIpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAodWludDI1NilcIixcbiAgXCJmdW5jdGlvbiBzeW1ib2woKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKHN0cmluZylcIixcbiAgXCJmdW5jdGlvbiBkZWNpbWFscygpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAodWludDgpXCIsXG4gIFwiZnVuY3Rpb24gbmFtZSgpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAoc3RyaW5nKVwiLFxuICBcImZ1bmN0aW9uIHRvdGFsU3VwcGx5KCkgZXh0ZXJuYWwgdmlldyByZXR1cm5zICh1aW50MjU2KVwiXG5dO1xuXG5jb25zdCBUb2tlblN3YXBfQUJJID0gW1xuICBcImZ1bmN0aW9uIHN3YXAoYWRkcmVzcyB0b2tlbkluLCB1aW50MjU2IGFtb3VudEluLCB1aW50MjU2IG1pbkFtb3VudE91dCkgZXh0ZXJuYWwgcmV0dXJucyAodWludDI1NilcIixcbiAgXCJmdW5jdGlvbiBnZXRUb2tlbkJMaXF1aWRpdHkoKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKHVpbnQyNTYpXCIsXG4gIFwiZnVuY3Rpb24gZ2V0VG9rZW5BQmFsYW5jZSgpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAodWludDI1NilcIixcbiAgXCJmdW5jdGlvbiB0b2tlbkEoKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKGFkZHJlc3MpXCIsXG4gIFwiZnVuY3Rpb24gdG9rZW5CKCkgZXh0ZXJuYWwgdmlldyByZXR1cm5zIChhZGRyZXNzKVwiLFxuICBcImV2ZW50IFN3YXAoYWRkcmVzcyBpbmRleGVkIHVzZXIsIGFkZHJlc3MgdG9rZW5JbiwgYWRkcmVzcyB0b2tlbk91dCwgdWludDI1NiBhbW91bnRJbiwgdWludDI1NiBhbW91bnRPdXQpXCJcbl07XG5cbmNvbnN0IExpcXVpZGl0eVBvb2xfQUJJID0gW1xuICBcImZ1bmN0aW9uIGFkZExpcXVpZGl0eSh1aW50MjU2IGFtb3VudEEsIHVpbnQyNTYgYW1vdW50QikgZXh0ZXJuYWwgcmV0dXJucyAodWludDI1NilcIixcbiAgXCJmdW5jdGlvbiByZW1vdmVMaXF1aWRpdHkodWludDI1NiBsaXF1aWRpdHkpIGV4dGVybmFsIHJldHVybnMgKHVpbnQyNTYsIHVpbnQyNTYpXCIsXG4gIFwiZnVuY3Rpb24gc3dhcChhZGRyZXNzIHRva2VuSW4sIHVpbnQyNTYgYW1vdW50SW4sIHVpbnQyNTYgbWluQW1vdW50T3V0KSBleHRlcm5hbCByZXR1cm5zICh1aW50MjU2KVwiLFxuICBcImZ1bmN0aW9uIGdldFJlc2VydmVzKCkgZXh0ZXJuYWwgdmlldyByZXR1cm5zICh1aW50MjU2LCB1aW50MjU2KVwiLFxuICBcImZ1bmN0aW9uIGdldEFtb3VudE91dChhZGRyZXNzIHRva2VuSW4sIHVpbnQyNTYgYW1vdW50SW4pIGV4dGVybmFsIHZpZXcgcmV0dXJucyAodWludDI1NilcIixcbiAgXCJmdW5jdGlvbiBiYWxhbmNlT2YoYWRkcmVzcyBvd25lcikgZXh0ZXJuYWwgdmlldyByZXR1cm5zICh1aW50MjU2KVwiLFxuICBcImZ1bmN0aW9uIHRvdGFsU3VwcGx5KCkgZXh0ZXJuYWwgdmlldyByZXR1cm5zICh1aW50MjU2KVwiLFxuICBcImZ1bmN0aW9uIHN5bWJvbCgpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAoc3RyaW5nKVwiLFxuICBcImZ1bmN0aW9uIG5hbWUoKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKHN0cmluZylcIlxuXTtcblxuY29uc3QgU3Rha2luZ19BQkkgPSBbXG4gIFwiZnVuY3Rpb24gc3Rha2UodWludDI1NiBhbW91bnQpIGV4dGVybmFsXCIsXG4gIFwiZnVuY3Rpb24gd2l0aGRyYXcodWludDI1NiBhbW91bnQpIGV4dGVybmFsXCIsXG4gIFwiZnVuY3Rpb24gY2xhaW1SZXdhcmQoKSBleHRlcm5hbFwiLFxuICBcImZ1bmN0aW9uIGV4aXQoKSBleHRlcm5hbFwiLFxuICBcImZ1bmN0aW9uIGNvbXBvdW5kKCkgZXh0ZXJuYWxcIixcbiAgXCJmdW5jdGlvbiBtdWx0aVN0YWtlKHVpbnQyNTZbXSBhbW91bnRzKSBleHRlcm5hbFwiLFxuICBcImZ1bmN0aW9uIG11bHRpV2l0aGRyYXcodWludDI1NltdIGFtb3VudHMpIGV4dGVybmFsXCIsXG4gIFwiZnVuY3Rpb24gbXVsdGlDbGFpbSh1aW50MjU2IHRpbWVzKSBleHRlcm5hbFwiLFxuICBcImZ1bmN0aW9uIGVhcm5lZChhZGRyZXNzIGFjY291bnQpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAodWludDI1NilcIixcbiAgXCJmdW5jdGlvbiBiYWxhbmNlcyhhZGRyZXNzIGFjY291bnQpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAodWludDI1NilcIixcbiAgXCJmdW5jdGlvbiBnZXRTdGFraW5nSW5mbyhhZGRyZXNzIHVzZXIpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAodWludDI1NiwgdWludDI1NiwgdWludDI1NiwgdWludDI1NilcIlxuXTtcblxuLy8gTmV4dXMgbmV0d29yayBjb25maWd1cmF0aW9uXG5jb25zdCBORVhVU19ORVRXT1JLID0ge1xuICBjaGFpbklkOiAnMHhGNjQnLCAvLyAzOTQwIGluIGhleFxuICBjaGFpbk5hbWU6ICdOZXh1cyBUZXN0bmV0IElJSScsXG4gIG5hdGl2ZUN1cnJlbmN5OiB7XG4gICAgbmFtZTogJ05FWCcsXG4gICAgc3ltYm9sOiAnTkVYJyxcbiAgICBkZWNpbWFsczogMTgsXG4gIH0sXG4gIHJwY1VybHM6IFsnaHR0cHM6Ly90ZXN0bmV0My5ycGMubmV4dXMueHl6J10sXG4gIGJsb2NrRXhwbG9yZXJVcmxzOiBbJ2h0dHBzOi8vZXhwbG9yZXIubmV4dXMueHl6J10sXG59O1xuXG4vLyBEZWZhdWx0IGNvbnRyYWN0IGFkZHJlc3NlcyAod2lsbCBiZSByZXBsYWNlZCB3aXRoIGRlcGxveWVkIGFkZHJlc3NlcylcbmNvbnN0IERFRkFVTFRfQUREUkVTU0VTID0ge1xuICBUb2tlbkE6ICcweDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAnLFxuICBUb2tlbkI6ICcweDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAnLFxuICBUb2tlblN3YXA6ICcweDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAnLFxufTtcblxuaW50ZXJmYWNlIENvbnRyYWN0QWRkcmVzc2VzIHtcbiAgVG9rZW5BOiBzdHJpbmc7XG4gIFRva2VuQjogc3RyaW5nO1xuICBUb2tlblN3YXA6IHN0cmluZztcbiAgTGlxdWlkaXR5UG9vbD86IHN0cmluZztcbiAgU3Rha2luZz86IHN0cmluZztcbiAgbmV0d29yaz86IHN0cmluZztcbiAgY2hhaW5JZD86IG51bWJlcjtcbiAgZGVwbG95ZXI/OiBzdHJpbmc7XG4gIGRlcGxveW1lbnRCbG9jaz86IG51bWJlcjtcbiAgdGltZXN0YW1wPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICAvLyBCYXNpYyBzdGF0ZXNcbiAgY29uc3QgW2FjY291bnQsIHNldEFjY291bnRdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFt0b2tlbkFCYWxhbmNlLCBzZXRUb2tlbkFCYWxhbmNlXSA9IHVzZVN0YXRlPHN0cmluZz4oJzAnKTtcbiAgY29uc3QgW3Rva2VuQkJhbGFuY2UsIHNldFRva2VuQkJhbGFuY2VdID0gdXNlU3RhdGU8c3RyaW5nPignMCcpO1xuICBjb25zdCBbbHBUb2tlbkJhbGFuY2UsIHNldExwVG9rZW5CYWxhbmNlXSA9IHVzZVN0YXRlPHN0cmluZz4oJzAnKTtcbiAgY29uc3QgW3N0YWtlZEJhbGFuY2UsIHNldFN0YWtlZEJhbGFuY2VdID0gdXNlU3RhdGU8c3RyaW5nPignMCcpO1xuICBjb25zdCBbZWFybmVkUmV3YXJkcywgc2V0RWFybmVkUmV3YXJkc10gPSB1c2VTdGF0ZTxzdHJpbmc+KCcwJyk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW3N1Y2Nlc3MsIHNldFN1Y2Nlc3NdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFtjb250cmFjdEFkZHJlc3Nlcywgc2V0Q29udHJhY3RBZGRyZXNzZXNdID0gdXNlU3RhdGU8Q29udHJhY3RBZGRyZXNzZXM+KERFRkFVTFRfQUREUkVTU0VTKTtcbiAgY29uc3QgW2xpcXVpZGl0eSwgc2V0TGlxdWlkaXR5XSA9IHVzZVN0YXRlPHN0cmluZz4oJzAnKTtcbiAgY29uc3QgW2lzQ29ycmVjdE5ldHdvcmssIHNldElzQ29ycmVjdE5ldHdvcmtdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xuXG4gIC8vIFRhYiBhbmQgZm9ybSBzdGF0ZXNcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlPHN0cmluZz4oJ3N3YXAnKTtcbiAgY29uc3QgW3N3YXBBbW91bnQsIHNldFN3YXBBbW91bnRdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFtsaXF1aWRpdHlBbW91bnRBLCBzZXRMaXF1aWRpdHlBbW91bnRBXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbbGlxdWlkaXR5QW1vdW50Qiwgc2V0TGlxdWlkaXR5QW1vdW50Ql0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW3N0YWtlQW1vdW50LCBzZXRTdGFrZUFtb3VudF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW3dpdGhkcmF3QW1vdW50LCBzZXRXaXRoZHJhd0Ftb3VudF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcblxuICAvLyBUb2tlbiBzZWxlY3Rpb24gc3RhdGVzXG4gIGNvbnN0IFtzZWxlY3RlZFRva2VuSW4sIHNldFNlbGVjdGVkVG9rZW5Jbl0gPSB1c2VTdGF0ZTxzdHJpbmc+KCdUb2tlbkEnKTtcbiAgY29uc3QgW3NlbGVjdGVkVG9rZW5PdXQsIHNldFNlbGVjdGVkVG9rZW5PdXRdID0gdXNlU3RhdGU8c3RyaW5nPignVG9rZW5CJyk7XG4gIGNvbnN0IFthdmFpbGFibGVUb2tlbnMsIHNldEF2YWlsYWJsZVRva2Vuc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2hlY2tJZldhbGxldElzQ29ubmVjdGVkKCk7XG4gICAgbG9hZENvbnRyYWN0QWRkcmVzc2VzKCk7XG4gICAgc2V0dXBFdmVudExpc3RlbmVycygpO1xuICB9LCBbXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoYWNjb3VudCAmJiBpc0NvcnJlY3ROZXR3b3JrKSB7XG4gICAgICB1cGRhdGVCYWxhbmNlcygpO1xuICAgICAgdXBkYXRlTGlxdWlkaXR5KCk7XG4gICAgICBsb2FkQXZhaWxhYmxlVG9rZW5zKCk7XG4gICAgfVxuICB9LCBbYWNjb3VudCwgY29udHJhY3RBZGRyZXNzZXMsIGlzQ29ycmVjdE5ldHdvcmtdKTtcblxuICBhc3luYyBmdW5jdGlvbiBsb2FkQXZhaWxhYmxlVG9rZW5zKCkge1xuICAgIGlmICghYWNjb3VudCB8fCAhaXNDb3JyZWN0TmV0d29yaykgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZXRoZXJldW0gfSA9IHdpbmRvdyBhcyBhbnk7XG4gICAgICBjb25zdCBwcm92aWRlciA9IG5ldyBldGhlcnMuQnJvd3NlclByb3ZpZGVyKGV0aGVyZXVtKTtcblxuICAgICAgY29uc3QgdG9rZW5zID0gW1xuICAgICAgICB7XG4gICAgICAgICAgc3ltYm9sOiAnVEtOQScsXG4gICAgICAgICAgbmFtZTogJ1Rva2VuIEEnLFxuICAgICAgICAgIGFkZHJlc3M6IGNvbnRyYWN0QWRkcmVzc2VzLlRva2VuQSxcbiAgICAgICAgICBiYWxhbmNlOiB0b2tlbkFCYWxhbmNlLFxuICAgICAgICAgIGRlY2ltYWxzOiAxOFxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgc3ltYm9sOiAnVEtOQicsXG4gICAgICAgICAgbmFtZTogJ1Rva2VuIEInLFxuICAgICAgICAgIGFkZHJlc3M6IGNvbnRyYWN0QWRkcmVzc2VzLlRva2VuQixcbiAgICAgICAgICBiYWxhbmNlOiB0b2tlbkJCYWxhbmNlLFxuICAgICAgICAgIGRlY2ltYWxzOiAxOFxuICAgICAgICB9XG4gICAgICBdO1xuXG4gICAgICAvLyBBZGQgTFAgdG9rZW4gaWYgYXZhaWxhYmxlXG4gICAgICBpZiAoY29udHJhY3RBZGRyZXNzZXMuTGlxdWlkaXR5UG9vbCkge1xuICAgICAgICB0b2tlbnMucHVzaCh7XG4gICAgICAgICAgc3ltYm9sOiAnTkxQJyxcbiAgICAgICAgICBuYW1lOiAnTmV4dXMgTFAgVG9rZW4nLFxuICAgICAgICAgIGFkZHJlc3M6IGNvbnRyYWN0QWRkcmVzc2VzLkxpcXVpZGl0eVBvb2wsXG4gICAgICAgICAgYmFsYW5jZTogbHBUb2tlbkJhbGFuY2UsXG4gICAgICAgICAgZGVjaW1hbHM6IDE4XG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICBzZXRBdmFpbGFibGVUb2tlbnModG9rZW5zKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBhdmFpbGFibGUgdG9rZW5zOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICBmdW5jdGlvbiBzZXR1cEV2ZW50TGlzdGVuZXJzKCkge1xuICAgIGNvbnN0IHsgZXRoZXJldW0gfSA9IHdpbmRvdyBhcyBhbnk7XG4gICAgaWYgKCFldGhlcmV1bSkgcmV0dXJuO1xuXG4gICAgLy8gTGlzdGVuIGZvciBhY2NvdW50IGNoYW5nZXNcbiAgICBldGhlcmV1bS5vbignYWNjb3VudHNDaGFuZ2VkJywgKGFjY291bnRzOiBzdHJpbmdbXSkgPT4ge1xuICAgICAgaWYgKGFjY291bnRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgc2V0QWNjb3VudChhY2NvdW50c1swXSk7XG4gICAgICAgIGNoZWNrTmV0d29yaygpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0QWNjb3VudCgnJyk7XG4gICAgICAgIHNldElzQ29ycmVjdE5ldHdvcmsoZmFsc2UpO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgLy8gTGlzdGVuIGZvciBuZXR3b3JrIGNoYW5nZXNcbiAgICBldGhlcmV1bS5vbignY2hhaW5DaGFuZ2VkJywgKGNoYWluSWQ6IHN0cmluZykgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ05ldHdvcmsgY2hhbmdlZCB0bzonLCBjaGFpbklkKTtcbiAgICAgIGNoZWNrTmV0d29yaygpO1xuICAgICAgLy8gUmVsb2FkIHBhZ2UgdG8gcmVzZXQgc3RhdGVcbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKTtcbiAgICB9KTtcblxuICAgIC8vIENsZWFudXAgZnVuY3Rpb25cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKGV0aGVyZXVtLnJlbW92ZUxpc3RlbmVyKSB7XG4gICAgICAgIGV0aGVyZXVtLnJlbW92ZUxpc3RlbmVyKCdhY2NvdW50c0NoYW5nZWQnLCAoKSA9PiB7fSk7XG4gICAgICAgIGV0aGVyZXVtLnJlbW92ZUxpc3RlbmVyKCdjaGFpbkNoYW5nZWQnLCAoKSA9PiB7fSk7XG4gICAgICB9XG4gICAgfTtcbiAgfVxuXG4gIGFzeW5jIGZ1bmN0aW9uIGxvYWRDb250cmFjdEFkZHJlc3NlcygpIHtcbiAgICB0cnkge1xuICAgICAgLy8gVHJ5IHRvIGxvYWQgZGVwbG95ZWQgYWRkcmVzc2VzIGZyb20gcHVibGljIGZvbGRlclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2RlcGxveWVkQWRkcmVzc2VzLmpzb24nKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBhZGRyZXNzZXMgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHNldENvbnRyYWN0QWRkcmVzc2VzKGFkZHJlc3Nlcyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdMb2FkZWQgY29udHJhY3QgYWRkcmVzc2VzOicsIGFkZHJlc3Nlcyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0NvdWxkIG5vdCBsb2FkIGRlcGxveWVkIGFkZHJlc3NlcywgdXNpbmcgZGVmYXVsdHMnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKCdDb3VsZCBub3QgbG9hZCBkZXBsb3llZCBhZGRyZXNzZXM6JywgZXJyb3IpO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGZ1bmN0aW9uIGNoZWNrSWZXYWxsZXRJc0Nvbm5lY3RlZCgpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBldGhlcmV1bSB9ID0gd2luZG93IGFzIGFueTtcbiAgICAgIGlmICghZXRoZXJldW0pIHtcbiAgICAgICAgc2V0RXJyb3IoJ01ldGFNYXNrIHRpZGFrIHRlcmRldGVrc2kuIFNpbGFrYW4gaW5zdGFsbCBNZXRhTWFzay4nKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBhY2NvdW50cyA9IGF3YWl0IGV0aGVyZXVtLnJlcXVlc3QoeyBtZXRob2Q6ICdldGhfYWNjb3VudHMnIH0pO1xuICAgICAgaWYgKGFjY291bnRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgc2V0QWNjb3VudChhY2NvdW50c1swXSk7XG4gICAgICAgIGF3YWl0IGNoZWNrTmV0d29yaygpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyB3YWxsZXQgY29ubmVjdGlvbjonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignRXJyb3Igc2FhdCBtZW5nZWNlayBrb25la3NpIHdhbGxldCcpO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGZ1bmN0aW9uIGNoZWNrTmV0d29yaygpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBldGhlcmV1bSB9ID0gd2luZG93IGFzIGFueTtcbiAgICAgIGlmICghZXRoZXJldW0pIHtcbiAgICAgICAgc2V0SXNDb3JyZWN0TmV0d29yayhmYWxzZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY2hhaW5JZCA9IGF3YWl0IGV0aGVyZXVtLnJlcXVlc3QoeyBtZXRob2Q6ICdldGhfY2hhaW5JZCcgfSk7XG4gICAgICBjb25zb2xlLmxvZygnQ3VycmVudCBjaGFpbklkOicsIGNoYWluSWQsICdFeHBlY3RlZDonLCBORVhVU19ORVRXT1JLLmNoYWluSWQpO1xuXG4gICAgICAvLyBDb252ZXJ0IGJvdGggdG8gc2FtZSBmb3JtYXQgZm9yIGNvbXBhcmlzb25cbiAgICAgIGNvbnN0IGN1cnJlbnRDaGFpbklkID0gcGFyc2VJbnQoY2hhaW5JZCwgMTYpO1xuICAgICAgY29uc3QgZXhwZWN0ZWRDaGFpbklkID0gcGFyc2VJbnQoTkVYVVNfTkVUV09SSy5jaGFpbklkLCAxNik7XG5cbiAgICAgIGlmIChjdXJyZW50Q2hhaW5JZCA9PT0gZXhwZWN0ZWRDaGFpbklkKSB7XG4gICAgICAgIHNldElzQ29ycmVjdE5ldHdvcmsodHJ1ZSk7XG4gICAgICAgIHNldEVycm9yKCcnKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBDb25uZWN0ZWQgdG8gY29ycmVjdCBuZXR3b3JrJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRJc0NvcnJlY3ROZXR3b3JrKGZhbHNlKTtcbiAgICAgICAgc2V0RXJyb3IoYFdyb25nIG5ldHdvcmsuIEN1cnJlbnQ6ICR7Y3VycmVudENoYWluSWR9LCBFeHBlY3RlZDogJHtleHBlY3RlZENoYWluSWR9IChOZXh1cyBUZXN0bmV0IElJSSlgKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCBXcm9uZyBuZXR3b3JrIGRldGVjdGVkJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIG5ldHdvcms6JywgZXJyb3IpO1xuICAgICAgc2V0SXNDb3JyZWN0TmV0d29yayhmYWxzZSk7XG4gICAgICBzZXRFcnJvcignRXJyb3IgY2hlY2tpbmcgbmV0d29yaycpO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGZ1bmN0aW9uIHN3aXRjaFRvTmV4dXNOZXR3b3JrKCkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGV0aGVyZXVtIH0gPSB3aW5kb3cgYXMgYW55O1xuICAgICAgc2V0RXJyb3IoJycpO1xuICAgICAgc2V0U3VjY2VzcygnU3dpdGNoaW5nIHRvIE5leHVzIG5ldHdvcmsuLi4nKTtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgZXRoZXJldW0ucmVxdWVzdCh7XG4gICAgICAgICAgbWV0aG9kOiAnd2FsbGV0X3N3aXRjaEV0aGVyZXVtQ2hhaW4nLFxuICAgICAgICAgIHBhcmFtczogW3sgY2hhaW5JZDogTkVYVVNfTkVUV09SSy5jaGFpbklkIH1dLFxuICAgICAgICB9KTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBOZXR3b3JrIHN3aXRjaCByZXF1ZXN0ZWQnKTtcbiAgICAgIH0gY2F0Y2ggKHN3aXRjaEVycm9yOiBhbnkpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1N3aXRjaCBlcnJvciBjb2RlOicsIHN3aXRjaEVycm9yLmNvZGUpO1xuICAgICAgICAvLyBOZXR3b3JrIGJlbHVtIGRpdGFtYmFoa2FuLCB0YW1iYWhrYW4gZHVsdVxuICAgICAgICBpZiAoc3dpdGNoRXJyb3IuY29kZSA9PT0gNDkwMikge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdBZGRpbmcgTmV4dXMgbmV0d29yay4uLicpO1xuICAgICAgICAgIGF3YWl0IGV0aGVyZXVtLnJlcXVlc3Qoe1xuICAgICAgICAgICAgbWV0aG9kOiAnd2FsbGV0X2FkZEV0aGVyZXVtQ2hhaW4nLFxuICAgICAgICAgICAgcGFyYW1zOiBbTkVYVVNfTkVUV09SS10sXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSBOZXR3b3JrIGFkZGVkJyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdGhyb3cgc3dpdGNoRXJyb3I7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gV2FpdCBhIGJpdCBmb3IgbmV0d29yayB0byBzd2l0Y2hcbiAgICAgIHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xuICAgICAgICBhd2FpdCBjaGVja05ldHdvcmsoKTtcbiAgICAgICAgc2V0U3VjY2VzcygnJyk7XG4gICAgICB9LCAxMDAwKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHN3aXRjaGluZyBuZXR3b3JrOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdHYWdhbCBzd2l0Y2gga2UgTmV4dXMgbmV0d29yazogJyArIChlcnJvci5tZXNzYWdlIHx8ICdVbmtub3duIGVycm9yJykpO1xuICAgICAgc2V0U3VjY2VzcygnJyk7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZnVuY3Rpb24gY29ubmVjdFdhbGxldCgpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBldGhlcmV1bSB9ID0gd2luZG93IGFzIGFueTtcbiAgICAgIGlmICghZXRoZXJldW0pIHtcbiAgICAgICAgc2V0RXJyb3IoJ01ldGFNYXNrIHRpZGFrIHRlcmRldGVrc2kuIFNpbGFrYW4gaW5zdGFsbCBNZXRhTWFzay4nKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBhY2NvdW50cyA9IGF3YWl0IGV0aGVyZXVtLnJlcXVlc3QoeyBtZXRob2Q6ICdldGhfcmVxdWVzdEFjY291bnRzJyB9KTtcbiAgICAgIHNldEFjY291bnQoYWNjb3VudHNbMF0pO1xuICAgICAgYXdhaXQgY2hlY2tOZXR3b3JrKCk7XG4gICAgICBzZXRFcnJvcignJyk7XG4gICAgICBzZXRTdWNjZXNzKCdXYWxsZXQgYmVyaGFzaWwgdGVyaHVidW5nIScpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjb25uZWN0aW5nIHdhbGxldDonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignR2FnYWwgbWVuZ2h1YnVuZ2thbiB3YWxsZXQnKTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBmdW5jdGlvbiB1cGRhdGVCYWxhbmNlcygpIHtcbiAgICBpZiAoIWFjY291bnQgfHwgIWlzQ29ycmVjdE5ldHdvcmspIHJldHVybjtcblxuICAgIC8vIENoZWNrIGlmIGNvbnRyYWN0IGFkZHJlc3NlcyBhcmUgdmFsaWRcbiAgICBpZiAoY29udHJhY3RBZGRyZXNzZXMuVG9rZW5BID09PSBERUZBVUxUX0FERFJFU1NFUy5Ub2tlbkEpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdDb250cmFjdCBhZGRyZXNzZXMgbm90IGxvYWRlZCB5ZXQsIHNraXBwaW5nIGJhbGFuY2UgdXBkYXRlJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZXRoZXJldW0gfSA9IHdpbmRvdyBhcyBhbnk7XG4gICAgICBjb25zdCBwcm92aWRlciA9IG5ldyBldGhlcnMuQnJvd3NlclByb3ZpZGVyKGV0aGVyZXVtKTtcblxuICAgICAgY29uc29sZS5sb2coJ1VwZGF0aW5nIGFsbCBiYWxhbmNlcyBmb3I6JywgYWNjb3VudCk7XG5cbiAgICAgIGNvbnN0IHRva2VuQUNvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChjb250cmFjdEFkZHJlc3Nlcy5Ub2tlbkEsIFRva2VuQV9BQkksIHByb3ZpZGVyKTtcbiAgICAgIGNvbnN0IHRva2VuQkNvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChjb250cmFjdEFkZHJlc3Nlcy5Ub2tlbkIsIFRva2VuQl9BQkksIHByb3ZpZGVyKTtcblxuICAgICAgLy8gR2V0IGJhc2ljIHRva2VuIGJhbGFuY2VzXG4gICAgICBjb25zdCBbYmFsYW5jZUEsIGJhbGFuY2VCXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgdG9rZW5BQ29udHJhY3QuYmFsYW5jZU9mKGFjY291bnQpLFxuICAgICAgICB0b2tlbkJDb250cmFjdC5iYWxhbmNlT2YoYWNjb3VudClcbiAgICAgIF0pO1xuXG4gICAgICBzZXRUb2tlbkFCYWxhbmNlKGV0aGVycy5mb3JtYXRFdGhlcihiYWxhbmNlQSkpO1xuICAgICAgc2V0VG9rZW5CQmFsYW5jZShldGhlcnMuZm9ybWF0RXRoZXIoYmFsYW5jZUIpKTtcblxuICAgICAgLy8gR2V0IExQIHRva2VuIGJhbGFuY2UgaWYgTGlxdWlkaXR5UG9vbCBleGlzdHNcbiAgICAgIGlmIChjb250cmFjdEFkZHJlc3Nlcy5MaXF1aWRpdHlQb29sKSB7XG4gICAgICAgIGNvbnN0IGxwQ29udHJhY3QgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KGNvbnRyYWN0QWRkcmVzc2VzLkxpcXVpZGl0eVBvb2wsIExpcXVpZGl0eVBvb2xfQUJJLCBwcm92aWRlcik7XG4gICAgICAgIGNvbnN0IGxwQmFsYW5jZSA9IGF3YWl0IGxwQ29udHJhY3QuYmFsYW5jZU9mKGFjY291bnQpO1xuICAgICAgICBzZXRMcFRva2VuQmFsYW5jZShldGhlcnMuZm9ybWF0RXRoZXIobHBCYWxhbmNlKSk7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldCBzdGFraW5nIGluZm8gaWYgU3Rha2luZyBleGlzdHNcbiAgICAgIGlmIChjb250cmFjdEFkZHJlc3Nlcy5TdGFraW5nKSB7XG4gICAgICAgIGNvbnN0IHN0YWtpbmdDb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoY29udHJhY3RBZGRyZXNzZXMuU3Rha2luZywgU3Rha2luZ19BQkksIHByb3ZpZGVyKTtcbiAgICAgICAgY29uc3QgW3N0YWtlZEJhbCwgZWFybmVkXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgICBzdGFraW5nQ29udHJhY3QuYmFsYW5jZXMoYWNjb3VudCksXG4gICAgICAgICAgc3Rha2luZ0NvbnRyYWN0LmVhcm5lZChhY2NvdW50KVxuICAgICAgICBdKTtcbiAgICAgICAgc2V0U3Rha2VkQmFsYW5jZShldGhlcnMuZm9ybWF0RXRoZXIoc3Rha2VkQmFsKSk7XG4gICAgICAgIHNldEVhcm5lZFJld2FyZHMoZXRoZXJzLmZvcm1hdEV0aGVyKGVhcm5lZCkpO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygnQWxsIGJhbGFuY2VzIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGJhbGFuY2VzOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdFcnJvciBjb25uZWN0aW5nIHRvIGNvbnRyYWN0cycpO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZUxpcXVpZGl0eSgpIHtcbiAgICBpZiAoIWlzQ29ycmVjdE5ldHdvcmspIHJldHVybjtcblxuICAgIC8vIENoZWNrIGlmIGNvbnRyYWN0IGFkZHJlc3NlcyBhcmUgdmFsaWRcbiAgICBpZiAoY29udHJhY3RBZGRyZXNzZXMuVG9rZW5Td2FwID09PSBERUZBVUxUX0FERFJFU1NFUy5Ub2tlblN3YXApIHtcbiAgICAgIGNvbnNvbGUubG9nKCdUb2tlblN3YXAgYWRkcmVzcyBub3QgbG9hZGVkIHlldCwgc2tpcHBpbmcgbGlxdWlkaXR5IHVwZGF0ZScpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGV0aGVyZXVtIH0gPSB3aW5kb3cgYXMgYW55O1xuICAgICAgY29uc3QgcHJvdmlkZXIgPSBuZXcgZXRoZXJzLkJyb3dzZXJQcm92aWRlcihldGhlcmV1bSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCdVcGRhdGluZyBsaXF1aWRpdHkgZm9yIFRva2VuU3dhcDonLCBjb250cmFjdEFkZHJlc3Nlcy5Ub2tlblN3YXApO1xuXG4gICAgICBjb25zdCBzd2FwQ29udHJhY3QgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KGNvbnRyYWN0QWRkcmVzc2VzLlRva2VuU3dhcCwgVG9rZW5Td2FwX0FCSSwgcHJvdmlkZXIpO1xuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBsaXF1aWRpdHlBbW91bnQgPSBhd2FpdCBzd2FwQ29udHJhY3QuZ2V0VG9rZW5CTGlxdWlkaXR5KCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdSYXcgbGlxdWlkaXR5OicsIGxpcXVpZGl0eUFtb3VudC50b1N0cmluZygpKTtcblxuICAgICAgICBzZXRMaXF1aWRpdHkoZXRoZXJzLmZvcm1hdEV0aGVyKGxpcXVpZGl0eUFtb3VudCkpO1xuICAgICAgICBjb25zb2xlLmxvZygnTGlxdWlkaXR5IHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgICB9IGNhdGNoIChjb250cmFjdEVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuU3dhcCBjb250cmFjdCBjYWxsIGVycm9yOicsIGNvbnRyYWN0RXJyb3IpO1xuICAgICAgICBzZXRFcnJvcignRXJyb3IgcmVhZGluZyBsaXF1aWRpdHkuIFRva2VuU3dhcCBjb250cmFjdCBtYXkgbm90IGJlIGRlcGxveWVkIGNvcnJlY3RseS4nKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgbGlxdWlkaXR5OicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdFcnJvciBjb25uZWN0aW5nIHRvIFRva2VuU3dhcCBjb250cmFjdCcpO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGZ1bmN0aW9uIGhhbmRsZVN3YXAoKSB7XG4gICAgaWYgKCFzd2FwQW1vdW50IHx8ICFhY2NvdW50IHx8ICFpc0NvcnJlY3ROZXR3b3JrKSByZXR1cm47XG5cbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKCcnKTtcbiAgICBzZXRTdWNjZXNzKCcnKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGV0aGVyZXVtIH0gPSB3aW5kb3cgYXMgYW55O1xuICAgICAgY29uc3QgcHJvdmlkZXIgPSBuZXcgZXRoZXJzLkJyb3dzZXJQcm92aWRlcihldGhlcmV1bSk7XG4gICAgICBjb25zdCBzaWduZXIgPSBhd2FpdCBwcm92aWRlci5nZXRTaWduZXIoKTtcblxuICAgICAgLy8gRGV0ZXJtaW5lIHdoaWNoIHRva2VucyB0byB1c2VcbiAgICAgIGNvbnN0IGlzVG9rZW5BVG9CID0gc2VsZWN0ZWRUb2tlbkluID09PSAnVG9rZW5BJztcbiAgICAgIGNvbnN0IGlucHV0VG9rZW5BZGRyZXNzID0gaXNUb2tlbkFUb0IgPyBjb250cmFjdEFkZHJlc3Nlcy5Ub2tlbkEgOiBjb250cmFjdEFkZHJlc3Nlcy5Ub2tlbkI7XG4gICAgICBjb25zdCBpbnB1dFRva2VuQ29udHJhY3QgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KGlucHV0VG9rZW5BZGRyZXNzLCBUb2tlbkFfQUJJLCBzaWduZXIpO1xuXG4gICAgICAvLyBVc2UgTGlxdWlkaXR5UG9vbCBmb3Igc3dhcHBpbmcgKG1vcmUgYWR2YW5jZWQgdGhhbiBUb2tlblN3YXApXG4gICAgICBjb25zdCBzd2FwQ29udHJhY3QgPSBjb250cmFjdEFkZHJlc3Nlcy5MaXF1aWRpdHlQb29sXG4gICAgICAgID8gbmV3IGV0aGVycy5Db250cmFjdChjb250cmFjdEFkZHJlc3Nlcy5MaXF1aWRpdHlQb29sLCBMaXF1aWRpdHlQb29sX0FCSSwgc2lnbmVyKVxuICAgICAgICA6IG5ldyBldGhlcnMuQ29udHJhY3QoY29udHJhY3RBZGRyZXNzZXMuVG9rZW5Td2FwLCBUb2tlblN3YXBfQUJJLCBzaWduZXIpO1xuXG4gICAgICBjb25zdCBhbW91bnQgPSBldGhlcnMucGFyc2VFdGhlcihzd2FwQW1vdW50KTtcblxuICAgICAgLy8gQ2hlY2sgYmFsYW5jZVxuICAgICAgY29uc3QgYmFsYW5jZSA9IGF3YWl0IGlucHV0VG9rZW5Db250cmFjdC5iYWxhbmNlT2YoYWNjb3VudCk7XG4gICAgICBpZiAoYmFsYW5jZSA8IGFtb3VudCkge1xuICAgICAgICBzZXRFcnJvcihgU2FsZG8gJHtzZWxlY3RlZFRva2VuSW4gPT09ICdUb2tlbkEnID8gJ1Rva2VuIEEnIDogJ1Rva2VuIEInfSB0aWRhayBtZW5jdWt1cGlgKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBhbGxvd2FuY2VcbiAgICAgIGNvbnN0IHN3YXBBZGRyZXNzID0gY29udHJhY3RBZGRyZXNzZXMuTGlxdWlkaXR5UG9vbCB8fCBjb250cmFjdEFkZHJlc3Nlcy5Ub2tlblN3YXA7XG4gICAgICBjb25zdCBhbGxvd2FuY2UgPSBhd2FpdCBpbnB1dFRva2VuQ29udHJhY3QuYWxsb3dhbmNlKGFjY291bnQsIHN3YXBBZGRyZXNzKTtcblxuICAgICAgaWYgKGFsbG93YW5jZSA8IGFtb3VudCkge1xuICAgICAgICBzZXRTdWNjZXNzKGBNZW55ZXR1anVpIHBlbmdndW5hYW4gJHtzZWxlY3RlZFRva2VuSW4gPT09ICdUb2tlbkEnID8gJ1Rva2VuIEEnIDogJ1Rva2VuIEInfS4uLmApO1xuICAgICAgICBjb25zdCBhcHByb3ZlVHggPSBhd2FpdCBpbnB1dFRva2VuQ29udHJhY3QuYXBwcm92ZShzd2FwQWRkcmVzcywgYW1vdW50KTtcbiAgICAgICAgYXdhaXQgYXBwcm92ZVR4LndhaXQoKTtcbiAgICAgICAgc2V0U3VjY2VzcygnQXBwcm92YWwgYmVyaGFzaWwhIE1lbGFrdWthbiBzd2FwLi4uJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRTdWNjZXNzKCdNZWxha3VrYW4gc3dhcC4uLicpO1xuICAgICAgfVxuXG4gICAgICAvLyBQZXJmb3JtIHN3YXBcbiAgICAgIGlmIChjb250cmFjdEFkZHJlc3Nlcy5MaXF1aWRpdHlQb29sKSB7XG4gICAgICAgIC8vIFVzZSBMaXF1aWRpdHlQb29sIHN3YXAgZnVuY3Rpb25cbiAgICAgICAgY29uc3Qgc3dhcFR4ID0gYXdhaXQgc3dhcENvbnRyYWN0LnN3YXAoaW5wdXRUb2tlbkFkZHJlc3MsIGFtb3VudCwgMCk7XG4gICAgICAgIGF3YWl0IHN3YXBUeC53YWl0KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBVc2Ugb2xkIFRva2VuU3dhcCAob25seSBzdXBwb3J0cyBUb2tlbkEgLT4gVG9rZW5CKVxuICAgICAgICBpZiAoIWlzVG9rZW5BVG9CKSB7XG4gICAgICAgICAgc2V0RXJyb3IoJ1Rva2VuU3dhcCBoYW55YSBtZW5kdWt1bmcgVG9rZW4gQSDihpIgVG9rZW4gQicpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBzd2FwVHggPSBhd2FpdCBzd2FwQ29udHJhY3Quc3dhcChpbnB1dFRva2VuQWRkcmVzcywgYW1vdW50LCAwKTtcbiAgICAgICAgYXdhaXQgc3dhcFR4LndhaXQoKTtcbiAgICAgIH1cblxuICAgICAgc2V0U3VjY2VzcyhgU3dhcCAke3NlbGVjdGVkVG9rZW5JbiA9PT0gJ1Rva2VuQScgPyAnVEtOQScgOiAnVEtOQid9IOKGkiAke3NlbGVjdGVkVG9rZW5PdXQgPT09ICdUb2tlbkEnID8gJ1RLTkEnIDogJ1RLTkInfSBiZXJoYXNpbCEg8J+OiWApO1xuICAgICAgc2V0U3dhcEFtb3VudCgnJyk7XG5cbiAgICAgIC8vIFVwZGF0ZSBiYWxhbmNlc1xuICAgICAgYXdhaXQgdXBkYXRlQmFsYW5jZXMoKTtcbiAgICAgIGF3YWl0IHVwZGF0ZUxpcXVpZGl0eSgpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZHVyaW5nIHN3YXA6JywgZXJyb3IpO1xuICAgICAgaWYgKGVycm9yLmNvZGUgPT09ICdBQ1RJT05fUkVKRUNURUQnKSB7XG4gICAgICAgIHNldEVycm9yKCdUcmFuc2Frc2kgZGliYXRhbGthbiBvbGVoIHVzZXInKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnaW5zdWZmaWNpZW50IGZ1bmRzJykpIHtcbiAgICAgICAgc2V0RXJyb3IoJ1NhbGRvIE5FWCB0aWRhayBtZW5jdWt1cGkgdW50dWsgZ2FzIGZlZScpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3IoJ1N3YXAgZ2FnYWw6ICcgKyAoZXJyb3IucmVhc29uIHx8IGVycm9yLm1lc3NhZ2UpKTtcbiAgICAgIH1cbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZnVuY3Rpb24gaGFuZGxlQWRkTGlxdWlkaXR5KCkge1xuICAgIGlmICghbGlxdWlkaXR5QW1vdW50QSB8fCAhbGlxdWlkaXR5QW1vdW50QiB8fCAhYWNjb3VudCB8fCAhaXNDb3JyZWN0TmV0d29yaykgcmV0dXJuO1xuXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcignJyk7XG4gICAgc2V0U3VjY2VzcygnJyk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBldGhlcmV1bSB9ID0gd2luZG93IGFzIGFueTtcbiAgICAgIGNvbnN0IHByb3ZpZGVyID0gbmV3IGV0aGVycy5Ccm93c2VyUHJvdmlkZXIoZXRoZXJldW0pO1xuICAgICAgY29uc3Qgc2lnbmVyID0gYXdhaXQgcHJvdmlkZXIuZ2V0U2lnbmVyKCk7XG5cbiAgICAgIGNvbnN0IHRva2VuQUNvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChjb250cmFjdEFkZHJlc3Nlcy5Ub2tlbkEsIFRva2VuQV9BQkksIHNpZ25lcik7XG4gICAgICBjb25zdCB0b2tlbkJDb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoY29udHJhY3RBZGRyZXNzZXMuVG9rZW5CLCBUb2tlbkJfQUJJLCBzaWduZXIpO1xuICAgICAgY29uc3QgbHBDb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoY29udHJhY3RBZGRyZXNzZXMuTGlxdWlkaXR5UG9vbCEsIExpcXVpZGl0eVBvb2xfQUJJLCBzaWduZXIpO1xuXG4gICAgICBjb25zdCBhbW91bnRBID0gZXRoZXJzLnBhcnNlRXRoZXIobGlxdWlkaXR5QW1vdW50QSk7XG4gICAgICBjb25zdCBhbW91bnRCID0gZXRoZXJzLnBhcnNlRXRoZXIobGlxdWlkaXR5QW1vdW50Qik7XG5cbiAgICAgIC8vIENoZWNrIGJhbGFuY2VzXG4gICAgICBjb25zdCBbYmFsYW5jZUEsIGJhbGFuY2VCXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgdG9rZW5BQ29udHJhY3QuYmFsYW5jZU9mKGFjY291bnQpLFxuICAgICAgICB0b2tlbkJDb250cmFjdC5iYWxhbmNlT2YoYWNjb3VudClcbiAgICAgIF0pO1xuXG4gICAgICBpZiAoYmFsYW5jZUEgPCBhbW91bnRBKSB7XG4gICAgICAgIHNldEVycm9yKCdTYWxkbyBUb2tlbiBBIHRpZGFrIG1lbmN1a3VwaScpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBpZiAoYmFsYW5jZUIgPCBhbW91bnRCKSB7XG4gICAgICAgIHNldEVycm9yKCdTYWxkbyBUb2tlbiBCIHRpZGFrIG1lbmN1a3VwaScpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIEFwcHJvdmUgdG9rZW5zXG4gICAgICBzZXRTdWNjZXNzKCdNZW55ZXR1anVpIFRva2VuIEEuLi4nKTtcbiAgICAgIGNvbnN0IGFwcHJvdmVBVHggPSBhd2FpdCB0b2tlbkFDb250cmFjdC5hcHByb3ZlKGNvbnRyYWN0QWRkcmVzc2VzLkxpcXVpZGl0eVBvb2wsIGFtb3VudEEpO1xuICAgICAgYXdhaXQgYXBwcm92ZUFUeC53YWl0KCk7XG5cbiAgICAgIHNldFN1Y2Nlc3MoJ01lbnlldHVqdWkgVG9rZW4gQi4uLicpO1xuICAgICAgY29uc3QgYXBwcm92ZUJUeCA9IGF3YWl0IHRva2VuQkNvbnRyYWN0LmFwcHJvdmUoY29udHJhY3RBZGRyZXNzZXMuTGlxdWlkaXR5UG9vbCwgYW1vdW50Qik7XG4gICAgICBhd2FpdCBhcHByb3ZlQlR4LndhaXQoKTtcblxuICAgICAgLy8gQWRkIGxpcXVpZGl0eVxuICAgICAgc2V0U3VjY2VzcygnTWVuYW1iYWhrYW4gbGlrdWlkaXRhcy4uLicpO1xuICAgICAgY29uc3QgYWRkTGlxdWlkaXR5VHggPSBhd2FpdCBscENvbnRyYWN0LmFkZExpcXVpZGl0eShhbW91bnRBLCBhbW91bnRCKTtcbiAgICAgIGF3YWl0IGFkZExpcXVpZGl0eVR4LndhaXQoKTtcblxuICAgICAgc2V0U3VjY2VzcygnTGlrdWlkaXRhcyBiZXJoYXNpbCBkaXRhbWJhaGthbiEg8J+OiScpO1xuICAgICAgc2V0TGlxdWlkaXR5QW1vdW50QSgnJyk7XG4gICAgICBzZXRMaXF1aWRpdHlBbW91bnRCKCcnKTtcblxuICAgICAgYXdhaXQgdXBkYXRlQmFsYW5jZXMoKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFkZGluZyBsaXF1aWRpdHk6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ0dhZ2FsIG1lbmFtYmFoa2FuIGxpa3VpZGl0YXM6ICcgKyAoZXJyb3IucmVhc29uIHx8IGVycm9yLm1lc3NhZ2UpKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZnVuY3Rpb24gaGFuZGxlU3Rha2UoKSB7XG4gICAgaWYgKCFzdGFrZUFtb3VudCB8fCAhYWNjb3VudCB8fCAhaXNDb3JyZWN0TmV0d29yaykgcmV0dXJuO1xuXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcignJyk7XG4gICAgc2V0U3VjY2VzcygnJyk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBldGhlcmV1bSB9ID0gd2luZG93IGFzIGFueTtcbiAgICAgIGNvbnN0IHByb3ZpZGVyID0gbmV3IGV0aGVycy5Ccm93c2VyUHJvdmlkZXIoZXRoZXJldW0pO1xuICAgICAgY29uc3Qgc2lnbmVyID0gYXdhaXQgcHJvdmlkZXIuZ2V0U2lnbmVyKCk7XG5cbiAgICAgIGNvbnN0IGxwQ29udHJhY3QgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KGNvbnRyYWN0QWRkcmVzc2VzLkxpcXVpZGl0eVBvb2whLCBMaXF1aWRpdHlQb29sX0FCSSwgc2lnbmVyKTtcbiAgICAgIGNvbnN0IHN0YWtpbmdDb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoY29udHJhY3RBZGRyZXNzZXMuU3Rha2luZyEsIFN0YWtpbmdfQUJJLCBzaWduZXIpO1xuXG4gICAgICBjb25zdCBhbW91bnQgPSBldGhlcnMucGFyc2VFdGhlcihzdGFrZUFtb3VudCk7XG5cbiAgICAgIC8vIENoZWNrIExQIGJhbGFuY2VcbiAgICAgIGNvbnN0IGxwQmFsYW5jZSA9IGF3YWl0IGxwQ29udHJhY3QuYmFsYW5jZU9mKGFjY291bnQpO1xuICAgICAgaWYgKGxwQmFsYW5jZSA8IGFtb3VudCkge1xuICAgICAgICBzZXRFcnJvcignU2FsZG8gTFAgVG9rZW4gdGlkYWsgbWVuY3VrdXBpJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gQXBwcm92ZSBMUCB0b2tlbnNcbiAgICAgIHNldFN1Y2Nlc3MoJ01lbnlldHVqdWkgTFAgVG9rZW5zLi4uJyk7XG4gICAgICBjb25zdCBhcHByb3ZlVHggPSBhd2FpdCBscENvbnRyYWN0LmFwcHJvdmUoY29udHJhY3RBZGRyZXNzZXMuU3Rha2luZywgYW1vdW50KTtcbiAgICAgIGF3YWl0IGFwcHJvdmVUeC53YWl0KCk7XG5cbiAgICAgIC8vIFN0YWtlXG4gICAgICBzZXRTdWNjZXNzKCdNZWxha3VrYW4gc3Rha2UuLi4nKTtcbiAgICAgIGNvbnN0IHN0YWtlVHggPSBhd2FpdCBzdGFraW5nQ29udHJhY3Quc3Rha2UoYW1vdW50KTtcbiAgICAgIGF3YWl0IHN0YWtlVHgud2FpdCgpO1xuXG4gICAgICBzZXRTdWNjZXNzKCdTdGFrZSBiZXJoYXNpbCEg8J+OiScpO1xuICAgICAgc2V0U3Rha2VBbW91bnQoJycpO1xuXG4gICAgICBhd2FpdCB1cGRhdGVCYWxhbmNlcygpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc3Rha2luZzonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignR2FnYWwgbWVsYWt1a2FuIHN0YWtlOiAnICsgKGVycm9yLnJlYXNvbiB8fCBlcnJvci5tZXNzYWdlKSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGZ1bmN0aW9uIGhhbmRsZUNsYWltKCkge1xuICAgIGlmICghYWNjb3VudCB8fCAhaXNDb3JyZWN0TmV0d29yaykgcmV0dXJuO1xuXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcignJyk7XG4gICAgc2V0U3VjY2VzcygnJyk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBldGhlcmV1bSB9ID0gd2luZG93IGFzIGFueTtcbiAgICAgIGNvbnN0IHByb3ZpZGVyID0gbmV3IGV0aGVycy5Ccm93c2VyUHJvdmlkZXIoZXRoZXJldW0pO1xuICAgICAgY29uc3Qgc2lnbmVyID0gYXdhaXQgcHJvdmlkZXIuZ2V0U2lnbmVyKCk7XG5cbiAgICAgIGNvbnN0IHN0YWtpbmdDb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoY29udHJhY3RBZGRyZXNzZXMuU3Rha2luZyEsIFN0YWtpbmdfQUJJLCBzaWduZXIpO1xuXG4gICAgICBzZXRTdWNjZXNzKCdNZW5na2xhaW0gcmV3YXJkcy4uLicpO1xuICAgICAgY29uc3QgY2xhaW1UeCA9IGF3YWl0IHN0YWtpbmdDb250cmFjdC5jbGFpbVJld2FyZCgpO1xuICAgICAgYXdhaXQgY2xhaW1UeC53YWl0KCk7XG5cbiAgICAgIHNldFN1Y2Nlc3MoJ1Jld2FyZHMgYmVyaGFzaWwgZGlrbGFpbSEg8J+OiScpO1xuXG4gICAgICBhd2FpdCB1cGRhdGVCYWxhbmNlcygpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2xhaW1pbmc6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ0dhZ2FsIG1lbmdrbGFpbSByZXdhcmRzOiAnICsgKGVycm9yLnJlYXNvbiB8fCBlcnJvci5tZXNzYWdlKSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGZvcm1hdEFkZHJlc3MgPSAoYWRkcmVzczogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIGAke2FkZHJlc3Muc2xpY2UoMCwgNil9Li4uJHthZGRyZXNzLnNsaWNlKC00KX1gO1xuICB9O1xuXG4gIGNvbnN0IGNsZWFyTWVzc2FnZXMgPSAoKSA9PiB7XG4gICAgc2V0RXJyb3IoJycpO1xuICAgIHNldFN1Y2Nlc3MoJycpO1xuICB9O1xuXG4gIGNvbnN0IGlzRGVwbG95ZXJBY2NvdW50ID0gYWNjb3VudCAmJiBjb250cmFjdEFkZHJlc3Nlcy5kZXBsb3llciAmJlxuICAgIGFjY291bnQudG9Mb3dlckNhc2UoKSA9PT0gY29udHJhY3RBZGRyZXNzZXMuZGVwbG95ZXIudG9Mb3dlckNhc2UoKTtcblxuICBhc3luYyBmdW5jdGlvbiByZXF1ZXN0VGVzdFRva2VucygpIHtcbiAgICBpZiAoIWFjY291bnQgfHwgIWlzQ29ycmVjdE5ldHdvcmspIHJldHVybjtcblxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IoJycpO1xuICAgIHNldFN1Y2Nlc3MoJ1JlcXVlc3RpbmcgdGVzdCB0b2tlbnMuLi4nKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBDaGVjayBpZiB1c2VyIGlzIHRoZSBkZXBsb3llclxuICAgICAgaWYgKGlzRGVwbG95ZXJBY2NvdW50KSB7XG4gICAgICAgIHNldFN1Y2Nlc3MoJ1lvdSBhcmUgdGhlIGRlcGxveWVyISBZb3UgYWxyZWFkeSBoYXZlIGFsbCB0b2tlbnMg8J+OiScpO1xuICAgICAgICBhd2FpdCB1cGRhdGVCYWxhbmNlcygpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIEZvciBub24tZGVwbG95ZXIgdXNlcnMsIHNob3cgaW5zdHJ1Y3Rpb25zXG4gICAgICBzZXRFcnJvcignJyk7XG4gICAgICBzZXRTdWNjZXNzKCcnKTtcbiAgICAgIGFsZXJ0KGBUbyBnZXQgdGVzdCB0b2tlbnM6XG5cbjEuIFN3aXRjaCB0byBkZXBsb3llciBhY2NvdW50OiAke2NvbnRyYWN0QWRkcmVzc2VzLmRlcGxveWVyfVxuMi4gT3IgYXNrIHRoZSBkZXBsb3llciB0byBzZW5kIHlvdSB0b2tlbnNcbjMuIE9yIHVzZSBhIGZhdWNldCBpZiBhdmFpbGFibGVcblxuWW91ciBjdXJyZW50IGFkZHJlc3M6ICR7YWNjb3VudH1cbkRlcGxveWVyIGFkZHJlc3M6ICR7Y29udHJhY3RBZGRyZXNzZXMuZGVwbG95ZXJ9XG5cblRoZSBkZXBsb3llciBoYXMgMSwwMDAsMDAwIFRva2VuIEEgYXZhaWxhYmxlIGZvciBkaXN0cmlidXRpb24uYCk7XG5cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZXF1ZXN0aW5nIHRlc3QgdG9rZW5zOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gZ2V0IHRlc3QgdG9rZW5zOiAnICsgKGVycm9yLnJlYXNvbiB8fCBlcnJvci5tZXNzYWdlKSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfVxuXG4gIC8vIEJhdGNoIE9wZXJhdGlvbnMgdW50dWsgTWF4aW11bSBUcmFuc2FjdGlvbnNcbiAgYXN5bmMgZnVuY3Rpb24gaGFuZGxlQmF0Y2hTd2FwcygpIHtcbiAgICBpZiAoIWFjY291bnQgfHwgIWlzQ29ycmVjdE5ldHdvcmspIHJldHVybjtcblxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IoJycpO1xuICAgIHNldFN1Y2Nlc3MoJycpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZXRoZXJldW0gfSA9IHdpbmRvdyBhcyBhbnk7XG4gICAgICBjb25zdCBwcm92aWRlciA9IG5ldyBldGhlcnMuQnJvd3NlclByb3ZpZGVyKGV0aGVyZXVtKTtcbiAgICAgIGNvbnN0IHNpZ25lciA9IGF3YWl0IHByb3ZpZGVyLmdldFNpZ25lcigpO1xuXG4gICAgICBjb25zdCB0b2tlbkFDb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoY29udHJhY3RBZGRyZXNzZXMuVG9rZW5BLCBUb2tlbkFfQUJJLCBzaWduZXIpO1xuICAgICAgY29uc3Qgc3dhcENvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChjb250cmFjdEFkZHJlc3Nlcy5Ub2tlblN3YXAsIFRva2VuU3dhcF9BQkksIHNpZ25lcik7XG5cbiAgICAgIC8vIE11bHRpcGxlIHNtYWxsIHN3YXBzIHVudHVrIGdlbmVyYXRlIGJhbnlhayB0cmFuc2Frc2lcbiAgICAgIGNvbnN0IGFtb3VudHMgPSBbJzEwJywgJzIwJywgJzMwJywgJzQwJywgJzUwJ107IC8vIDUgdHJhbnNha3NpIHN3YXBcbiAgICAgIGxldCB0b3RhbEFtb3VudCA9IGV0aGVycy5wYXJzZUV0aGVyKCcwJyk7XG5cbiAgICAgIGZvciAoY29uc3QgYW1vdW50IG9mIGFtb3VudHMpIHtcbiAgICAgICAgdG90YWxBbW91bnQgKz0gZXRoZXJzLnBhcnNlRXRoZXIoYW1vdW50KTtcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgYmFsYW5jZVxuICAgICAgY29uc3QgYmFsYW5jZSA9IGF3YWl0IHRva2VuQUNvbnRyYWN0LmJhbGFuY2VPZihhY2NvdW50KTtcbiAgICAgIGlmIChiYWxhbmNlIDwgdG90YWxBbW91bnQpIHtcbiAgICAgICAgc2V0RXJyb3IoJ1NhbGRvIFRva2VuIEEgdGlkYWsgbWVuY3VrdXBpIHVudHVrIGJhdGNoIHN3YXBzJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gQXBwcm92ZSB0b3RhbCBhbW91bnRcbiAgICAgIHNldFN1Y2Nlc3MoJ01lbnlldHVqdWkgdG90YWwgYW1vdW50IHVudHVrIGJhdGNoIHN3YXBzLi4uJyk7XG4gICAgICBjb25zdCBhcHByb3ZlVHggPSBhd2FpdCB0b2tlbkFDb250cmFjdC5hcHByb3ZlKGNvbnRyYWN0QWRkcmVzc2VzLlRva2VuU3dhcCwgdG90YWxBbW91bnQpO1xuICAgICAgYXdhaXQgYXBwcm92ZVR4LndhaXQoKTtcblxuICAgICAgLy8gRXhlY3V0ZSBtdWx0aXBsZSBzd2Fwc1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhbW91bnRzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIHNldFN1Y2Nlc3MoYE1lbGFrdWthbiBzd2FwICR7aSArIDF9LyR7YW1vdW50cy5sZW5ndGh9Li4uYCk7XG4gICAgICAgIGNvbnN0IGFtb3VudCA9IGV0aGVycy5wYXJzZUV0aGVyKGFtb3VudHNbaV0pO1xuICAgICAgICBjb25zdCBzd2FwVHggPSBhd2FpdCBzd2FwQ29udHJhY3Quc3dhcChjb250cmFjdEFkZHJlc3Nlcy5Ub2tlbkEsIGFtb3VudCwgMCk7XG4gICAgICAgIGF3YWl0IHN3YXBUeC53YWl0KCk7XG5cbiAgICAgICAgLy8gU21hbGwgZGVsYXkgYmV0d2VlbiB0cmFuc2FjdGlvbnNcbiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMDApKTtcbiAgICAgIH1cblxuICAgICAgc2V0U3VjY2VzcyhgQmF0Y2ggc3dhcHMgYmVyaGFzaWwhICR7YW1vdW50cy5sZW5ndGh9IHRyYW5zYWtzaSBjb21wbGV0ZWQg8J+OiWApO1xuICAgICAgYXdhaXQgdXBkYXRlQmFsYW5jZXMoKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGR1cmluZyBiYXRjaCBzd2FwczonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignQmF0Y2ggc3dhcHMgZ2FnYWw6ICcgKyAoZXJyb3IucmVhc29uIHx8IGVycm9yLm1lc3NhZ2UpKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZnVuY3Rpb24gaGFuZGxlVHJhbnNhY3Rpb25TcGFtKCkge1xuICAgIGlmICghYWNjb3VudCB8fCAhaXNDb3JyZWN0TmV0d29yaykgcmV0dXJuO1xuXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcignJyk7XG4gICAgc2V0U3VjY2VzcygnJyk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBldGhlcmV1bSB9ID0gd2luZG93IGFzIGFueTtcbiAgICAgIGNvbnN0IHByb3ZpZGVyID0gbmV3IGV0aGVycy5Ccm93c2VyUHJvdmlkZXIoZXRoZXJldW0pO1xuICAgICAgY29uc3Qgc2lnbmVyID0gYXdhaXQgcHJvdmlkZXIuZ2V0U2lnbmVyKCk7XG5cbiAgICAgIGNvbnN0IHRva2VuQUNvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChjb250cmFjdEFkZHJlc3Nlcy5Ub2tlbkEsIFRva2VuQV9BQkksIHNpZ25lcik7XG4gICAgICBjb25zdCB0b2tlbkJDb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoY29udHJhY3RBZGRyZXNzZXMuVG9rZW5CLCBUb2tlbkJfQUJJLCBzaWduZXIpO1xuXG4gICAgICAvLyBHZW5lcmF0ZSBtYW55IGFwcHJvdmFsIHRyYW5zYWN0aW9uc1xuICAgICAgY29uc3Qgc3BhbUFtb3VudCA9IGV0aGVycy5wYXJzZUV0aGVyKCcxJyk7XG4gICAgICBjb25zdCBjb250cmFjdHMgPSBbXG4gICAgICAgIGNvbnRyYWN0QWRkcmVzc2VzLlRva2VuU3dhcCxcbiAgICAgICAgY29udHJhY3RBZGRyZXNzZXMuTGlxdWlkaXR5UG9vbCxcbiAgICAgICAgY29udHJhY3RBZGRyZXNzZXMuU3Rha2luZ1xuICAgICAgXTtcblxuICAgICAgbGV0IHRyYW5zYWN0aW9uQ291bnQgPSAwO1xuXG4gICAgICBmb3IgKGNvbnN0IGNvbnRyYWN0QWRkciBvZiBjb250cmFjdHMpIHtcbiAgICAgICAgLy8gTXVsdGlwbGUgYXBwcm92YWxzIGZvciBlYWNoIGNvbnRyYWN0XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgMzsgaSsrKSB7XG4gICAgICAgICAgc2V0U3VjY2VzcyhgR2VuZXJhdGluZyBhcHByb3ZhbCB0cmFuc2FjdGlvbiAke3RyYW5zYWN0aW9uQ291bnQgKyAxfS4uLmApO1xuXG4gICAgICAgICAgY29uc3QgYXBwcm92ZVR4ID0gYXdhaXQgdG9rZW5BQ29udHJhY3QuYXBwcm92ZShjb250cmFjdEFkZHIhLCBzcGFtQW1vdW50KTtcbiAgICAgICAgICBhd2FpdCBhcHByb3ZlVHgud2FpdCgpO1xuICAgICAgICAgIHRyYW5zYWN0aW9uQ291bnQrKztcblxuICAgICAgICAgIC8vIEFsc28gYXBwcm92ZSBUb2tlbkJcbiAgICAgICAgICBjb25zdCBhcHByb3ZlQlR4ID0gYXdhaXQgdG9rZW5CQ29udHJhY3QuYXBwcm92ZShjb250cmFjdEFkZHIhLCBzcGFtQW1vdW50KTtcbiAgICAgICAgICBhd2FpdCBhcHByb3ZlQlR4LndhaXQoKTtcbiAgICAgICAgICB0cmFuc2FjdGlvbkNvdW50Kys7XG5cbiAgICAgICAgICAvLyBTbWFsbCBkZWxheVxuICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBzZXRTdWNjZXNzKGBUcmFuc2FjdGlvbiBzcGFtIGNvbXBsZXRlZCEgJHt0cmFuc2FjdGlvbkNvdW50fSB0cmFuc2FjdGlvbnMgZ2VuZXJhdGVkIPCfmoBgKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGR1cmluZyB0cmFuc2FjdGlvbiBzcGFtOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdUcmFuc2FjdGlvbiBzcGFtIGdhZ2FsOiAnICsgKGVycm9yLnJlYXNvbiB8fCBlcnJvci5tZXNzYWdlKSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTUwIHRvLWdyYXktMTAwIHJlbGF0aXZlXCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtNiByaWdodC02IGZsZXggZmxleC1jb2wgaXRlbXMtZW5kIHNwYWNlLXktMlwiPlxuICAgICAgICB7YWNjb3VudCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+Q29ubmVjdGVkOjwvc3Bhbj4ge2Zvcm1hdEFkZHJlc3MoYWNjb3VudCl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTMgaC0zIHJvdW5kZWQtZnVsbCAke2lzQ29ycmVjdE5ldHdvcmsgPyAnYmctZ3JlZW4tNTAwJyA6ICdiZy1yZWQtNTAwJ31gfT48L2Rpdj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgIHtpc0NvcnJlY3ROZXR3b3JrID8gJ05leHVzIFRlc3RuZXQgSUlJJyA6ICdXcm9uZyBOZXR3b3JrJ31cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICB7LyogRGVidWcgaW5mbyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbWF4LXcteHNcIj5cbiAgICAgICAgICA8ZGl2PkV4cGVjdGVkIENoYWluOiAzOTQwICgweEY2NCk8L2Rpdj5cbiAgICAgICAgICA8ZGl2PkNvbnRyYWN0czoge2NvbnRyYWN0QWRkcmVzc2VzLlRva2VuQSAhPT0gREVGQVVMVF9BRERSRVNTRVMuVG9rZW5BID8gJ+KchScgOiAn4p2MJ308L2Rpdj5cbiAgICAgICAgICA8ZGl2PkRlcGxveWVyOiB7Y29udHJhY3RBZGRyZXNzZXMuZGVwbG95ZXIgPyBjb250cmFjdEFkZHJlc3Nlcy5kZXBsb3llci5zbGljZSgwLCA4KSArICcuLi4nIDogJ04vQSd9PC9kaXY+XG4gICAgICAgICAgPGRpdj5Zb3VyIEFkZHJlc3M6IHthY2NvdW50ID8gYWNjb3VudC5zbGljZSgwLCA4KSArICcuLi4nIDogJ04vQSd9PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLXNjcmVlbiBweC00XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTJcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC02eGwgZm9udC1ib2xkIG1iLTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLXB1cnBsZS02MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIj5cbiAgICAgICAgICAgIE5leHVzIFN3YXBcbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTYwMCBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgRGVjZW50cmFsaXplZCB0b2tlbiBleGNoYW5nZSBvbiBOZXh1cyBibG9ja2NoYWluLiBTd2FwIFRva2VuIEEgZm9yIFRva2VuIEIgYXQgYSBmaXhlZCAxOjEgcmF0aW8uXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRXJyb3IvU3VjY2VzcyBNZXNzYWdlcyAqL31cbiAgICAgICAgeyhlcnJvciB8fCBzdWNjZXNzKSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctbWQgbWItNlwiPlxuICAgICAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHRleHQtcmVkLTcwMCBweC00IHB5LTMgcm91bmRlZC1sZyBtYi0zIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxzcGFuPntlcnJvcn08L3NwYW4+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgb25DbGljaz17Y2xlYXJNZXNzYWdlc30gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIGhvdmVyOnRleHQtcmVkLTcwMFwiPlxuICAgICAgICAgICAgICAgICAg4pyVXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHtzdWNjZXNzICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCB0ZXh0LWdyZWVuLTcwMCBweC00IHB5LTMgcm91bmRlZC1sZyBtYi0zIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxzcGFuPntzdWNjZXNzfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIHR5cGU9XCJidXR0b25cIiBvbkNsaWNrPXtjbGVhck1lc3NhZ2VzfSBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTUwMCBob3Zlcjp0ZXh0LWdyZWVuLTcwMFwiPlxuICAgICAgICAgICAgICAgICAg4pyVXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7IWFjY291bnQgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgb25DbGljaz17Y29ubmVjdFdhbGxldH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLXB1cnBsZS02MDAgdGV4dC13aGl0ZSBweC04IHB5LTQgcm91bmRlZC14bCB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgaG92ZXI6ZnJvbS1ibHVlLTcwMCBob3Zlcjp0by1wdXJwbGUtNzAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBDb25uZWN0IFdhbGxldFxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIG10LTRcIj5cbiAgICAgICAgICAgICAgQ29ubmVjdCB5b3VyIE1ldGFNYXNrIHdhbGxldCB0byBzdGFydCBzd2FwcGluZyB0b2tlbnNcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6ICFpc0NvcnJlY3ROZXR3b3JrID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtzd2l0Y2hUb05leHVzTmV0d29ya31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1vcmFuZ2UtNjAwIHRleHQtd2hpdGUgcHgtOCBweS00IHJvdW5kZWQteGwgdGV4dC1sZyBmb250LXNlbWlib2xkIGhvdmVyOmJnLW9yYW5nZS03MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGxcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgU3dpdGNoIHRvIE5leHVzIE5ldHdvcmtcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NoZWNrTmV0d29ya31cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwIHVuZGVybGluZVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgQWxyZWFkeSBzd2l0Y2hlZD8gQ2xpY2sgdG8gcmVmcmVzaFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBtdC00XCI+XG4gICAgICAgICAgICAgIFBsZWFzZSBzd2l0Y2ggdG8gTmV4dXMgVGVzdG5ldCBJSUkgdG8gY29udGludWVcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBtYXgtdy00eGxcIj5cbiAgICAgICAgICAgIHsvKiBCYWxhbmNlcyBPdmVydmlldyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LXhsIHAtNiBib3JkZXIgYm9yZGVyLWdyYXktMjAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi00IHRleHQtY2VudGVyXCI+UG9ydGZvbGlvIE92ZXJ2aWV3PC9oMj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTQgYmctYmx1ZS01MCByb3VuZGVkLXhsXCI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItMVwiPlRva2VuIEE8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC1sZyBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7cGFyc2VGbG9hdCh0b2tlbkFCYWxhbmNlKS50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+VEtOQTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNCBiZy1wdXJwbGUtNTAgcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1iLTFcIj5Ub2tlbiBCPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tb25vIHRleHQtbGcgZm9udC1ib2xkIHRleHQtcHVycGxlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7cGFyc2VGbG9hdCh0b2tlbkJCYWxhbmNlKS50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+VEtOQjwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNCBiZy1ncmVlbi01MCByb3VuZGVkLXhsXCI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItMVwiPkxQIFRva2VuczwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7cGFyc2VGbG9hdChscFRva2VuQmFsYW5jZSkudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPk5MUDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNCBiZy1vcmFuZ2UtNTAgcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1iLTFcIj5TdGFrZWQ8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC1sZyBmb250LWJvbGQgdGV4dC1vcmFuZ2UtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtwYXJzZUZsb2F0KHN0YWtlZEJhbGFuY2UpLnRvRml4ZWQoMil9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5OTFA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICB7cGFyc2VGbG9hdChlYXJuZWRSZXdhcmRzKSA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWNlbnRlciBwLTMgYmcteWVsbG93LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlBlbmRpbmcgUmV3YXJkczwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXllbGxvdy02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge3BhcnNlRmxvYXQoZWFybmVkUmV3YXJkcykudG9GaXhlZCg0KX0gVEtOQlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBUYWJzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLTJ4bCBzaGFkb3cteGwgYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAge1tcbiAgICAgICAgICAgICAgICAgIHsgaWQ6ICdzd2FwJywgbGFiZWw6ICdTd2FwJywgaWNvbjogJ/CflIQnIH0sXG4gICAgICAgICAgICAgICAgICB7IGlkOiAnbGlxdWlkaXR5JywgbGFiZWw6ICdBZGQgTGlxdWlkaXR5JywgaWNvbjogJ/CfkqcnIH0sXG4gICAgICAgICAgICAgICAgICB7IGlkOiAnc3Rha2UnLCBsYWJlbDogJ1N0YWtlJywgaWNvbjogJ/Cfj6YnIH0sXG4gICAgICAgICAgICAgICAgICB7IGlkOiAnY2xhaW0nLCBsYWJlbDogJ0NsYWltJywgaWNvbjogJ/CfjoEnIH0sXG4gICAgICAgICAgICAgICAgICB7IGlkOiAnYmF0Y2gnLCBsYWJlbDogJ0JhdGNoIE9wcycsIGljb246ICfimqEnIH1cbiAgICAgICAgICAgICAgICBdLm1hcCgodGFiKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGtleT17dGFiLmlkfVxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKHRhYi5pZCl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweC00IHB5LTMgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gdGFiLmlkXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICd0ZXh0LWJsdWUtNjAwIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIGJnLWJsdWUtNTAnXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS01MCdcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTJcIj57dGFiLmljb259PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICB7dGFiLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBUYWIgQ29udGVudCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnc3dhcCcgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi00XCI+U3dhcCBUb2tlbnM8L2gzPlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBUb2tlbiBTZWxlY3Rpb24gKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogRnJvbSBUb2tlbiAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+RnJvbTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRUb2tlbklufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VsZWN0ZWRUb2tlbkluKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctd2hpdGUgYm9yZGVyLTIgYm9yZGVyLWdyYXktMzAwIHB4LTMgcHktMiByb3VuZGVkLWxnIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiVG9rZW5BXCI+VEtOQSAtIFRva2VuIEE8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiVG9rZW5CXCI+VEtOQiAtIFRva2VuIEI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJnLWdyYXktMTAwIHJvdW5kZWQtbGcgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEJhbGFuY2U6IHtzZWxlY3RlZFRva2VuSW4gPT09ICdUb2tlbkEnID8gcGFyc2VGbG9hdCh0b2tlbkFCYWxhbmNlKS50b0ZpeGVkKDQpIDogcGFyc2VGbG9hdCh0b2tlbkJCYWxhbmNlKS50b0ZpeGVkKDQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIFN3YXAgRGlyZWN0aW9uIEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0ZW1wID0gc2VsZWN0ZWRUb2tlbkluO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkVG9rZW5JbihzZWxlY3RlZFRva2VuT3V0KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFRva2VuT3V0KHRlbXApO1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgYmctYmx1ZS0xMDAgaG92ZXI6YmctYmx1ZS0yMDAgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIPCflIRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIFRvIFRva2VuICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5UbzwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRUb2tlbk91dH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlbGVjdGVkVG9rZW5PdXQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBiZy13aGl0ZSBib3JkZXItMiBib3JkZXItZ3JheS0zMDAgcHgtMyBweS0yIHJvdW5kZWQtbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJUb2tlbkJcIj5US05CIC0gVG9rZW4gQjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJUb2tlbkFcIj5US05BIC0gVG9rZW4gQTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC0zIHB5LTIgYmctZ3JheS0xMDAgcm91bmRlZC1sZyB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQmFsYW5jZToge3NlbGVjdGVkVG9rZW5PdXQgPT09ICdUb2tlbkEnID8gcGFyc2VGbG9hdCh0b2tlbkFCYWxhbmNlKS50b0ZpeGVkKDQpIDogcGFyc2VGbG9hdCh0b2tlbkJCYWxhbmNlKS50b0ZpeGVkKDQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogU3dhcCBBbW91bnQgSW5wdXQgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgQW1vdW50IHRvIFN3YXBcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3N3YXBBbW91bnR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdTd2FwIGlucHV0IGNoYW5nZWQ6JywgZS50YXJnZXQudmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFN3YXBBbW91bnQoZS50YXJnZXQudmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjAuMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy13aGl0ZSBib3JkZXItMiBib3JkZXItZ3JheS0zMDAgcHgtNCBweS00IHByLTIwIHJvdW5kZWQteGwgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCBmb250LW1vbm8gdGV4dC1sZyB0ZXh0LWdyYXktOTAwIHBsYWNlaG9sZGVyLWdyYXktNDAwIHNoYWRvdy1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3RlcD1cIjAuMDFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBhdXRvQ29tcGxldGU9XCJvZmZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtZ3JheS01MDAgZm9udC1tZWRpdW0gcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRUb2tlbkluID09PSAnVG9rZW5BJyA/ICdUS05BJyA6ICdUS05CJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBFeGNoYW5nZSByYXRlOiAxIHtzZWxlY3RlZFRva2VuSW4gPT09ICdUb2tlbkEnID8gJ1RLTkEnIDogJ1RLTkInfSDiiYggMSB7c2VsZWN0ZWRUb2tlbk91dCA9PT0gJ1Rva2VuQScgPyAnVEtOQScgOiAnVEtOQid9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTd2FwQW1vdW50KCcxMCcpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTYwMCBweC0yIHB5LTEgcm91bmRlZCBob3ZlcjpiZy1ibHVlLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAxMFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFN3YXBBbW91bnQoJzEwMCcpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTYwMCBweC0yIHB5LTEgcm91bmRlZCBob3ZlcjpiZy1ibHVlLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAxMDBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTd2FwQW1vdW50KHNlbGVjdGVkVG9rZW5JbiA9PT0gJ1Rva2VuQScgPyB0b2tlbkFCYWxhbmNlIDogdG9rZW5CQmFsYW5jZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyBiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtNjAwIHB4LTIgcHktMSByb3VuZGVkIGhvdmVyOmJnLWJsdWUtMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCAoc2VsZWN0ZWRUb2tlbkluID09PSAnVG9rZW5BJyA/IHBhcnNlRmxvYXQodG9rZW5BQmFsYW5jZSkgPT09IDAgOiBwYXJzZUZsb2F0KHRva2VuQkJhbGFuY2UpID09PSAwKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1heFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBBdmFpbGFibGU6IHtzZWxlY3RlZFRva2VuSW4gPT09ICdUb2tlbkEnID8gcGFyc2VGbG9hdCh0b2tlbkFCYWxhbmNlKS50b0ZpeGVkKDQpIDogcGFyc2VGbG9hdCh0b2tlbkJCYWxhbmNlKS50b0ZpeGVkKDQpfSB7c2VsZWN0ZWRUb2tlbkluID09PSAnVG9rZW5BJyA/ICdUS05BJyA6ICdUS05CJ30gfCBJbnB1dDogXCJ7c3dhcEFtb3VudH1cIlxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIExpcXVpZGl0eSBJbmZvICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTYgcC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlBvb2wgTGlxdWlkaXR5PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3BhcnNlRmxvYXQobGlxdWlkaXR5KS50b0ZpeGVkKDIpfSBUb2tlbiBCXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogU3dhcCBCdXR0b24gKi99XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTd2FwfVxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nIHx8ICFzd2FwQW1vdW50IHx8IHBhcnNlRmxvYXQoc3dhcEFtb3VudCkgPD0gMH1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLXB1cnBsZS02MDAgdGV4dC13aGl0ZSBweS00IHJvdW5kZWQteGwgdGV4dC1sZyBmb250LXNlbWlib2xkIGhvdmVyOmZyb20tYmx1ZS03MDAgaG92ZXI6dG8tcHVycGxlLTcwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZGlzYWJsZWQ6ZnJvbS1ncmF5LTQwMCBkaXNhYmxlZDp0by1ncmF5LTQwMCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7bG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNSB3LTUgYm9yZGVyLWItMiBib3JkZXItd2hpdGUgbXItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICBQcm9jZXNzaW5nLi4uXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgYFN3YXAgJHtzZWxlY3RlZFRva2VuSW4gPT09ICdUb2tlbkEnID8gJ1RLTkEnIDogJ1RLTkInfSDihpIgJHtzZWxlY3RlZFRva2VuT3V0ID09PSAnVG9rZW5BJyA/ICdUS05BJyA6ICdUS05CJ31gXG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2xpcXVpZGl0eScgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi00XCI+QWRkIExpcXVpZGl0eTwvaDM+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTQgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgVG9rZW4gQSBBbW91bnRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtsaXF1aWRpdHlBbW91bnRBfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0xpcXVpZGl0eSBBIGlucHV0IGNoYW5nZWQ6JywgZS50YXJnZXQudmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0TGlxdWlkaXR5QW1vdW50QShlLnRhcmdldC52YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjAuMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXdoaXRlIGJvcmRlci0yIGJvcmRlci1ncmF5LTMwMCBweC00IHB5LTMgcHItMTYgcm91bmRlZC14bCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwIGZvbnQtbW9ubyB0ZXh0LWxnIHRleHQtZ3JheS05MDAgcGxhY2Vob2xkZXItZ3JheS00MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjAxXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdXRvQ29tcGxldGU9XCJvZmZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LWdyYXktNTAwIGZvbnQtbWVkaXVtIHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBUS05BXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBBdmFpbGFibGU6IHtwYXJzZUZsb2F0KHRva2VuQUJhbGFuY2UpLnRvRml4ZWQoNCl9IFRLTkEgfCBDdXJyZW50OiBcIntsaXF1aWRpdHlBbW91bnRBfVwiXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFRva2VuIEIgQW1vdW50XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bGlxdWlkaXR5QW1vdW50Qn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdMaXF1aWRpdHkgQiBpbnB1dCBjaGFuZ2VkOicsIGUudGFyZ2V0LnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldExpcXVpZGl0eUFtb3VudEIoZS50YXJnZXQudmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwLjBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy13aGl0ZSBib3JkZXItMiBib3JkZXItZ3JheS0zMDAgcHgtNCBweS0zIHByLTE2IHJvdW5kZWQteGwgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCBmb250LW1vbm8gdGV4dC1sZyB0ZXh0LWdyYXktOTAwIHBsYWNlaG9sZGVyLWdyYXktNDAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGVwPVwiMC4wMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0NvbXBsZXRlPVwib2ZmXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgdGV4dC1ncmF5LTUwMCBmb250LW1lZGl1bSBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgVEtOQlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQXZhaWxhYmxlOiB7cGFyc2VGbG9hdCh0b2tlbkJCYWxhbmNlKS50b0ZpeGVkKDQpfSBUS05CIHwgQ3VycmVudDogXCJ7bGlxdWlkaXR5QW1vdW50Qn1cIlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWRkTGlxdWlkaXR5fVxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nIHx8ICFsaXF1aWRpdHlBbW91bnRBIHx8ICFsaXF1aWRpdHlBbW91bnRCfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNjAwIHRvLWJsdWUtNjAwIHRleHQtd2hpdGUgcHktNCByb3VuZGVkLXhsIHRleHQtbGcgZm9udC1zZW1pYm9sZCBob3Zlcjpmcm9tLWdyZWVuLTcwMCBob3Zlcjp0by1ibHVlLTcwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZGlzYWJsZWQ6ZnJvbS1ncmF5LTQwMCBkaXNhYmxlZDp0by1ncmF5LTQwMCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7bG9hZGluZyA/ICdQcm9jZXNzaW5nLi4uJyA6ICdBZGQgTGlxdWlkaXR5J31cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3N0YWtlJyAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTRcIj5TdGFrZSBMUCBUb2tlbnM8L2gzPlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgU3Rha2UgQW1vdW50IChMUCBUb2tlbnMpXG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzdGFrZUFtb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1N0YWtlIGlucHV0IGNoYW5nZWQ6JywgZS50YXJnZXQudmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFN0YWtlQW1vdW50KGUudGFyZ2V0LnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwLjBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctd2hpdGUgYm9yZGVyLTIgYm9yZGVyLWdyYXktMzAwIHB4LTQgcHktMyBwci0xNiByb3VuZGVkLXhsIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDAgZm9udC1tb25vIHRleHQtbGcgdGV4dC1ncmF5LTkwMCBwbGFjZWhvbGRlci1ncmF5LTQwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3RlcD1cIjAuMDFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBhdXRvQ29tcGxldGU9XCJvZmZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtZ3JheS01MDAgZm9udC1tZWRpdW0gcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBOTFBcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBBdmFpbGFibGU6IHtwYXJzZUZsb2F0KGxwVG9rZW5CYWxhbmNlKS50b0ZpeGVkKDQpfSBOTFAgfCBDdXJyZW50OiBcIntzdGFrZUFtb3VudH1cIlxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVN0YWtlfVxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nIHx8ICFzdGFrZUFtb3VudCB8fCBwYXJzZUZsb2F0KGxwVG9rZW5CYWxhbmNlKSA9PT0gMH1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLW9yYW5nZS02MDAgdG8tcmVkLTYwMCB0ZXh0LXdoaXRlIHB5LTQgcm91bmRlZC14bCB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgaG92ZXI6ZnJvbS1vcmFuZ2UtNzAwIGhvdmVyOnRvLXJlZC03MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGRpc2FibGVkOmZyb20tZ3JheS00MDAgZGlzYWJsZWQ6dG8tZ3JheS00MDAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGxcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge2xvYWRpbmcgPyAnUHJvY2Vzc2luZy4uLicgOiAnU3Rha2UgTFAgVG9rZW5zJ31cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2NsYWltJyAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTRcIj5DbGFpbSBSZXdhcmRzPC9oMz5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTYgcC00IGJnLXllbGxvdy01MCByb3VuZGVkLXhsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1iLTJcIj5QZW5kaW5nIFJld2FyZHM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tb25vIHRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXllbGxvdy02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtwYXJzZUZsb2F0KGVhcm5lZFJld2FyZHMpLnRvRml4ZWQoNil9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlRLTkI8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNCBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5TdGFrZWQ8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC1sZyBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtwYXJzZUZsb2F0KHN0YWtlZEJhbGFuY2UpLnRvRml4ZWQoNCl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5OTFA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5BUFk8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH41MCVcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPkVzdGltYXRlZDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsYWltfVxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nIHx8IHBhcnNlRmxvYXQoZWFybmVkUmV3YXJkcykgPT09IDB9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNjAwIHRvLW9yYW5nZS02MDAgdGV4dC13aGl0ZSBweS00IHJvdW5kZWQteGwgdGV4dC1sZyBmb250LXNlbWlib2xkIGhvdmVyOmZyb20teWVsbG93LTcwMCBob3Zlcjp0by1vcmFuZ2UtNzAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBkaXNhYmxlZDpmcm9tLWdyYXktNDAwIGRpc2FibGVkOnRvLWdyYXktNDAwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtsb2FkaW5nID8gJ1Byb2Nlc3NpbmcuLi4nIDogJ0NsYWltIFJld2FyZHMnfVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnYmF0Y2gnICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPkJhdGNoIE9wZXJhdGlvbnM8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIEdlbmVyYXRlIG11bHRpcGxlIHRyYW5zYWN0aW9ucyBmb3IgbWF4aW11bSBOZXh1cyB0ZXN0bmV0IGludGVyYWN0aW9uIVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogQmF0Y2ggU3dhcHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctYmx1ZS01MCByb3VuZGVkLXhsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtODAwIG1iLTJcIj7wn5SEIEJhdGNoIFN3YXBzPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTcwMCBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEV4ZWN1dGUgNSBzZXBhcmF0ZSBzd2FwIHRyYW5zYWN0aW9ucyAoMTAsIDIwLCAzMCwgNDAsIDUwIFRLTkEpXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVCYXRjaFN3YXBzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCBwYXJzZUZsb2F0KHRva2VuQUJhbGFuY2UpIDwgMTUwfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweS0zIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGRpc2FibGVkOmJnLWdyYXktNDAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2xvYWRpbmcgPyAnUHJvY2Vzc2luZy4uLicgOiAnRXhlY3V0ZSBCYXRjaCBTd2FwcyAoNSBUWHMpJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNjAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUmVxdWlyZXM6IDE1MCBUS05BIG1pbmltdW1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBUcmFuc2FjdGlvbiBTcGFtICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJnLXB1cnBsZS01MCByb3VuZGVkLXhsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXB1cnBsZS04MDAgbWItMlwiPuKaoSBUcmFuc2FjdGlvbiBTcGFtPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wdXJwbGUtNzAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgR2VuZXJhdGUgMTggYXBwcm92YWwgdHJhbnNhY3Rpb25zIGFjcm9zcyBhbGwgY29udHJhY3RzXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVUcmFuc2FjdGlvblNwYW19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctcHVycGxlLTYwMCB0ZXh0LXdoaXRlIHB5LTMgcm91bmRlZC1sZyBmb250LW1lZGl1bSBob3ZlcjpiZy1wdXJwbGUtNzAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBkaXNhYmxlZDpiZy1ncmF5LTQwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtsb2FkaW5nID8gJ1Byb2Nlc3NpbmcuLi4nIDogJ0dlbmVyYXRlIFRyYW5zYWN0aW9uIFNwYW0gKDE4IFRYcyknfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtcHVycGxlLTYwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEdlbmVyYXRlcyBtYW55IGFwcHJvdmFsIHRyYW5zYWN0aW9uc1xuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIFRyYW5zYWN0aW9uIENvdW50ZXIgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctZ3JlZW4tNTAgcm91bmRlZC14bCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi04MDAgbWItMlwiPvCfk4ogVHJhbnNhY3Rpb24gQ291bnRlcjwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgRXN0aW1hdGVkIHRyYW5zYWN0aW9ucyBwZXIgZnVsbCBjeWNsZTpcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtMiB0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcC0yIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGRcIj5CYXNpYyBGbG93PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj44LTEwIFRYczwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBwLTIgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZFwiPldpdGggQmF0Y2hlczwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+MjUrIFRYczwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIEluc3RydWN0aW9ucyAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy15ZWxsb3ctNTAgcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC15ZWxsb3ctODAwIG1iLTJcIj7wn5KhIFBybyBUaXBzPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQteWVsbG93LTcwMCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiBVc2UgYmF0Y2ggb3BlcmF0aW9ucyB0byBnZW5lcmF0ZSBtYW55IHRyYW5zYWN0aW9ucyBxdWlja2x5PC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiBFYWNoIG9wZXJhdGlvbiBjcmVhdGVzIG11bHRpcGxlIGJsb2NrY2hhaW4gaW50ZXJhY3Rpb25zPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiBQZXJmZWN0IGZvciBtYXhpbWl6aW5nIE5leHVzIHRlc3RuZXQgY29udHJpYnV0aW9uPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiBNb25pdG9yIHlvdXIgdHJhbnNhY3Rpb24gY291bnQgaW4gTWV0YU1hc2s8L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogVGVzdCBUb2tlbnMgSW5mbyAqL31cbiAgICAgICAgICAgICAge3BhcnNlRmxvYXQodG9rZW5BQmFsYW5jZSkgPT09IDAgJiYgYWN0aXZlVGFiID09PSAnc3dhcCcgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtNiBtYi02IHAtNCBiZy15ZWxsb3ctNTAgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC15ZWxsb3ctODAwIG1iLTJcIj5OZWVkIFRlc3QgVG9rZW5zPzwvaDM+XG4gICAgICAgICAgICAgICAgICB7aXNEZXBsb3llckFjY291bnQgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXllbGxvdy03MDAgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgWW91IGFyZSB0aGUgZGVwbG95ZXIhIFlvdSBoYXZlIDEsMDAwLDAwMCBUb2tlbiBBIGF2YWlsYWJsZS5cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXt1cGRhdGVCYWxhbmNlc31cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXllbGxvdy02MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIGhvdmVyOmJnLXllbGxvdy03MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBSZWZyZXNoIEJhbGFuY2VcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC15ZWxsb3ctNzAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFRvIGdldCB0ZXN0IHRva2Vucywgc3dpdGNoIHRvIHRoZSBkZXBsb3llciBhY2NvdW50IG9yIGFzayBmb3IgYSB0cmFuc2ZlcjpcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQteWVsbG93LTYwMCBtYi0zIGZvbnQtbW9ubyBiZy15ZWxsb3ctMTAwIHAtMiByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBEZXBsb3llcjoge2NvbnRyYWN0QWRkcmVzc2VzLmRlcGxveWVyPy5zbGljZSgwLCAyMCl9Li4uXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtyZXF1ZXN0VGVzdFRva2Vuc31cbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmcteWVsbG93LTYwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW0gaG92ZXI6YmcteWVsbG93LTcwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZGlzYWJsZWQ6YmctZ3JheS00MDBcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIFNob3cgSW5zdHJ1Y3Rpb25zXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogSW5zdHJ1Y3Rpb25zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IHRleHQtY2VudGVyIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgbWItMlwiPkRlRmkgRmxvdyBHdWlkZTo8L2gzPlxuICAgICAgICAgICAgICA8b2wgY2xhc3NOYW1lPVwidGV4dC1zbSBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICA8bGk+MS4gPHN0cm9uZz5Td2FwPC9zdHJvbmc+OiBUcmFkZSBUb2tlbiBBIOKHhCBUb2tlbiBCPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+Mi4gPHN0cm9uZz5BZGQgTGlxdWlkaXR5PC9zdHJvbmc+OiBQcm92aWRlIGJvdGggdG9rZW5zIOKGkiBHZXQgTFAgdG9rZW5zPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+My4gPHN0cm9uZz5TdGFrZTwvc3Ryb25nPjogU3Rha2UgTFAgdG9rZW5zIOKGkiBFYXJuIHJld2FyZHM8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT40LiA8c3Ryb25nPkNsYWltPC9zdHJvbmc+OiBDb2xsZWN0IHlvdXIgZWFybmVkIHJld2FyZHM8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT41LiA8c3Ryb25nPlJlcGVhdDwvc3Ryb25nPjogTW9yZSB0cmFuc2FjdGlvbnMgPSBNb3JlIE5leHVzIGludGVyYWN0aW9uITwvbGk+XG4gICAgICAgICAgICAgIDwvb2w+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiZXRoZXJzIiwiVG9rZW5BX0FCSSIsIlRva2VuQl9BQkkiLCJUb2tlblN3YXBfQUJJIiwiTGlxdWlkaXR5UG9vbF9BQkkiLCJTdGFraW5nX0FCSSIsIk5FWFVTX05FVFdPUksiLCJjaGFpbklkIiwiY2hhaW5OYW1lIiwibmF0aXZlQ3VycmVuY3kiLCJuYW1lIiwic3ltYm9sIiwiZGVjaW1hbHMiLCJycGNVcmxzIiwiYmxvY2tFeHBsb3JlclVybHMiLCJERUZBVUxUX0FERFJFU1NFUyIsIlRva2VuQSIsIlRva2VuQiIsIlRva2VuU3dhcCIsIkhvbWUiLCJjb250cmFjdEFkZHJlc3NlcyIsImFjY291bnQiLCJzZXRBY2NvdW50IiwidG9rZW5BQmFsYW5jZSIsInNldFRva2VuQUJhbGFuY2UiLCJ0b2tlbkJCYWxhbmNlIiwic2V0VG9rZW5CQmFsYW5jZSIsImxwVG9rZW5CYWxhbmNlIiwic2V0THBUb2tlbkJhbGFuY2UiLCJzdGFrZWRCYWxhbmNlIiwic2V0U3Rha2VkQmFsYW5jZSIsImVhcm5lZFJld2FyZHMiLCJzZXRFYXJuZWRSZXdhcmRzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwic3VjY2VzcyIsInNldFN1Y2Nlc3MiLCJzZXRDb250cmFjdEFkZHJlc3NlcyIsImxpcXVpZGl0eSIsInNldExpcXVpZGl0eSIsImlzQ29ycmVjdE5ldHdvcmsiLCJzZXRJc0NvcnJlY3ROZXR3b3JrIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwic3dhcEFtb3VudCIsInNldFN3YXBBbW91bnQiLCJsaXF1aWRpdHlBbW91bnRBIiwic2V0TGlxdWlkaXR5QW1vdW50QSIsImxpcXVpZGl0eUFtb3VudEIiLCJzZXRMaXF1aWRpdHlBbW91bnRCIiwic3Rha2VBbW91bnQiLCJzZXRTdGFrZUFtb3VudCIsIndpdGhkcmF3QW1vdW50Iiwic2V0V2l0aGRyYXdBbW91bnQiLCJzZWxlY3RlZFRva2VuSW4iLCJzZXRTZWxlY3RlZFRva2VuSW4iLCJzZWxlY3RlZFRva2VuT3V0Iiwic2V0U2VsZWN0ZWRUb2tlbk91dCIsImF2YWlsYWJsZVRva2VucyIsInNldEF2YWlsYWJsZVRva2VucyIsImNoZWNrSWZXYWxsZXRJc0Nvbm5lY3RlZCIsImxvYWRDb250cmFjdEFkZHJlc3NlcyIsInNldHVwRXZlbnRMaXN0ZW5lcnMiLCJ1cGRhdGVCYWxhbmNlcyIsInVwZGF0ZUxpcXVpZGl0eSIsImxvYWRBdmFpbGFibGVUb2tlbnMiLCJldGhlcmV1bSIsIndpbmRvdyIsInByb3ZpZGVyIiwiQnJvd3NlclByb3ZpZGVyIiwidG9rZW5zIiwiYWRkcmVzcyIsImJhbGFuY2UiLCJMaXF1aWRpdHlQb29sIiwicHVzaCIsImNvbnNvbGUiLCJvbiIsImFjY291bnRzIiwibGVuZ3RoIiwiY2hlY2tOZXR3b3JrIiwibG9nIiwibG9jYXRpb24iLCJyZWxvYWQiLCJyZW1vdmVMaXN0ZW5lciIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImFkZHJlc3NlcyIsImpzb24iLCJ3YXJuIiwicmVxdWVzdCIsIm1ldGhvZCIsImN1cnJlbnRDaGFpbklkIiwicGFyc2VJbnQiLCJleHBlY3RlZENoYWluSWQiLCJzd2l0Y2hUb05leHVzTmV0d29yayIsInBhcmFtcyIsInN3aXRjaEVycm9yIiwiY29kZSIsInNldFRpbWVvdXQiLCJtZXNzYWdlIiwiY29ubmVjdFdhbGxldCIsInRva2VuQUNvbnRyYWN0IiwiQ29udHJhY3QiLCJ0b2tlbkJDb250cmFjdCIsImJhbGFuY2VBIiwiYmFsYW5jZUIiLCJQcm9taXNlIiwiYWxsIiwiYmFsYW5jZU9mIiwiZm9ybWF0RXRoZXIiLCJscENvbnRyYWN0IiwibHBCYWxhbmNlIiwiU3Rha2luZyIsInN0YWtpbmdDb250cmFjdCIsInN0YWtlZEJhbCIsImVhcm5lZCIsImJhbGFuY2VzIiwic3dhcENvbnRyYWN0IiwibGlxdWlkaXR5QW1vdW50IiwiZ2V0VG9rZW5CTGlxdWlkaXR5IiwidG9TdHJpbmciLCJjb250cmFjdEVycm9yIiwiaGFuZGxlU3dhcCIsInNpZ25lciIsImdldFNpZ25lciIsImlzVG9rZW5BVG9CIiwiaW5wdXRUb2tlbkFkZHJlc3MiLCJpbnB1dFRva2VuQ29udHJhY3QiLCJhbW91bnQiLCJwYXJzZUV0aGVyIiwic3dhcEFkZHJlc3MiLCJhbGxvd2FuY2UiLCJhcHByb3ZlVHgiLCJhcHByb3ZlIiwid2FpdCIsInN3YXBUeCIsInN3YXAiLCJpbmNsdWRlcyIsInJlYXNvbiIsImhhbmRsZUFkZExpcXVpZGl0eSIsImFtb3VudEEiLCJhbW91bnRCIiwiYXBwcm92ZUFUeCIsImFwcHJvdmVCVHgiLCJhZGRMaXF1aWRpdHlUeCIsImFkZExpcXVpZGl0eSIsImhhbmRsZVN0YWtlIiwic3Rha2VUeCIsInN0YWtlIiwiaGFuZGxlQ2xhaW0iLCJjbGFpbVR4IiwiY2xhaW1SZXdhcmQiLCJmb3JtYXRBZGRyZXNzIiwic2xpY2UiLCJjbGVhck1lc3NhZ2VzIiwiaXNEZXBsb3llckFjY291bnQiLCJkZXBsb3llciIsInRvTG93ZXJDYXNlIiwicmVxdWVzdFRlc3RUb2tlbnMiLCJhbGVydCIsImhhbmRsZUJhdGNoU3dhcHMiLCJhbW91bnRzIiwidG90YWxBbW91bnQiLCJpIiwicmVzb2x2ZSIsImhhbmRsZVRyYW5zYWN0aW9uU3BhbSIsInNwYW1BbW91bnQiLCJjb250cmFjdHMiLCJ0cmFuc2FjdGlvbkNvdW50IiwiY29udHJhY3RBZGRyIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsImgxIiwicCIsImJ1dHRvbiIsInR5cGUiLCJvbkNsaWNrIiwiaDIiLCJwYXJzZUZsb2F0IiwidG9GaXhlZCIsImlkIiwibGFiZWwiLCJpY29uIiwibWFwIiwidGFiIiwiaDMiLCJzZWxlY3QiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsImRpc2FibGVkIiwib3B0aW9uIiwidGVtcCIsImlucHV0IiwicGxhY2Vob2xkZXIiLCJtaW4iLCJzdGVwIiwiYXV0b0NvbXBsZXRlIiwiaDQiLCJ1bCIsImxpIiwib2wiLCJzdHJvbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});