// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title StakingRewards
 * @dev Staking contract with rewards distribution
 * Generates multiple transactions for Nexus testnet interaction
 */
contract StakingRewards is ReentrancyGuard, Ownable {
    IERC20 public stakingToken;
    IERC20 public rewardsToken;
    
    uint256 public rewardRate = 100; // Rewards per second
    uint256 public lastUpdateTime;
    uint256 public rewardPerTokenStored;
    uint256 public totalStaked;
    
    mapping(address => uint256) public userRewardPerTokenPaid;
    mapping(address => uint256) public rewards;
    mapping(address => uint256) public balances;
    mapping(address => uint256) public stakingTime;
    
    // Multiple staking pools for more transactions
    struct StakingPool {
        uint256 rewardRate;
        uint256 lockPeriod;
        uint256 totalStaked;
        bool active;
    }
    
    mapping(uint256 => StakingPool) public stakingPools;
    mapping(address => mapping(uint256 => uint256)) public userPoolBalances;
    mapping(address => mapping(uint256 => uint256)) public userPoolStakeTime;
    uint256 public poolCount = 0;
    
    event Staked(address indexed user, uint256 amount, uint256 poolId);
    event Withdrawn(address indexed user, uint256 amount, uint256 poolId);
    event RewardPaid(address indexed user, uint256 reward);
    event RewardRateUpdated(uint256 newRate);
    event PoolCreated(uint256 poolId, uint256 rewardRate, uint256 lockPeriod);
    event Compounded(address indexed user, uint256 amount);
    
    constructor(
        address _stakingToken,
        address _rewardsToken
    ) Ownable(msg.sender) {
        stakingToken = IERC20(_stakingToken);
        rewardsToken = IERC20(_rewardsToken);
        lastUpdateTime = block.timestamp;
        
        // Create default pools with different lock periods
        createPool(50, 0);      // Pool 0: No lock, low rewards
        createPool(100, 86400); // Pool 1: 1 day lock, medium rewards
        createPool(200, 604800); // Pool 2: 1 week lock, high rewards
    }
    
    modifier updateReward(address account) {
        rewardPerTokenStored = rewardPerToken();
        lastUpdateTime = block.timestamp;
        if (account != address(0)) {
            rewards[account] = earned(account);
            userRewardPerTokenPaid[account] = rewardPerTokenStored;
        }
        _;
    }
    
    /**
     * @dev Create new staking pool
     * Generates transaction: createPool
     */
    function createPool(uint256 _rewardRate, uint256 _lockPeriod) public onlyOwner {
        stakingPools[poolCount] = StakingPool({
            rewardRate: _rewardRate,
            lockPeriod: _lockPeriod,
            totalStaked: 0,
            active: true
        });
        
        emit PoolCreated(poolCount, _rewardRate, _lockPeriod);
        poolCount++;
    }
    
    /**
     * @dev Stake tokens in specific pool
     * Generates transaction: stake
     */
    function stake(uint256 amount, uint256 poolId) external nonReentrant updateReward(msg.sender) {
        require(amount > 0, "Cannot stake 0");
        require(poolId < poolCount, "Invalid pool");
        require(stakingPools[poolId].active, "Pool not active");
        
        totalStaked += amount;
        balances[msg.sender] += amount;
        stakingPools[poolId].totalStaked += amount;
        userPoolBalances[msg.sender][poolId] += amount;
        userPoolStakeTime[msg.sender][poolId] = block.timestamp;
        stakingTime[msg.sender] = block.timestamp;
        
        require(stakingToken.transferFrom(msg.sender, address(this), amount), "Transfer failed");
        
        emit Staked(msg.sender, amount, poolId);
    }
    
    /**
     * @dev Withdraw staked tokens from specific pool
     * Generates transaction: withdraw
     */
    function withdraw(uint256 amount, uint256 poolId) public nonReentrant updateReward(msg.sender) {
        require(amount > 0, "Cannot withdraw 0");
        require(poolId < poolCount, "Invalid pool");
        require(userPoolBalances[msg.sender][poolId] >= amount, "Insufficient balance");
        
        // Check lock period
        uint256 lockPeriod = stakingPools[poolId].lockPeriod;
        if (lockPeriod > 0) {
            require(
                block.timestamp >= userPoolStakeTime[msg.sender][poolId] + lockPeriod,
                "Tokens still locked"
            );
        }
        
        totalStaked -= amount;
        balances[msg.sender] -= amount;
        stakingPools[poolId].totalStaked -= amount;
        userPoolBalances[msg.sender][poolId] -= amount;
        
        require(stakingToken.transfer(msg.sender, amount), "Transfer failed");
        
        emit Withdrawn(msg.sender, amount, poolId);
    }
    
    /**
     * @dev Claim rewards
     * Generates transaction: getReward
     */
    function getReward() public nonReentrant updateReward(msg.sender) {
        uint256 reward = rewards[msg.sender];
        if (reward > 0) {
            rewards[msg.sender] = 0;
            require(rewardsToken.transfer(msg.sender, reward), "Reward transfer failed");
            emit RewardPaid(msg.sender, reward);
        }
    }
    
    /**
     * @dev Compound rewards (stake rewards)
     * Generates multiple transactions: getReward + stake
     */
    function compound(uint256 poolId) external nonReentrant updateReward(msg.sender) {
        require(poolId < poolCount, "Invalid pool");
        require(stakingPools[poolId].active, "Pool not active");
        
        uint256 reward = rewards[msg.sender];
        require(reward > 0, "No rewards to compound");
        
        // Reset rewards
        rewards[msg.sender] = 0;
        
        // Stake the rewards
        totalStaked += reward;
        balances[msg.sender] += reward;
        stakingPools[poolId].totalStaked += reward;
        userPoolBalances[msg.sender][poolId] += reward;
        userPoolStakeTime[msg.sender][poolId] = block.timestamp;
        
        emit Compounded(msg.sender, reward);
        emit Staked(msg.sender, reward, poolId);
    }
    
    /**
     * @dev Emergency withdraw (forfeit rewards)
     * Generates transaction: emergencyWithdraw
     */
    function emergencyWithdraw(uint256 poolId) external nonReentrant {
        require(poolId < poolCount, "Invalid pool");
        uint256 amount = userPoolBalances[msg.sender][poolId];
        require(amount > 0, "No balance to withdraw");
        
        totalStaked -= amount;
        balances[msg.sender] -= amount;
        stakingPools[poolId].totalStaked -= amount;
        userPoolBalances[msg.sender][poolId] = 0;
        
        // Forfeit rewards
        rewards[msg.sender] = 0;
        
        require(stakingToken.transfer(msg.sender, amount), "Transfer failed");
        
        emit Withdrawn(msg.sender, amount, poolId);
    }
    
    /**
     * @dev Batch operations for multiple transactions
     */
    function batchStake(uint256[] calldata amounts, uint256[] calldata poolIds) external {
        require(amounts.length == poolIds.length, "Array length mismatch");
        
        for (uint256 i = 0; i < amounts.length; i++) {
            stake(amounts[i], poolIds[i]);
        }
    }
    
    function batchWithdraw(uint256[] calldata amounts, uint256[] calldata poolIds) external {
        require(amounts.length == poolIds.length, "Array length mismatch");
        
        for (uint256 i = 0; i < amounts.length; i++) {
            withdraw(amounts[i], poolIds[i]);
        }
    }
    
    // View functions
    function rewardPerToken() public view returns (uint256) {
        if (totalStaked == 0) {
            return rewardPerTokenStored;
        }
        return rewardPerTokenStored + 
            (((block.timestamp - lastUpdateTime) * rewardRate * 1e18) / totalStaked);
    }
    
    function earned(address account) public view returns (uint256) {
        return ((balances[account] * (rewardPerToken() - userRewardPerTokenPaid[account])) / 1e18) + 
            rewards[account];
    }
    
    function getPoolInfo(uint256 poolId) external view returns (
        uint256 rewardRate,
        uint256 lockPeriod,
        uint256 totalStaked,
        bool active
    ) {
        require(poolId < poolCount, "Invalid pool");
        StakingPool memory pool = stakingPools[poolId];
        return (pool.rewardRate, pool.lockPeriod, pool.totalStaked, pool.active);
    }
    
    function getUserPoolInfo(address user, uint256 poolId) external view returns (
        uint256 balance,
        uint256 stakeTime,
        bool canWithdraw
    ) {
        require(poolId < poolCount, "Invalid pool");
        balance = userPoolBalances[user][poolId];
        stakeTime = userPoolStakeTime[user][poolId];
        
        uint256 lockPeriod = stakingPools[poolId].lockPeriod;
        canWithdraw = lockPeriod == 0 || block.timestamp >= stakeTime + lockPeriod;
    }
    
    // Admin functions
    function setRewardRate(uint256 _rewardRate) external onlyOwner updateReward(address(0)) {
        rewardRate = _rewardRate;
        emit RewardRateUpdated(_rewardRate);
    }
    
    function togglePool(uint256 poolId) external onlyOwner {
        require(poolId < poolCount, "Invalid pool");
        stakingPools[poolId].active = !stakingPools[poolId].active;
    }
    
    function addRewards(uint256 amount) external onlyOwner {
        require(rewardsToken.transferFrom(msg.sender, address(this), amount), "Transfer failed");
    }
}
