import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Nexus Swap - DeFi Token Exchange',
  description: 'Decentralized token swap application on Nexus blockchain',
  keywords: ['DeFi', 'Nexus', 'Token Swap', 'Blockchain', 'Cryptocurrency'],
  authors: [{ name: 'Nexus Team' }],
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
          {children}
        </div>
      </body>
    </html>
  )
}
