{"name": "nexus-swap-defi-app", "version": "1.0.0", "description": "Decentralized token swap application on Nexus blockchain", "main": "index.js", "scripts": {"install:all": "npm install && cd contracts && npm install && cd ../frontend && npm install", "clean": "rm -rf node_modules contracts/node_modules frontend/node_modules", "clean:cache": "cd contracts && npm run clean && cd ../frontend && rm -rf .next", "contracts:compile": "cd contracts && npm run compile", "contracts:deploy": "cd contracts && npm run deploy", "contracts:verify": "cd contracts && npm run verify", "contracts:test": "cd contracts && npm run test", "frontend:dev": "cd frontend && npm run dev", "frontend:build": "cd frontend && npm run build", "frontend:start": "cd frontend && npm run start", "dev": "concurrently \"npm run frontend:dev\"", "build": "npm run contracts:compile && npm run frontend:build", "setup": "npm run install:all && cp .env.example .env && cp contracts/.env.example contracts/.env", "deploy:full": "npm run contracts:deploy && npm run frontend:build"}, "keywords": ["defi", "nexus", "blockchain", "token-swap", "ethereum", "smart-contracts", "dapp", "web3"], "author": "Nexus Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/nexus-xyz/nexus-swap-example.git"}, "bugs": {"url": "https://github.com/nexus-xyz/nexus-swap-example/issues"}, "homepage": "https://github.com/nexus-xyz/nexus-swap-example#readme"}