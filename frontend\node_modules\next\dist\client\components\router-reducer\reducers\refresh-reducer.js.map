{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "names": ["refreshReducer", "state", "action", "origin", "mutable", "href", "canonicalUrl", "currentTree", "tree", "preserveCustomHistoryState", "cache", "createEmptyCacheNode", "data", "fetchServerResponse", "URL", "nextUrl", "buildId", "then", "flightData", "canonicalUrlOverride", "handleExternalUrl", "pushRef", "pendingPush", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "cacheNodeSeedData", "head", "slice", "subTreeData", "status", "CacheStates", "READY", "fillLazyItemsTillLeafWithHead", "prefetchCache", "Map", "patchedTree", "handleMutable"], "mappings": ";;;;+BAmBgBA;;;eAAAA;;;qCAnBoB;mCACF;6CACU;6CACA;iCAOV;+BACJ;+CAIvB;+CACuC;2BACT;AAE9B,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/B,IAAIC,cAAcN,MAAMO,IAAI;IAE5BJ,QAAQK,0BAA0B,GAAG;IAErC,MAAMC,QAAmBC,IAAAA,+BAAoB;IAC7C,uDAAuD;IACvD,wCAAwC;IACxCD,MAAME,IAAI,GAAGC,IAAAA,wCAAmB,EAC9B,IAAIC,IAAIT,MAAMF,SACd;QAACI,WAAW,CAAC,EAAE;QAAEA,WAAW,CAAC,EAAE;QAAEA,WAAW,CAAC,EAAE;QAAE;KAAU,EAC3DN,MAAMc,OAAO,EACbd,MAAMe,OAAO;IAGf,OAAON,MAAME,IAAI,CAACK,IAAI,CACpB;YAAC,CAACC,YAAYC,qBAAqB;QACjC,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOE,IAAAA,kCAAiB,EACtBnB,OACAG,SACAc,YACAjB,MAAMoB,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DZ,MAAME,IAAI,GAAG;QAEb,KAAK,MAAMW,kBAAkBL,WAAY;YACvC,oFAAoF;YACpF,IAAIK,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOzB;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAAC0B,UAAU,GAAGJ;YACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJtB,aACAoB;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIE,MAAM;YAClB;YAEA,IAAIC,IAAAA,wDAA2B,EAACxB,aAAaqB,UAAU;gBACrD,OAAOR,IAAAA,kCAAiB,EACtBnB,OACAG,SACAC,MACAJ,MAAMoB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMU,2BAA2Bb,uBAC7Bc,IAAAA,oCAAiB,EAACd,wBAClBe;YAEJ,IAAIf,sBAAsB;gBACxBf,QAAQE,YAAY,GAAG0B;YACzB;YAEA,0DAA0D;YAC1D,MAAM,CAACG,mBAAmBC,KAAK,GAAGb,eAAec,KAAK,CAAC,CAAC;YAExD,8FAA8F;YAC9F,IAAIF,sBAAsB,MAAM;gBAC9B,MAAMG,cAAcH,iBAAiB,CAAC,EAAE;gBACxCzB,MAAM6B,MAAM,GAAGC,0CAAW,CAACC,KAAK;gBAChC/B,MAAM4B,WAAW,GAAGA;gBACpBI,IAAAA,4DAA6B,EAC3BhC,OACA,4FAA4F;gBAC5FwB,WACAP,WACAQ,mBACAC;gBAEFhC,QAAQM,KAAK,GAAGA;gBAChBN,QAAQuC,aAAa,GAAG,IAAIC;YAC9B;YAEAxC,QAAQyC,WAAW,GAAGjB;YACtBxB,QAAQE,YAAY,GAAGD;YAEvBE,cAAcqB;QAChB;QAEA,OAAOkB,IAAAA,4BAAa,EAAC7C,OAAOG;IAC9B,GACA,IAAMH;AAEV"}