// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title TokenSwapComplete
 * @dev Complete secure TokenSwap with all vulnerability fixes
 */
contract TokenSwapComplete is ReentrancyGuard, Ownable, Pausable {
    IERC20 public tokenA;
    IERC20 public tokenB;
    
    // Security limits
    uint256 public constant MAX_SWAP_AMOUNT = 1_000_000 * 10**18; // 1M tokens max per swap
    uint256 public constant MIN_SWAP_AMOUNT = 1 * 10**15; // 0.001 tokens minimum
    uint256 public dailySwapLimit = 10_000_000 * 10**18; // 10M tokens per day
    
    // Daily tracking
    mapping(uint256 => uint256) public dailySwapVolume; // day => volume
    mapping(address => mapping(uint256 => uint256)) public userDailySwaps; // user => day => amount
    uint256 public constant USER_DAILY_LIMIT = 100_000 * 10**18; // 100K per user per day
    
    // Emergency settings
    uint256 public constant EMERGENCY_WITHDRAWAL_DELAY = 24 hours;
    uint256 public emergencyWithdrawalRequestTime;
    bool public emergencyWithdrawalRequested;

    event Swap(address indexed user, uint256 amount, uint256 timestamp);
    event SwapLimitUpdated(uint256 newLimit);
    event TokensWithdrawn(address indexed token, address indexed to, uint256 amount);
    event EmergencyWithdrawalRequested(uint256 timestamp);

    constructor(address _tokenA, address _tokenB) Ownable(msg.sender) {
        require(_tokenA != address(0), "TokenA address cannot be zero");
        require(_tokenB != address(0), "TokenB address cannot be zero");
        require(_tokenA != _tokenB, "TokenA and TokenB must be different");
        tokenA = IERC20(_tokenA);
        tokenB = IERC20(_tokenB);
    }

    /**
     * @dev Secure swap with comprehensive validation and limits
     * @param amount Amount of TokenA to swap
     * @param deadline Transaction deadline for MEV protection
     * @param minAmountOut Minimum amount of TokenB expected (slippage protection)
     */
    function swap(
        uint256 amount, 
        uint256 deadline,
        uint256 minAmountOut
    ) external nonReentrant whenNotPaused returns (bool success) {
        // Time validation
        require(block.timestamp <= deadline, "Transaction expired");
        
        // Amount validation with safe math (Solidity 0.8+ has built-in overflow protection)
        require(amount >= MIN_SWAP_AMOUNT, "Amount below minimum");
        require(amount <= MAX_SWAP_AMOUNT, "Amount exceeds maximum");
        
        // Slippage protection (1:1 ratio, so minAmountOut should equal amount)
        require(minAmountOut <= amount, "Invalid slippage parameters");
        
        // Daily limits
        uint256 today = block.timestamp / 1 days;
        require(
            dailySwapVolume[today] + amount <= dailySwapLimit,
            "Daily swap limit exceeded"
        );
        require(
            userDailySwaps[msg.sender][today] + amount <= USER_DAILY_LIMIT,
            "User daily limit exceeded"
        );
        
        // Liquidity check
        uint256 contractTokenBBalance = tokenB.balanceOf(address(this));
        require(contractTokenBBalance >= amount, "Insufficient Token B liquidity");
        
        // Update daily tracking BEFORE external calls (CEI pattern)
        dailySwapVolume[today] += amount;
        userDailySwaps[msg.sender][today] += amount;
        
        // Emit event before external calls
        emit Swap(msg.sender, amount, block.timestamp);
        
        // External calls last (Checks-Effects-Interactions)
        require(
            tokenA.transferFrom(msg.sender, address(this), amount),
            "TokenA transfer failed"
        );
        require(
            tokenB.transfer(msg.sender, amount),
            "TokenB transfer failed"
        );
        
        return true;
    }

    /**
     * @dev Get swap quote (for frontend)
     */
    function getSwapQuote(uint256 amountIn) external pure returns (uint256 amountOut) {
        return amountIn; // 1:1 ratio
    }

    /**
     * @dev Check if swap is possible
     */
    function canSwap(address user, uint256 amount) external view returns (bool possible, string memory reason) {
        if (paused()) return (false, "Contract paused");
        if (amount < MIN_SWAP_AMOUNT) return (false, "Amount below minimum");
        if (amount > MAX_SWAP_AMOUNT) return (false, "Amount exceeds maximum");
        
        uint256 today = block.timestamp / 1 days;
        if (dailySwapVolume[today] + amount > dailySwapLimit) {
            return (false, "Daily limit exceeded");
        }
        if (userDailySwaps[user][today] + amount > USER_DAILY_LIMIT) {
            return (false, "User daily limit exceeded");
        }
        if (tokenB.balanceOf(address(this)) < amount) {
            return (false, "Insufficient liquidity");
        }
        
        return (true, "");
    }

    /**
     * @dev Admin function to update daily swap limit
     */
    function updateDailySwapLimit(uint256 newLimit) external onlyOwner {
        require(newLimit > 0, "Limit must be greater than 0");
        require(newLimit <= 100_000_000 * 10**18, "Limit too high"); // 100M max
        dailySwapLimit = newLimit;
        emit SwapLimitUpdated(newLimit);
    }

    /**
     * @dev Admin function to withdraw accumulated TokenA
     */
    function withdrawTokenA(uint256 amount) external onlyOwner {
        require(amount > 0, "Amount must be greater than 0");
        uint256 balance = tokenA.balanceOf(address(this));
        require(balance >= amount, "Insufficient TokenA balance");
        
        require(tokenA.transfer(owner(), amount), "TokenA withdrawal failed");
        emit TokensWithdrawn(address(tokenA), owner(), amount);
    }

    /**
     * @dev Emergency pause
     */
    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev Request emergency withdrawal
     */
    function requestEmergencyWithdrawal() external onlyOwner {
        emergencyWithdrawalRequested = true;
        emergencyWithdrawalRequestTime = block.timestamp;
        emit EmergencyWithdrawalRequested(block.timestamp);
    }

    /**
     * @dev Execute emergency withdrawal after delay
     */
    function executeEmergencyWithdrawal(address token) external onlyOwner {
        require(emergencyWithdrawalRequested, "Emergency withdrawal not requested");
        require(
            block.timestamp >= emergencyWithdrawalRequestTime + EMERGENCY_WITHDRAWAL_DELAY,
            "Emergency withdrawal delay not met"
        );

        IERC20 tokenContract = IERC20(token);
        uint256 balance = tokenContract.balanceOf(address(this));
        require(balance > 0, "No tokens to withdraw");

        emergencyWithdrawalRequested = false;
        require(tokenContract.transfer(owner(), balance), "Emergency withdrawal failed");
    }

    // View functions
    function getTokenBLiquidity() external view returns (uint256) {
        return tokenB.balanceOf(address(this));
    }

    function getTokenABalance() external view returns (uint256) {
        return tokenA.balanceOf(address(this));
    }

    function getDailySwapVolume() external view returns (uint256) {
        uint256 today = block.timestamp / 1 days;
        return dailySwapVolume[today];
    }

    function getUserDailySwaps(address user) external view returns (uint256) {
        uint256 today = block.timestamp / 1 days;
        return userDailySwaps[user][today];
    }
}