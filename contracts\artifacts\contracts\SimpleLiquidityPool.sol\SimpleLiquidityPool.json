{"_format": "hh-sol-artifact-1", "contractName": "SimpleLiquidityPool", "sourceName": "contracts/SimpleLiquidityPool.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_tokenA", "type": "address"}, {"internalType": "address", "name": "_tokenB", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "provider", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountA", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountB", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "liquidity", "type": "uint256"}], "name": "LiquidityAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "provider", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountA", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountB", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "liquidity", "type": "uint256"}], "name": "LiquidityRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "address", "name": "tokenIn", "type": "address"}, {"indexed": false, "internalType": "address", "name": "tokenOut", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountOut", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "FEE_DENOMINATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FEE_RATE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amountA", "type": "uint256"}, {"internalType": "uint256", "name": "amountB", "type": "uint256"}], "name": "addLiquidity", "outputs": [{"internalType": "uint256", "name": "liquidity", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}], "name": "getAmountOut", "outputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getReserves", "outputs": [{"internalType": "uint256", "name": "reserveA", "type": "uint256"}, {"internalType": "uint256", "name": "reserveB", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "liquidity", "type": "uint256"}], "name": "removeLiquidity", "outputs": [{"internalType": "uint256", "name": "amountA", "type": "uint256"}, {"internalType": "uint256", "name": "amountB", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountOut", "type": "uint256"}], "name": "swap", "outputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenA", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenB", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}