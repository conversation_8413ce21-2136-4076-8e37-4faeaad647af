{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAalC,CAAC;AAEF,SAAS,eAAe,CAAC,KAAU;IAC/B,OAAO,CAAC,KAAK,IAAI,OAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,UAAU,CAAC,CAAC;AAC7D,CAAC;AAED,SAAS,kBAAkB,CAAC,OAAyB;IACjD,MAAM,IAAI,GAAG,UAAS,SAAc,EAAE,OAAa;QAC/C,IAAI,OAAO,IAAI,IAAI,EAAE;YAAE,OAAO,GAAG,EAAG,CAAC;SAAE;QACvC,MAAM,YAAY,GAAe,EAAE,CAAC;QAEpC,IAAI,SAAS,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,EAAE;YACpD,IAAI;gBACA,YAAY,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;aAC5E;YAAC,OAAM,KAAK,EAAE,GAAG;SACrB;QAED,IAAI,SAAS,CAAC,iBAAiB,IAAI,OAAO,CAAC,SAAS,KAAK,GAAG,EAAE;YAC1D,IAAI;gBACA,YAAY,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;aAClF;YAAC,OAAM,KAAK,EAAE,GAAG;SACrB;QAED,IAAI,SAAS,CAAC,eAAe,IAAI,OAAO,CAAC,OAAO,KAAK,GAAG,EAAE;YACtD,IAAI;gBACA,YAAY,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;aAC9E;YAAC,OAAM,KAAK,EAAE,GAAG;SACrB;QAED,IAAI,SAAS,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,EAAE;YACpD,yDAAyD;YACzD,wDAAwD;YACxD,sBAAsB;YACtB,6DAA6D;YAC7D,MAAM,IAAI,GAAG,CAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAE,CAAC;YAC3D,IAAI;gBACA,MAAM,QAAQ,GAAG,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvE,IAAI,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;oBAChE,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC/B;aACJ;YAAC,OAAM,KAAK,EAAE,GAAG;SACrB;QAED,IAAI,SAAS,CAAC,kBAAkB,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG,EAAE;YAC5D,IAAI;gBACA,YAAY,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;aAChE;YAAC,OAAM,KAAK,EAAE,GAAG;SACrB;QAED,IAAI,SAAS,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,KAAK,GAAG,EAAE;YAChD,IAAI;gBACA,MAAM,IAAI,GAAG,CAAE,SAAS,CAAE,CAAC;gBAC3B,MAAM,QAAQ,GAAG,IAAI,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBACnE,IAAI,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;oBAChE,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC/B;aACJ;YAAC,OAAM,KAAK,EAAE,GAAG;SACrB;QAED,IAAI,SAAS,CAAC,iBAAiB,IAAI,OAAO,CAAC,SAAS,KAAK,GAAG,EAAE;YAC1D,IAAI;gBACA,YAAY,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;aAClF;YAAC,OAAM,KAAK,EAAE,GAAG;SACrB;QAED,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QAE/C,IAAI,SAAS,CAAC,gBAAgB,EAAE;YAC5B,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE;gBACxB,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;aAC3B;iBAAM,IAAI,OAAO,KAAK,WAAW,EAAE;gBAChC,MAAM,GAAG,CAAC,CAAC;aACd;YACD,OAAO,IAAI,SAAS,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;SAC/D;QAED,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAI,CAAC,SAAS,GAAG,UAAS,OAAgB;QACtC,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW,EAAE,OAAyB;IAC9D,MAAM,IAAI,GAAG,UAAS,SAAc,EAAE,OAAa;QAC/C,IAAI,SAAS,CAAC,eAAe,EAAE;YAC3B,OAAO,IAAI,SAAS,CAAC,eAAe,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;SACtD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF,IAAI,CAAC,SAAS,GAAG,UAAS,OAAgB;QACtC,OAAO,kBAAkB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEF,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,MAAM,SAAS,GAAY;IACvB,OAAO,EAAE,CAAC;IACV,UAAU,EAAE,4CAA4C;IACxD,IAAI,EAAE,WAAW;IACjB,gBAAgB,EAAE,kBAAkB,CAAC,WAAW,CAAC;CACpD,CAAC;AAEF,MAAM,OAAO,GAAY;IACrB,OAAO,EAAE,CAAC;IACV,UAAU,EAAE,4CAA4C;IACxD,IAAI,EAAE,SAAS;IACf,gBAAgB,EAAE,kBAAkB,CAAC,SAAS,CAAC;CAClD,CAAC;AAEF,MAAM,aAAa,GAAY;IAC3B,OAAO,EAAE,EAAE;IACX,IAAI,EAAE,eAAe;IACrB,gBAAgB,EAAE,kBAAkB,CAAC,qCAAqC,EAAE,eAAe,CAAC;CAC/F,CAAC;AAEF,6BAA6B;AAC7B,MAAM,QAAQ,GAAgC;IAC1C,WAAW,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE;IAEhD,SAAS,EAAE,SAAS;IACpB,OAAO,EAAE,SAAS;IAElB,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;IAEtC,OAAO,EAAE,OAAO;IAChB,OAAO,EAAE,OAAO;IAEhB,OAAO,EAAE;QACL,OAAO,EAAE,CAAC;QACV,UAAU,EAAE,4CAA4C;QACxD,IAAI,EAAE,SAAS;QACf,gBAAgB,EAAE,kBAAkB,CAAC,SAAS,CAAC;KAClD;IAED,KAAK,EAAE;QACH,OAAO,EAAE,EAAE;QACX,IAAI,EAAE,OAAO;QACb,gBAAgB,EAAE,kBAAkB,CAAC,OAAO,CAAC;KAChD;IAED,MAAM,EAAE;QACJ,OAAO,EAAE,CAAC;QACV,UAAU,EAAE,4CAA4C;QACxD,IAAI,EAAE,QAAQ;QACd,gBAAgB,EAAE,kBAAkB,CAAC,QAAQ,CAAC;KACjD;IAED,QAAQ,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE;IAEhD,OAAO,EAAE;QACL,OAAO,EAAE,QAAQ;QACjB,UAAU,EAAE,4CAA4C;QACxD,IAAI,EAAE,SAAS;QACf,gBAAgB,EAAE,kBAAkB,CAAC,SAAS,CAAC;KAClD;IAED,OAAO,EAAE;QACL,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,SAAS;QACf,gBAAgB,EAAE,kBAAkB,CAAC,SAAS,CAAC;KAClD;IAED,kBAAkB;IAClB,OAAO,EAAE;QACL,OAAO,EAAE,EAAE;QACX,IAAI,EAAE,SAAS;QACf,gBAAgB,EAAE,kBAAkB,CAAC,mCAAmC,EAAE,SAAS,CAAC;KACvF;IAED,aAAa,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE;IAErD,aAAa,EAAE,aAAa;IAC5B,cAAc,EAAE,aAAa;IAE7B,YAAY,EAAE;QACV,OAAO,EAAE,CAAC;QACV,IAAI,EAAE,cAAc;QACpB,gBAAgB,EAAE,kBAAkB,CAAC,qCAAqC,EAAE,cAAc,CAAC;KAC9F;IAED,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE;IAEpC,KAAK,EAAE;QACH,OAAO,EAAE,GAAG;QACZ,IAAI,EAAE,OAAO;QACb,gBAAgB,EAAE,kBAAkB,CAAC,OAAO,CAAC;KAChD;IACD,QAAQ,EAAE;QACN,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,UAAU;QAChB,gBAAgB,EAAE,kBAAkB,CAAC,UAAU,CAAC;KACnD;IAED,QAAQ,EAAE;QACN,OAAO,EAAE,EAAE;QACX,IAAI,EAAE,UAAU;QAChB,gBAAgB,EAAE,kBAAkB,CAAC,UAAU,CAAC;KACnD;IACD,gBAAgB,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;IACzD,iBAAiB,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,iBAAiB,EAAE;IAC5D,kBAAkB,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,kBAAkB,EAAE;IAEnE,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE;IAC9C,kBAAkB,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,kBAAkB,EAAE;IACjE,iBAAiB,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE;IAC/D,kBAAkB,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,kBAAkB,EAAE;IAEjE,GAAG,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;IACjC,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;CACtC,CAAA;AAED;;;;;GAKG;AACH,MAAM,UAAU,UAAU,CAAC,OAAmB;IAC1C,oBAAoB;IACpB,IAAI,OAAO,IAAI,IAAI,EAAE;QAAE,OAAO,IAAI,CAAC;KAAE;IAErC,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE;QAC9B,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;YACzB,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE;gBAC9B,OAAO;oBACH,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,UAAU,EAAE,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC;oBACzC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,gBAAgB,IAAI,IAAI,CAAC;iBACxD,CAAC;aACL;SACJ;QAED,OAAO;YACH,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,SAAS;SAClB,CAAC;KACL;IAED,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE;QAC9B,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,QAAQ,IAAI,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QACtC,OAAO;YACH,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,gBAAgB,EAAE,CAAC,QAAQ,CAAC,gBAAgB,IAAI,IAAI,CAAC;SACxD,CAAC;KACL;IAED,MAAM,QAAQ,GAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAEzC,sEAAsE;IACtE,IAAI,CAAC,QAAQ,EAAE;QACX,IAAI,OAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE;YACtC,MAAM,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;SAC5E;QACD,OAAO,OAAO,CAAC;KAClB;IAED,wFAAwF;IACxF,IAAI,OAAO,CAAC,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,EAAE;QAC/D,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;KAC7E;IAED,+EAA+E;IAC/E,6EAA6E;IAC7E,IAAI,eAAe,GAAwB,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAC5E,IAAI,eAAe,IAAI,IAAI,IAAI,QAAQ,CAAC,gBAAgB,EAAE;QACtD,IAAI,eAAe,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;YAC5C,eAAe,GAAG,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SAClE;aAAM;YACH,eAAe,GAAG,QAAQ,CAAC,gBAAgB,CAAC;SAC/C;KACJ;IAED,sDAAsD;IACtD,OAAO;QACH,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,OAAO,EAAE,QAAQ,CAAC,OAAO;QACzB,UAAU,EAAE,CAAC,OAAO,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC;QAC/D,gBAAgB,EAAE,eAAe;KACpC,CAAC;AACN,CAAC"}