// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title NXY Token
 * @dev Enhanced ERC20 token designed for Nexus ecosystem with NEX integration
 * Features:
 * - Burnable tokens for deflationary mechanics
 * - Pausable for emergency stops
 * - Owner controls for advanced features
 * - NEX token interaction capabilities
 * - Reward mechanism for staking
 * - 18 decimals (standard)
 * - Initial supply of 1,000,000 tokens
 * - Anti-whale mechanisms
 */
contract NXY is ERC20, ERC20Burnable, Ownable, Pausable, ReentrancyGuard {
    uint256 public constant INITIAL_SUPPLY = 1_000_000 * 10**18; // 1M tokens
    uint256 public constant MAX_SUPPLY = 10_000_000 * 10**18;    // 10M tokens max

    // NEX integration
    uint256 public nexSwapRate = 150; // 1 NEX = 150 NXY (higher rate than NXI)
    bool public nexSwapEnabled = true;
    
    // Anti-whale mechanism
    uint256 public maxTransferAmount = 75_000 * 10**18; // 75k tokens max per transfer
    mapping(address => bool) public whitelistedAddresses;

    // Reward mechanism
    mapping(address => bool) public rewardMinters;
    uint256 public totalRewardsDistributed;
    
    // NEX reward pool
    uint256 public nexRewardPool;
    mapping(address => uint256) public nexRewardClaims;

    // Events
    event TokensMinted(address indexed to, uint256 amount);
    event RewardsMinted(address indexed to, uint256 amount);
    event RewardMinterAdded(address indexed minter);
    event RewardMinterRemoved(address indexed minter);
    event ContractPaused(address indexed by);
    event ContractUnpaused(address indexed by);
    event NEXSwapped(address indexed user, uint256 nexAmount, uint256 nxyAmount);
    event NEXSwapRateUpdated(uint256 oldRate, uint256 newRate);
    event MaxTransferUpdated(uint256 oldMax, uint256 newMax);
    event AddressWhitelisted(address indexed account, bool whitelisted);
    event NEXRewardClaimed(address indexed user, uint256 amount);
    event NEXRewardPoolFunded(uint256 amount);

    /**
     * @dev Constructor that mints initial supply to the deployer
     * Sets up the token with enhanced features for Nexus DeFi operations
     */
    constructor() ERC20("Nexus Yield Token", "NXY") Ownable(msg.sender) {
        _mint(msg.sender, INITIAL_SUPPLY);
        
        // Whitelist deployer and common DeFi contracts
        whitelistedAddresses[msg.sender] = true;
        whitelistedAddresses[address(0)] = true; // For burns
        
        emit TokensMinted(msg.sender, INITIAL_SUPPLY);
    }

    /**
     * @dev Swap NEX for NXY tokens
     * Users can send NEX and receive NXY at the current swap rate
     */
    function swapNEXForNXY() external payable nonReentrant whenNotPaused {
        require(nexSwapEnabled, "NEX swap is disabled");
        require(msg.value > 0, "Must send NEX to swap");
        
        uint256 nxyAmount = msg.value * nexSwapRate;
        require(totalSupply() + nxyAmount <= MAX_SUPPLY, "Would exceed max supply");
        
        _mint(msg.sender, nxyAmount);
        
        // Add 10% of NEX to reward pool
        uint256 rewardPoolContribution = msg.value / 10;
        nexRewardPool += rewardPoolContribution;
        
        emit NEXSwapped(msg.sender, msg.value, nxyAmount);
        emit TokensMinted(msg.sender, nxyAmount);
        emit NEXRewardPoolFunded(rewardPoolContribution);
    }

    /**
     * @dev Mint new tokens (only owner, respects max supply)
     * @param to Address to mint tokens to
     * @param amount Amount of tokens to mint
     */
    function mint(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Cannot mint to zero address");
        require(totalSupply() + amount <= MAX_SUPPLY, "Would exceed max supply");

        _mint(to, amount);
        emit TokensMinted(to, amount);
    }

    /**
     * @dev Mint rewards (only authorized reward minters like staking contracts)
     * @param to Address to mint rewards to
     * @param amount Amount of reward tokens to mint
     */
    function mintRewards(address to, uint256 amount) external {
        require(rewardMinters[msg.sender], "Not authorized to mint rewards");
        require(to != address(0), "Cannot mint to zero address");
        require(totalSupply() + amount <= MAX_SUPPLY, "Would exceed max supply");

        _mint(to, amount);
        totalRewardsDistributed += amount;
        emit RewardsMinted(to, amount);
    }

    /**
     * @dev Claim NEX rewards based on NXY holdings
     * Rewards are proportional to NXY balance
     */
    function claimNEXRewards() external nonReentrant whenNotPaused {
        require(balanceOf(msg.sender) > 0, "Must hold NXY tokens to claim rewards");
        require(nexRewardPool > 0, "No NEX rewards available");
        
        uint256 userBalance = balanceOf(msg.sender);
        uint256 totalSupplyAmount = totalSupply();
        uint256 userShare = (nexRewardPool * userBalance) / totalSupplyAmount;
        
        // Minimum claim threshold
        require(userShare >= 0.001 ether, "Reward amount too small");
        
        // Prevent double claiming in same block
        require(nexRewardClaims[msg.sender] < block.number, "Already claimed this block");
        
        nexRewardClaims[msg.sender] = block.number;
        nexRewardPool -= userShare;
        
        payable(msg.sender).transfer(userShare);
        
        emit NEXRewardClaimed(msg.sender, userShare);
    }

    /**
     * @dev Add authorized reward minter (typically staking contract)
     * @param minter Address to authorize for reward minting
     */
    function addRewardMinter(address minter) external onlyOwner {
        require(minter != address(0), "Cannot add zero address as minter");
        rewardMinters[minter] = true;
        emit RewardMinterAdded(minter);
    }

    /**
     * @dev Remove authorized reward minter
     * @param minter Address to remove from reward minting
     */
    function removeRewardMinter(address minter) external onlyOwner {
        rewardMinters[minter] = false;
        emit RewardMinterRemoved(minter);
    }

    /**
     * @dev Set NEX to NXY swap rate (only owner)
     * @param newRate New swap rate (1 NEX = newRate NXY)
     */
    function setNEXSwapRate(uint256 newRate) external onlyOwner {
        require(newRate > 0, "Swap rate must be positive");
        uint256 oldRate = nexSwapRate;
        nexSwapRate = newRate;
        emit NEXSwapRateUpdated(oldRate, newRate);
    }

    /**
     * @dev Enable/disable NEX swapping (only owner)
     * @param enabled Whether NEX swapping should be enabled
     */
    function setNEXSwapEnabled(bool enabled) external onlyOwner {
        nexSwapEnabled = enabled;
    }

    /**
     * @dev Set maximum transfer amount (only owner)
     * @param newMax New maximum transfer amount
     */
    function setMaxTransferAmount(uint256 newMax) external onlyOwner {
        uint256 oldMax = maxTransferAmount;
        maxTransferAmount = newMax;
        emit MaxTransferUpdated(oldMax, newMax);
    }

    /**
     * @dev Whitelist address to bypass transfer limits (only owner)
     * @param account Address to whitelist
     * @param whitelisted Whether address should be whitelisted
     */
    function setWhitelistAddress(address account, bool whitelisted) external onlyOwner {
        whitelistedAddresses[account] = whitelisted;
        emit AddressWhitelisted(account, whitelisted);
    }

    /**
     * @dev Fund NEX reward pool (only owner)
     */
    function fundNEXRewardPool() external payable onlyOwner {
        require(msg.value > 0, "Must send NEX to fund pool");
        nexRewardPool += msg.value;
        emit NEXRewardPoolFunded(msg.value);
    }

    /**
     * @dev Pause all token transfers (emergency function)
     */
    function pause() external onlyOwner {
        _pause();
        emit ContractPaused(msg.sender);
    }

    /**
     * @dev Unpause all token transfers
     */
    function unpause() external onlyOwner {
        _unpause();
        emit ContractUnpaused(msg.sender);
    }

    /**
     * @dev Withdraw NEX from contract (only owner, excluding reward pool)
     * @param to Address to send NEX to
     * @param amount Amount of NEX to withdraw
     */
    function withdrawNEX(address payable to, uint256 amount) external onlyOwner {
        require(to != address(0), "Cannot withdraw to zero address");
        uint256 availableBalance = address(this).balance - nexRewardPool;
        require(amount <= availableBalance, "Insufficient NEX balance (excluding reward pool)");
        
        to.transfer(amount);
    }

    /**
     * @dev Override transfer to respect pause state and anti-whale limits
     */
    function _update(address from, address to, uint256 value) internal override whenNotPaused {
        // Check anti-whale limits (skip for whitelisted addresses)
        if (!whitelistedAddresses[from] && !whitelistedAddresses[to] && from != address(0)) {
            require(value <= maxTransferAmount, "Transfer amount exceeds maximum");
        }
        
        super._update(from, to, value);
    }

    /**
     * @dev Returns comprehensive token information for frontend integration
     */
    function getTokenInfo() external view returns (
        string memory tokenName,
        string memory tokenSymbol,
        uint8 tokenDecimals,
        uint256 currentSupply,
        uint256 maxSupply,
        uint256 totalRewards,
        uint256 nexRate,
        bool nexSwapActive,
        uint256 maxTransfer,
        bool isPaused,
        uint256 nexBalance,
        uint256 rewardPool
    ) {
        return (
            name(),
            symbol(),
            decimals(),
            totalSupply(),
            MAX_SUPPLY,
            totalRewardsDistributed,
            nexSwapRate,
            nexSwapEnabled,
            maxTransferAmount,
            paused(),
            address(this).balance,
            nexRewardPool
        );
    }

    /**
     * @dev Calculate NXY amount for given NEX amount
     * @param nexAmount Amount of NEX to swap
     * @return Amount of NXY that would be received
     */
    function calculateNXYForNEX(uint256 nexAmount) external view returns (uint256) {
        return nexAmount * nexSwapRate;
    }

    /**
     * @dev Calculate potential NEX rewards for user
     * @param user Address to calculate rewards for
     * @return Amount of NEX rewards user can claim
     */
    function calculateNEXRewards(address user) external view returns (uint256) {
        if (balanceOf(user) == 0 || nexRewardPool == 0) return 0;
        
        uint256 userBalance = balanceOf(user);
        uint256 totalSupplyAmount = totalSupply();
        return (nexRewardPool * userBalance) / totalSupplyAmount;
    }

    /**
     * @dev Check if address is authorized reward minter
     * @param minter Address to check
     * @return True if authorized to mint rewards
     */
    function isRewardMinter(address minter) external view returns (bool) {
        return rewardMinters[minter];
    }

    /**
     * @dev Check if address is whitelisted
     * @param account Address to check
     * @return True if address is whitelisted
     */
    function isWhitelisted(address account) external view returns (bool) {
        return whitelistedAddresses[account];
    }

    /**
     * @dev Receive function to handle direct NEX transfers
     */
    receive() external payable {
        if (msg.value > 0 && nexSwapEnabled && !paused()) {
            uint256 nxyAmount = msg.value * nexSwapRate;
            if (totalSupply() + nxyAmount <= MAX_SUPPLY) {
                _mint(msg.sender, nxyAmount);
                
                // Add 10% to reward pool
                uint256 rewardPoolContribution = msg.value / 10;
                nexRewardPool += rewardPoolContribution;
                
                emit NEXSwapped(msg.sender, msg.value, nxyAmount);
                emit TokensMinted(msg.sender, nxyAmount);
                emit NEXRewardPoolFunded(rewardPoolContribution);
            }
        }
    }
}