{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;AAEb,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,EAAE,QAAQ,EAAsB,MAAM,kCAAkC,CAAC;AAChF,OAAO,EAA0B,MAAM,EAAoD,MAAM,gCAAgC,CAAC;AAClI,OAAO,EAAE,QAAQ,EAAoB,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,EAAiB,MAAM,sBAAsB,CAAC;AACnI,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACrE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,iBAAiB,EAAY,MAAM,uBAAuB,CAAC;AACzF,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9E,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,4BAA4B,CAAC;AACxD,OAAO,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,eAAe,EAAoB,MAAM,6BAA6B,CAAC;AAC1H,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAuB,MAAM,6BAA6B,CAAC;AAG7G,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,SAAS,SAAS,CAAC,KAAU;IACzB,OAAO,CAAC,KAAK,IAAI,IAAI,IAAI,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC;AACzF,CAAC;AAED,SAAS,WAAW,CAAC,KAAU;IAC3B,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IAChC,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzC,CAAC;AAED,MAAM,OAAO,MAAO,SAAQ,MAAM;IAU9B,YAAY,UAA2D,EAAE,QAAmB;QACxF,KAAK,EAAE,CAAC;QAER,IAAI,SAAS,CAAC,UAAU,CAAC,EAAE;YACvB,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACzD,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,CAAC;YACtD,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAEhE,IAAI,IAAI,CAAC,OAAO,KAAK,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBACjD,MAAM,CAAC,kBAAkB,CAAC,6BAA6B,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;aACxF;YAED,IAAI,WAAW,CAAC,UAAU,CAAC,EAAE;gBACzB,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC;gBACxC,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CACpC;oBACI,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,IAAI,EAAE,WAAW,CAAC,IAAI,IAAI,WAAW;oBACrC,MAAM,EAAE,WAAW,CAAC,MAAM,IAAI,IAAI;iBACrC,CACJ,CAAC,CAAC;gBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACnG,IAAI,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,OAAO,EAAE;oBAClD,MAAM,CAAC,kBAAkB,CAAC,2BAA2B,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;iBACtF;aACJ;iBAAM;gBACH,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,GAAa,EAAE,CAAC,IAAI,CAAC,CAAC;aAC3D;SAGJ;aAAM;YACH,IAAI,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;gBACrC,wBAAwB;gBACxB,IAAI,UAAU,CAAC,KAAK,KAAK,WAAW,EAAE;oBAClC,MAAM,CAAC,kBAAkB,CAAC,sCAAsC,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;iBACjG;gBACD,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE,GAAG,EAAE,CAAc,UAAW,CAAC,CAAC;aAEvE;iBAAM;gBACH,0EAA0E;gBAC1E,IAAI,OAAM,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE;oBACjC,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;wBAC9D,UAAU,GAAG,IAAI,GAAG,UAAU,CAAC;qBAClC;iBACJ;gBAED,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;gBAC9C,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,CAAC;aACzD;YAED,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,GAAa,EAAE,CAAC,IAAI,CAAC,CAAC;YACxD,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;SACnE;QAED,wBAAwB;QACxB,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC5C,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;SACvE;QAED,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,IAAI,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,QAAQ,KAAe,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IACrD,IAAI,UAAU,KAAa,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAClE,IAAI,SAAS,KAAa,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;IAEhE,UAAU;QACN,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAED,OAAO,CAAC,QAAkB;QACtB,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,eAAe,CAAC,WAA+B;QAC3C,OAAO,iBAAiB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE;YAC9C,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE;gBACjB,IAAI,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,EAAE;oBACtC,MAAM,CAAC,kBAAkB,CAAC,mCAAmC,EAAE,kBAAkB,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;iBACxG;gBACD,OAAO,EAAE,CAAC,IAAI,CAAC;aAClB;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,CAAsB,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/F,OAAO,SAAS,CAAsB,EAAE,EAAE,SAAS,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACP,CAAC;IAEK,WAAW,CAAC,OAAuB;;YACrC,OAAO,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9E,CAAC;KAAA;IAEK,cAAc,CAAC,MAAuB,EAAE,KAA4C,EAAE,KAA0B;;YAClH,yBAAyB;YACzB,MAAM,SAAS,GAAG,MAAM,iBAAiB,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAY,EAAE,EAAE;gBAC1F,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;oBACvB,MAAM,CAAC,UAAU,CAAC,6CAA6C,EAAE,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;wBAClG,SAAS,EAAE,aAAa;wBACxB,KAAK,EAAE,IAAI;qBACd,CAAC,CAAC;iBACN;gBACD,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;YAEH,OAAO,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC1H,CAAC;KAAA;IAED,OAAO,CAAC,QAAwB,EAAE,OAAa,EAAE,gBAAmC;QAChF,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,UAAU,IAAI,CAAC,gBAAgB,EAAE;YACrD,gBAAgB,GAAG,OAAO,CAAC;YAC3B,OAAO,GAAG,EAAE,CAAC;SAChB;QAED,IAAI,gBAAgB,IAAI,OAAM,CAAC,gBAAgB,CAAC,KAAK,UAAU,EAAE;YAC7D,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,OAAO,EAAE;YAAE,OAAO,GAAG,EAAE,CAAC;SAAE;QAE/B,OAAO,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;IACtE,CAAC;IAGD;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,OAAa;QAC7B,IAAI,OAAO,GAAe,WAAW,CAAC,EAAE,CAAC,CAAC;QAE1C,IAAI,CAAC,OAAO,EAAE;YAAE,OAAO,GAAG,EAAG,CAAC;SAAE;QAEhC,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,OAAO,EAAE,OAAO,CAAC,YAAY,CAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SACjG;QAED,MAAM,QAAQ,GAAG,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5D,OAAO,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,IAAY,EAAE,QAAwB,EAAE,gBAAmC;QAChG,OAAO,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YACxE,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACP,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,IAAY,EAAE,QAAwB;QAC/D,OAAO,IAAI,MAAM,CAAC,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,QAAgB,EAAE,IAAa,EAAE,QAAmB;QACpE,IAAI,CAAC,IAAI,EAAE;YAAE,IAAI,GAAG,WAAW,CAAC;SAAE;QAClC,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IACtF,CAAC;CACJ;AAED,MAAM,UAAU,aAAa,CAAC,OAAuB,EAAE,SAAwB;IAC3E,OAAO,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC;AAC3D,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,MAAuB,EAAE,KAA4C,EAAE,KAA0B,EAAE,SAAwB;IACvJ,OAAO,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;AACnF,CAAC"}